# Wazuh REST API Integration Guide

## Overview

This document provides comprehensive technical specifications for integrating Wazuh SIEM/XDR REST API capabilities into the GRCOS Monitor module. The integration enables real-time security monitoring, threat detection, and automated incident response within GRCOS's blockchain-secured compliance management framework.

## Wazuh API Base Configuration

### Base URL Structure

```
https://your-wazuh-manager:55000/
```

### API Versioning

Wazuh API uses versioning in the URL path:
- **Current Version**: `/v1/` (recommended)
- **Legacy Support**: Available for backward compatibility

**Complete Base URL:**
```
https://your-wazuh-manager:55000/v1/
```

## Authentication & Connection Management

### Authentication Methods

Wazuh API supports multiple authentication methods for GRCOS integration:

1. **JWT Token Authentication** (Recommended for GRCOS)
2. **Basic Authentication** (Development/Testing)
3. **API Key Authentication** (Service-to-Service)

### JWT Token Authentication (Recommended)

**Endpoint:** `POST /security/user/authenticate`

**Request:**
```json
{
  "user": "wazuh-admin",
  "password": "your_secure_password"
}
```

**Response:**
```json
{
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "policies": {
      "rbac_mode": "white",
      "rbac_policies": ["policy1", "policy2"]
    }
  },
  "message": "User authenticated successfully",
  "error": 0
}
```

### Token Management for GRCOS Integration

**Token Refresh Strategy:**
```javascript
class WazuhTokenManager {
  constructor(baseURL, credentials) {
    this.baseURL = baseURL;
    this.credentials = credentials;
    this.token = null;
    this.tokenExpiry = null;
    this.refreshBuffer = 300; // 5 minutes before expiry
  }

  async getValidToken() {
    if (!this.token || this.isTokenExpiring()) {
      await this.refreshToken();
    }
    return this.token;
  }

  async refreshToken() {
    const response = await fetch(`${this.baseURL}/security/user/authenticate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(this.credentials)
    });
    
    const data = await response.json();
    this.token = data.data.token;
    this.tokenExpiry = Date.now() + (15 * 60 * 1000); // 15 minutes
  }

  isTokenExpiring() {
    return Date.now() > (this.tokenExpiry - this.refreshBuffer * 1000);
  }
}
```

### Required Headers for GRCOS Integration

For all authenticated requests, include:

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
X-GRCOS-Request-ID: <unique_request_id>
X-GRCOS-Module: monitor
```

### Rate Limiting and Connection Best Practices

**Rate Limits:**
- **Default**: 300 requests per minute per IP
- **Burst**: Up to 50 requests in 10 seconds
- **Concurrent**: Maximum 10 concurrent connections

**Connection Pool Configuration:**
```python
import aiohttp
import asyncio

class WazuhConnectionPool:
    def __init__(self, base_url, max_connections=10):
        self.base_url = base_url
        self.connector = aiohttp.TCPConnector(
            limit=max_connections,
            limit_per_host=max_connections,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        self.session = aiohttp.ClientSession(
            connector=self.connector,
            timeout=aiohttp.ClientTimeout(total=30)
        )
    
    async def request(self, method, endpoint, **kwargs):
        url = f"{self.base_url}{endpoint}"
        async with self.session.request(method, url, **kwargs) as response:
            return await response.json()
    
    async def close(self):
        await self.session.close()
```

## Core Data Retrieval Endpoints

### Agent Management API

#### Get All Agents
- **URL:** `GET /agents`
- **Description:** Retrieve all Wazuh agents with status and configuration information
- **OSCAL Mapping:** Maps to OSCAL Component Definition for asset inventory

**Example Request:**
```http
GET /v1/agents?limit=500&offset=0&sort=+id&search=status=active
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "id": "001",
        "name": "web-server-01",
        "ip": "*************",
        "status": "active",
        "os": {
          "arch": "x86_64",
          "major": "20",
          "minor": "04",
          "name": "Ubuntu",
          "platform": "ubuntu",
          "version": "20.04.3 LTS"
        },
        "version": "4.7.0",
        "manager": "wazuh-manager",
        "node_name": "worker-01",
        "dateAdd": "2024-01-15T10:30:00.000Z",
        "lastKeepAlive": "2024-01-15T15:45:30.000Z",
        "group": ["default", "web-servers"],
        "configSum": "ab73af41699f13fdd81903b5f23d8d00",
        "mergedSum": "f69c8a56d2cd8b401643a52cf423ad1c"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "All selected agents information was returned",
  "error": 0
}
```

#### Get Agent Configuration
- **URL:** `GET /agents/{agent_id}/config/{component}`
- **Description:** Retrieve agent configuration for specific components
- **Components:** `agent`, `agentless`, `analysis`, `auth`, `com`, `csyslog`, `integrator`, `logcollector`, `mail`, `monitor`, `request`, `syscheck`, `wmodules`

**Example Request:**
```http
GET /v1/agents/001/config/syscheck
```

**Example Response:**
```json
{
  "data": {
    "syscheck": {
      "disabled": "no",
      "frequency": 43200,
      "scan_on_start": "yes",
      "auto_ignore": "no",
      "directories": [
        {
          "path": "/etc",
          "check_all": "yes",
          "report_changes": "yes",
          "realtime": "no"
        },
        {
          "path": "/usr/bin",
          "check_all": "yes",
          "report_changes": "yes",
          "realtime": "no"
        }
      ],
      "ignore": [
        "/etc/mtab",
        "/etc/hosts.deny",
        "/etc/mail/statistics"
      ]
    }
  },
  "message": "Agent configuration was successfully read",
  "error": 0
}
```

### Security Alerts and Events API

#### Get Security Alerts
- **URL:** `GET /security/events`
- **Description:** Retrieve security alerts and events for real-time monitoring
- **OSCAL Mapping:** Maps to OSCAL Assessment Results for continuous monitoring
- **GRCOS Integration:** Primary endpoint for Monitor module event ingestion

**Query Parameters:**
- `limit`: Maximum number of alerts to return (default: 500, max: 10000)
- `offset`: Number of alerts to skip for pagination
- `sort`: Sort field (+/-field_name for ascending/descending)
- `search`: Search query using Wazuh query syntax
- `date_range`: Time range for alerts (e.g., "1d", "7d", "30d")
- `rule.level`: Filter by alert severity level (1-15)
- `agent.id`: Filter by specific agent ID
- `rule.groups`: Filter by rule groups

**Example Request:**
```http
GET /v1/security/events?limit=100&offset=0&sort=-timestamp&search=rule.level>=7&date_range=1d
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "timestamp": "2024-01-15T15:45:30.123Z",
        "rule": {
          "id": 5712,
          "level": 10,
          "description": "Multiple authentication failures",
          "groups": ["authentication_failed", "authentication_failures"],
          "firedtimes": 1,
          "mail": false,
          "pci_dss": ["10.2.4", "10.2.5"],
          "gdpr": ["IV_35.7.d", "IV_32.2"],
          "hipaa": ["164.312.b"],
          "nist_800_53": ["AU.14", "AC.7"],
          "tsc": ["CC6.1", "CC6.8", "CC7.2", "CC7.3"]
        },
        "agent": {
          "id": "001",
          "name": "web-server-01",
          "ip": "*************"
        },
        "manager": {
          "name": "wazuh-manager"
        },
        "id": "1642263930.123456",
        "cluster": {
          "name": "wazuh-cluster",
          "node": "worker-01"
        },
        "full_log": "Jan 15 15:45:30 web-server-01 sshd[12345]: Failed password for invalid user admin from ************* port 22 ssh2",
        "decoder": {
          "name": "sshd",
          "parent": "sshd"
        },
        "data": {
          "srcip": "*************",
          "srcport": "22",
          "dstuser": "admin",
          "protocol": "ssh"
        },
        "location": "/var/log/auth.log"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Security events information was returned",
  "error": 0
}
```

#### Get Real-time Events Stream
- **URL:** `GET /security/events/stream` (Server-Sent Events)
- **Description:** Real-time streaming of security events for immediate response
- **GRCOS Integration:** Used by Monitor module for real-time threat detection

**Example Request:**
```http
GET /v1/security/events/stream?rule.level>=5&agent.id=001
Accept: text/event-stream
```

**Example Response (SSE Format):**
```
data: {"timestamp":"2024-01-15T15:45:30.123Z","rule":{"id":5712,"level":10},"agent":{"id":"001"}}

data: {"timestamp":"2024-01-15T15:46:15.456Z","rule":{"id":1002,"level":2},"agent":{"id":"002"}}
```

### Vulnerability Detection API

#### Get Vulnerability Scan Results
- **URL:** `GET /vulnerability/{agent_id}`
- **Description:** Retrieve vulnerability assessment results for specific agents
- **OSCAL Mapping:** Maps to OSCAL Assessment Results for vulnerability management
- **GRCOS Integration:** Used for risk assessment and compliance reporting

**Query Parameters:**
- `limit`: Maximum number of vulnerabilities to return
- `offset`: Number of vulnerabilities to skip for pagination
- `sort`: Sort field (+/-field_name)
- `search`: Search query for vulnerability filtering
- `severity`: Filter by severity (Critical, High, Medium, Low)
- `status`: Filter by status (Pending, Fixed, Ignored)

**Example Request:**
```http
GET /v1/vulnerability/001?limit=100&sort=-severity&search=severity=Critical
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "agent": {
          "id": "001",
          "name": "web-server-01",
          "ip": "*************"
        },
        "cve": "CVE-2024-1234",
        "title": "Critical Remote Code Execution Vulnerability",
        "severity": "Critical",
        "score": {
          "base": 9.8,
          "temporal": 9.5,
          "environmental": 9.2
        },
        "package": {
          "name": "openssl",
          "version": "1.1.1f-1ubuntu2.16",
          "architecture": "amd64",
          "condition": "Package less than 1.1.1f-1ubuntu2.17"
        },
        "published": "2024-01-10T00:00:00.000Z",
        "updated": "2024-01-12T00:00:00.000Z",
        "references": [
          "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-1234",
          "https://ubuntu.com/security/CVE-2024-1234"
        ],
        "description": "A critical vulnerability in OpenSSL allows remote code execution...",
        "detection_time": "2024-01-15T10:30:00.000Z",
        "status": "Pending",
        "external_references": [
          {
            "type": "advisory",
            "url": "https://www.openssl.org/news/secadv/20240110.txt"
          }
        ]
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Vulnerability information was returned",
  "error": 0
}
```

### File Integrity Monitoring (FIM) API

#### Get FIM Events
- **URL:** `GET /syscheck/{agent_id}`
- **Description:** Retrieve file integrity monitoring events and changes
- **OSCAL Mapping:** Maps to OSCAL Assessment Results for system integrity monitoring
- **GRCOS Integration:** Used for change management and compliance auditing

**Query Parameters:**
- `limit`: Maximum number of FIM events to return
- `offset`: Number of events to skip for pagination
- `sort`: Sort field (+/-field_name)
- `search`: Search query for FIM event filtering
- `type`: Filter by event type (added, modified, deleted)
- `file`: Filter by specific file path

**Example Request:**
```http
GET /v1/syscheck/001?limit=100&sort=-date&search=type=modified
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "agent": {
          "id": "001",
          "name": "web-server-01",
          "ip": "*************"
        },
        "file": "/etc/passwd",
        "event": "modified",
        "date": "2024-01-15T15:45:30.123Z",
        "changes": 1,
        "size_before": 2847,
        "size_after": 2891,
        "perm_before": "rw-r--r--",
        "perm_after": "rw-r--r--",
        "uid_before": "0",
        "uid_after": "0",
        "gid_before": "0",
        "gid_after": "0",
        "md5_before": "d41d8cd98f00b204e9800998ecf8427e",
        "md5_after": "098f6bcd4621d373cade4e832627b4f6",
        "sha1_before": "da39a3ee5e6b4b0d3255bfef95601890afd80709",
        "sha1_after": "356a192b7913b04c54574d18c28d46e6395428ab",
        "sha256_before": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
        "sha256_after": "6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b",
        "attributes": ["checksum", "size", "owner", "group", "permission"],
        "content_changes": "+user:x:1001:1001:New User:/home/<USER>/bin/bash"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Syscheck information was returned",
  "error": 0
}
```

### Rules and Decoders API

#### Get Security Rules
- **URL:** `GET /rules`
- **Description:** Retrieve Wazuh security rules and their configurations
- **OSCAL Mapping:** Maps to OSCAL Control Implementation for security controls
- **GRCOS Integration:** Used for policy mapping and compliance validation

**Query Parameters:**
- `limit`: Maximum number of rules to return
- `offset`: Number of rules to skip for pagination
- `sort`: Sort field (+/-field_name)
- `search`: Search query for rule filtering
- `level`: Filter by rule level (1-15)
- `group`: Filter by rule group
- `pci_dss`: Filter by PCI DSS requirement
- `gdpr`: Filter by GDPR requirement
- `nist_800_53`: Filter by NIST 800-53 control

**Example Request:**
```http
GET /v1/rules?limit=100&sort=-level&search=nist_800_53=AU.14
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "id": 5712,
        "level": 10,
        "status": "enabled",
        "description": "Multiple authentication failures",
        "groups": ["authentication_failed", "authentication_failures"],
        "pci_dss": ["10.2.4", "10.2.5"],
        "gdpr": ["IV_35.7.d", "IV_32.2"],
        "hipaa": ["164.312.b"],
        "nist_800_53": ["AU.14", "AC.7"],
        "tsc": ["CC6.1", "CC6.8", "CC7.2", "CC7.3"],
        "mitre": {
          "tactic": ["Credential Access"],
          "id": ["T1110"],
          "technique": ["Brute Force"]
        },
        "details": {
          "frequency": 8,
          "timeframe": 240,
          "if_matched_sid": 5710,
          "same_source_ip": true
        },
        "filename": "/var/ossec/ruleset/rules/0095-sshd_rules.xml",
        "relative_dirname": "ruleset/rules",
        "file": "0095-sshd_rules.xml"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Rules information was returned",
  "error": 0
}
```

### System Inventory and Asset Discovery API

#### Get System Inventory
- **URL:** `GET /syscollector/{agent_id}`
- **Description:** Retrieve comprehensive system inventory for agents
- **OSCAL Mapping:** Maps to OSCAL Component Definition for asset inventory
- **GRCOS Integration:** Primary source for asset discovery and inventory management

**Inventory Types:**
- `hardware`: Hardware information (CPU, memory, storage)
- `os`: Operating system details
- `network`: Network interfaces and configuration
- `packages`: Installed software packages
- `processes`: Running processes
- `ports`: Open network ports

**Example Request:**
```http
GET /v1/syscollector/001/hardware
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "agent_id": "001",
        "board_serial": "0",
        "cpu_name": "Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz",
        "cpu_cores": 6,
        "cpu_mhz": 3700.0,
        "ram_total": 16777216,
        "ram_free": 8388608,
        "ram_usage": 50,
        "scan_time": "2024-01-15T10:30:00.000Z"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Hardware information was returned",
  "error": 0
}
```

## Data Structures & Schemas

### Wazuh Security Event Schema

```json
{
  "timestamp": "2024-01-15T15:45:30.123Z",
  "rule": {
    "id": 5712,
    "level": 10,
    "description": "Multiple authentication failures",
    "groups": ["authentication_failed", "authentication_failures"],
    "firedtimes": 1,
    "mail": false,
    "pci_dss": ["10.2.4", "10.2.5"],
    "gdpr": ["IV_35.7.d", "IV_32.2"],
    "hipaa": ["164.312.b"],
    "nist_800_53": ["AU.14", "AC.7"],
    "tsc": ["CC6.1", "CC6.8", "CC7.2", "CC7.3"],
    "mitre": {
      "tactic": ["Credential Access"],
      "id": ["T1110"],
      "technique": ["Brute Force"]
    }
  },
  "agent": {
    "id": "001",
    "name": "web-server-01",
    "ip": "*************",
    "groups": ["default", "web-servers"]
  },
  "manager": {
    "name": "wazuh-manager"
  },
  "id": "1642263930.123456",
  "cluster": {
    "name": "wazuh-cluster",
    "node": "worker-01"
  },
  "full_log": "Original log message",
  "decoder": {
    "name": "sshd",
    "parent": "sshd"
  },
  "data": {
    "srcip": "*************",
    "srcport": "22",
    "dstuser": "admin",
    "protocol": "ssh"
  },
  "location": "/var/log/auth.log",
  "previous_output": "Previous related log message"
}
```

**Field Descriptions:**
- `timestamp` (string): ISO 8601 timestamp of the event
- `rule` (object): Security rule that triggered the alert
  - `id` (integer): Unique rule identifier
  - `level` (integer): Severity level (1-15, where 15 is most critical)
  - `description` (string): Human-readable rule description
  - `groups` (array): Rule classification groups
  - `pci_dss`, `gdpr`, `hipaa`, `nist_800_53`, `tsc` (arrays): Compliance framework mappings
  - `mitre` (object): MITRE ATT&CK framework mapping
- `agent` (object): Source agent information
- `manager` (object): Wazuh manager information
- `id` (string): Unique event identifier
- `cluster` (object): Cluster node information
- `full_log` (string): Original log message that triggered the alert
- `decoder` (object): Log decoder information
- `data` (object): Extracted data fields from the log
- `location` (string): Log file path or source

### Security Event Classification and Severity Levels

**Wazuh Alert Levels (1-15):**
- **Level 1-3**: Informational events (system status, normal operations)
- **Level 4-6**: Low priority events (minor configuration changes, warnings)
- **Level 7-9**: Medium priority events (authentication failures, policy violations)
- **Level 10-12**: High priority events (multiple failures, attack attempts)
- **Level 13-15**: Critical events (successful attacks, system compromise)

**GRCOS Severity Mapping:**
```json
{
  "severity_mapping": {
    "1-3": "info",
    "4-6": "low",
    "7-9": "medium",
    "10-12": "high",
    "13-15": "critical"
  },
  "oscal_impact_mapping": {
    "info": "low",
    "low": "low",
    "medium": "moderate",
    "high": "high",
    "critical": "high"
  }
}
```

## Query Capabilities

### Filtering and Search Parameters

**Wazuh Query Syntax:**
- **Exact Match**: `field=value`
- **Range Queries**: `field>value`, `field<value`, `field>=value`, `field<=value`
- **Multiple Values**: `field=value1,value2,value3`
- **Negation**: `field!=value`
- **Wildcard**: `field~value` (partial match)
- **Date Ranges**: `timestamp>2024-01-01T00:00:00.000Z`

**Example Complex Queries:**
```http
# High severity authentication failures from specific IP
GET /v1/security/events?search=rule.level>=10;rule.groups=authentication_failed;data.srcip=*************

# Critical vulnerabilities in web servers
GET /v1/vulnerability?search=severity=Critical;agent.groups=web-servers

# File changes in system directories
GET /v1/syscheck?search=file~/etc;event=modified;date>2024-01-15T00:00:00.000Z
```

### Time-based Queries and Date Ranges

**Supported Date Range Formats:**
- **Relative**: `1h`, `24h`, `7d`, `30d`, `1y`
- **Absolute**: `2024-01-15T00:00:00.000Z` to `2024-01-16T00:00:00.000Z`
- **Timestamp Range**: `timestamp>1642204800000&timestamp<1642291200000`

**Example Time-based Queries:**
```http
# Events from last 24 hours
GET /v1/security/events?date_range=24h

# Events from specific time window
GET /v1/security/events?timestamp>2024-01-15T00:00:00.000Z&timestamp<2024-01-16T00:00:00.000Z

# Vulnerability scans from last week
GET /v1/vulnerability/summary?date_range=7d
```

### Aggregation and Statistical Queries

**Supported Aggregations:**
- **Group By**: `group_by=field1,field2`
- **Count**: Automatic count aggregation
- **Time Series**: `interval=1h,1d,1w`
- **Top N**: `top=10` (most frequent values)

**Example Aggregation Queries:**
```http
# Alert summary by severity and agent
GET /v1/security/events/summary?group_by=rule.level,agent.id&date_range=7d

# Top 10 most active rules
GET /v1/security/events/summary?group_by=rule.id&top=10&date_range=24h

# Hourly event trends
GET /v1/security/events/summary?interval=1h&date_range=24h
```

## Integration Considerations

### Webhook/Streaming Options for Real-time Monitoring

**Server-Sent Events (SSE):**
```javascript
const eventSource = new EventSource('/v1/security/events/stream?rule.level>=7');

eventSource.onmessage = function(event) {
  const securityEvent = JSON.parse(event.data);

  // Process security event in GRCOS Monitor module
  await processSecurityEvent(securityEvent);
};

eventSource.onerror = function(event) {
  console.error('SSE connection error:', event);
  // Implement reconnection logic
};
```

**WebSocket Integration:**
```javascript
const ws = new WebSocket('wss://wazuh-manager:55000/v1/events/websocket');

ws.onopen = function() {
  // Subscribe to specific event types
  ws.send(JSON.stringify({
    action: 'subscribe',
    filters: {
      'rule.level': '>=7',
      'agent.groups': 'critical-systems'
    }
  }));
};

ws.onmessage = function(event) {
  const securityEvent = JSON.parse(event.data);
  // Forward to GRCOS Monitor module
  grcos.monitor.processEvent(securityEvent);
};
```

### Bulk Data Export Capabilities

**Large Dataset Export:**
```http
# Export all events for compliance reporting
GET /v1/security/events/export?format=json&date_range=30d&limit=0

# Export vulnerability data for risk assessment
GET /v1/vulnerability/export?format=csv&severity=Critical,High&date_range=90d
```

**Streaming Export for Large Datasets:**
```python
import aiohttp
import asyncio

async def stream_security_events(start_date, end_date):
    async with aiohttp.ClientSession() as session:
        params = {
            'timestamp': f'>{start_date}',
            'timestamp': f'<{end_date}',
            'limit': 1000,
            'offset': 0
        }

        while True:
            async with session.get('/v1/security/events', params=params) as response:
                data = await response.json()

                if not data['data']['affected_items']:
                    break

                # Process batch of events
                for event in data['data']['affected_items']:
                    yield event

                params['offset'] += 1000
```

### Error Handling and Retry Mechanisms

**Exponential Backoff Strategy:**
```python
import asyncio
import random

class WazuhAPIClient:
    def __init__(self, base_url, max_retries=3):
        self.base_url = base_url
        self.max_retries = max_retries

    async def request_with_retry(self, method, endpoint, **kwargs):
        for attempt in range(self.max_retries + 1):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.request(method, f"{self.base_url}{endpoint}", **kwargs) as response:
                        if response.status == 200:
                            return await response.json()
                        elif response.status == 429:  # Rate limited
                            wait_time = (2 ** attempt) + random.uniform(0, 1)
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            response.raise_for_status()
            except aiohttp.ClientError as e:
                if attempt == self.max_retries:
                    raise
                wait_time = (2 ** attempt) + random.uniform(0, 1)
                await asyncio.sleep(wait_time)
```

### Performance Optimization for Large Datasets

**Connection Pooling and Caching:**
```python
import aioredis
from aiohttp import ClientSession, TCPConnector

class OptimizedWazuhClient:
    def __init__(self, base_url, redis_url):
        self.base_url = base_url
        self.connector = TCPConnector(limit=100, limit_per_host=30)
        self.session = ClientSession(connector=self.connector)
        self.redis = aioredis.from_url(redis_url)
        self.cache_ttl = 300  # 5 minutes

    async def get_cached_or_fetch(self, endpoint, cache_key=None):
        if cache_key:
            cached = await self.redis.get(cache_key)
            if cached:
                return json.loads(cached)

        response = await self.session.get(f"{self.base_url}{endpoint}")
        data = await response.json()

        if cache_key:
            await self.redis.setex(cache_key, self.cache_ttl, json.dumps(data))

        return data
```

---

**Document Version**: 1.0
**Classification**: Internal Use
**Next Review**: Quarterly
**Owner**: GRCOS Security Integration Team
