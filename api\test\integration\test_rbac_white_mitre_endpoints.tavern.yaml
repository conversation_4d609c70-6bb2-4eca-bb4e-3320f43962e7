---
test_name: GET /mitre/metadata

stages:

  # GET /mitre/metadata
  - name: Request MITRE metadata (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/metadata"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &permission_allowed
      status_code: 200
      json:
        error: 0

---
test_name: GET /mitre/mitigations

stages:

  # GET /mitre/mitigations
  - name: Request MITRE mitigations (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/mitigations"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_allowed

---
test_name: GET /mitre/references

stages:

  # GET /mitre/references
  - name: Request MITRE references (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/references"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_allowed

---
test_name: GET /mitre/techniques

stages:

  # GET /mitre/techniques
  - name: Request MITRE techniques (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/techniques"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_allowed

---
test_name: GET /mitre/tactics

stages:

  # GET /mitre/tactics
  - name: Request MITRE tactics (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/tactics"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_allowed

---
test_name: GET /mitre/groups

stages:

  # GET /mitre/groups
  - name: Request MITRE groups (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/groups"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_allowed

---
test_name: GET /mitre/software

stages:

  # GET /mitre/software
  - name: Request MITRE software (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/software"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_allowed
