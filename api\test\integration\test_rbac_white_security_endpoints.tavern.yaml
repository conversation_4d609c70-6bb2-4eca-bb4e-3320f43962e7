---
test_name: GET USERS RBAC

stages:

  - name: Get all users in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 100
              username: administrator
              allow_run_as: false
              roles:
                - 100
                - 101
            - id: 101
              username: normal
              allow_run_as: false
              roles:
                - 104
                - 105
                - 103
            - id: 103
              username: python
              allow_run_as: false
              roles:
                - 101
            - id: 105
              username: guest
              allow_run_as: false
              roles: []
          total_affected_items: 4
          total_failed_items: 0
          failed_items: []

  - name: Get a specified user by its id (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 101
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 101
              username: normal
              allow_run_as: false
              roles:
                - 104
                - 105
                - 103
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Get a specified user by its id (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 1
    response: &permission_denied
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

  - name: Get a list of users by its id (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 101,103,105
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 101
              username: normal
              allow_run_as: false
            - id: 103
              username: python
              allow_run_as: false
            - id: 105
              username: guest
              allow_run_as: false
          failed_items: []
          total_affected_items: 3
          total_failed_items: 0

  - name: Get a list of users by its id (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 1,102
    response:
      <<: *permission_denied

  - name: Get a list of users by its username (Both)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 101,103,105,102,1,100
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 100
              username: administrator
              allow_run_as: false
              roles:
                - 100
                - 101
            - id: 101
              username: normal
              allow_run_as: false
              roles:
                - 104
                - 105
                - 103
            - id: 103
              username: python
              allow_run_as: false
              roles:
                - 101
            - id: 105
              username: guest
              roles: []
          total_affected_items: 4
          total_failed_items: 2
          failed_items:
            - error:
                code: 4000
              id:
                - 1
                - 102

---
test_name: GET ROLES RBAC

stages:

  - name: Get all roles in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
              name: administrator
            - id: 2
              name: readonly
            - id: 3
              name: users_admin
            - id: 4
              name: agents_readonly
            - id: 5
              name: agents_admin
            - id: 6
              name: cluster_readonly
            - id: 7
              name: cluster_admin
            - id: 99
              name: testing
            - id: 100
              name: wazuh
            - id: 101
              name: wazuh-wui
            - id: 102
              name: technical
            - id: 103
              name: administrator_test
            - id: 104
              name: normalUser
            - id: 105
              name: ossec
          total_affected_items: 14
          total_failed_items: 0
          failed_items: []

  - name: Get a specified role by its id (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        role_ids: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
              name: administrator
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

  - name: Get a specified role by its id (It doesn't exist but we have all the permissions on the resource roles)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        role_ids: 999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4002
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1

  - name: Get a list of roles by its id (Existent and no existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        role_ids: 1,2,999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
              name: administrator
            - id: 2
              name: readonly
          total_affected_items: 2
          total_failed_items: 1
          failed_items:
            - error:
                code: 4002
              id:
                - 999

---
test_name: GET RULES RBAC

stages:

  - name: Get all rules in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
            - id: 2
            - id: 100
            - id: 101
            - id: 102
            - id: 103
            - id: 104
            - id: 105
          failed_items: []
          total_affected_items: 8
          total_failed_items: 0

---
test_name: GET POLICIES RBAC

stages:

  - name: Get all policies in the system (All denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Get a specified policy by its id (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        policy_ids: 1,2,3
    response:
      <<: *permission_denied

---
test_name: GET SECURITY CONFIG

stages:

  - name: Get current security config (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          auth_token_exp_timeout: !anyint
          rbac_mode: !anystr

---
test_name: UPDATE SECURITY CONFIG

stages:

  - name: Update default security config (deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        auth_token_exp_timeout: 3000
    response:
      status_code: 200

---
test_name: UPDATE USERS RBAC

stages:

  - name: Update one specified user in the system (All denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/105"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        password: stringA1!
    response:
      <<: *permission_denied

  - name: Update one specified user in the system (All denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/1"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        password: stringA1!
    response:
      <<: *permission_denied

---
test_name: UPDATE USER'S ALLOW_RUN_AS FLAG

stages:

  - name: Update one specified user's allow_run_as flag (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103/run_as"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      params:
        allow_run_as: true
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              username: python
              allow_run_as: true
          total_affected_items: 1

---
test_name: UPDATE ROLES RBAC

stages:

  - name: Update one specified role in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/102"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: technicalModified
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              name: technicalModified
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

  - name: Update one specified role in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/2"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: wazuh-wuiModified
    response:
      <<: *permission_denied

---
test_name: UPDATE RULES RBAC

stages:

  - name: Update one specified rule in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: modified_rule # No need to update the rule itself
    response:
      <<: *permission_denied

---
test_name: UPDATE POLICIES RBAC

stages:

  - name: Update one specified policy in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: normalPolicyModified
        policy:
          actions:
            - agent:delete
          effect: deny
          resources:
            - agent:id:099
            - agent:id:002
            - agent:id:003
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              name: normalPolicyModified
              policy:
                actions:
                  - agent:delete
                resources:
                  - agent:id:099
                  - agent:id:002
                  - agent:id:003
                effect: deny
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

  - name: Update one specified policy in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/106"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: administratorPolicyModified
        policy:
          actions:
            - agent:delete
          effect: allow
          resources:
            - agent:id:*
    response:
      <<: *permission_denied

---
test_name: CREATE LINK USER-ROLES RBAC

stages:

  - name: Create one specified link between one user and a list of roles (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        role_ids: 102,103
    response:
      <<: *permission_denied

  - name: Create one specified link between one user and a list of roles (Allow and Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/104/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        role_ids: 102,103,104,105
    response:
      <<: *permission_denied

---
test_name: CREATE LINK ROLE-POLICIES RBAC

stages:

  - name: Create one specified link between one role and a list of policies (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/103/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        policy_ids: 104,105,106
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0
          total_failed_items: 3
          failed_items:
            - error:
                code: 4000
              id:
                - 105
                - 106
            - error:
                code: 4011
              id:
                - 104

---
test_name: CREATE LINK USER-ROLES RBAC

stages:

  - name: Create one specified link between one user and a list of roles (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        role_ids: 102,103
    response:
      <<: *permission_denied

  - name: Create one specified link between one user and a list of roles (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/104/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        role_ids: 102,103,104,105
    response:
      <<: *permission_denied

---
test_name: CREATE LINK ROLES-RULES RBAC

stages:

  - name: Create one specified link between one role and a list of rules (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/103/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        rule_ids: 101
    response:
      <<: *permission_denied

  - name: Create one specified link between one role and a list of rules (Partially allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/105/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        rule_ids: 102,103
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 105
              rules:
                - 102
                - 105
          failed_items:
            - id:
                - 103
              error:
                code: 4000
          total_affected_items: 1
          total_failed_items: 1

---
test_name: DELETE LINK ROLE-RULES RBAC

stages:

  - name: Delete one specified link between one role and a list of rules (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/11/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 104,105,106
    response:
      <<: *permission_denied

---
test_name: DELETE LINK ROLE-POLICIES RBAC

stages:

  - name: Delete one specified link between one user and a list of roles (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 102,103
    response:
      <<: *permission_denied

  - name: Delete one specified link between one user and a list of roles (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/104/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 102,103,104,105
    response:
      <<: *permission_denied

---
test_name: DELETE LINK ROLE-POLICIES RBAC

stages:

  - name: Delete one specified link between one role and a list of policies (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/11/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 104,105,106
    response:
      <<: *permission_denied

---
test_name: DELETE USERS RBAC

stages:

  - name: Delete one specified user in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 105
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 105
              username: guest
              allow_run_as: false
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Delete all allowed user in the system (All)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 101
              username: normal
              allow_run_as: false
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Delete all allowed user in the system (All)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Delete a list of users in the system (Allow and deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 1,101,105
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4000
              id:
                - 1
            - error:
                code: 5001
              id:
                - 101
                - 105
          total_affected_items: 0
          total_failed_items: 3

---
test_name: DELETE RULES RBAC

stages:

  - name: Delete all rules in the system (Deny) (User agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Delete one specified rule in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 101
    response:
      <<: *permission_denied

---
test_name: DELETE ROLES RBAC

stages:

  - name: Delete all roles in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Delete one specified role in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 101,102
    response:
      <<: *permission_denied

---
test_name: DELETE POLICIES RBAC

stages:

  - name: Delete one specified policy in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Delete one specified policy in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 101,102
    response:
      <<: *permission_denied

---
test_name: CREATE USERS RBAC

stages:

  - name: Create one specified user (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      json:
        username: newUser
        password: stringA1!
    response:
      <<: *permission_denied

---
test_name: CREATE ROLES AND POLICIES RBAC

stages:

  - name: Create one specified role (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      json:
        name: newUserRole
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: !anyint
              name: newUserRole
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Create one specified policy (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      json:
        name: newUserPolicy
        policy:
          actions:
            - security:delete
          effect: allow
          resources:
            - role:id:*
            - policy:id:*
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: !anyint
              name: newUserPolicy
              policy:
                actions:
                  - security:delete
                effect: allow
                resources:
                  - role:id:*
                  - policy:id:*
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: REVOKE TOKENS RBAC

stages:

  - name: Revoke all tokens (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/revoke"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
    response:
      status_code: 403
      json:
        error: 4000
