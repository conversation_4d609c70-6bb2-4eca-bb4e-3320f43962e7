---
test_name: GET /syscheck/000

stages:

    # GET /syscheck/000
  - name: Try to get syscheck scan results for agent 000
    request: &get_syscheck_agent
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/000"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        # We get totalItems number of arrays in items, using !anything to check items key is in the response
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1
  - name: Try to get syscheck scan results for agent 000 with a set limit of 1 answer
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        type: file
    response:
      status_code: 200
      json:
        error: 0
        data:
          # We define this items answer as a common one array full answer
          affected_items: &full_items_array
            - changes: !anyint
              date: !anystr
              file: !anystr
              gid: !anystr
              gname: !anystr
              inode: !anyint
              md5: !anystr
              mtime: !anystr
              perm: !anystr
              sha1: !anystr
              sha256: !anystr
              size: !anyint
              type: !anystr
              uid: !anystr
              uname: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      # Save some data for future use in the test
      save:
        json:
          returned_file: data.affected_items[0].file
          returned_md5: data.affected_items[0].md5
          returned_sha1: data.affected_items[0].sha1
          returned_sha256: data.affected_items[0].sha256
          returned_total: data.total_affected_items

  # GET /syscheck/000?q=file={returned_file};md5~{returned_md5};sha1={returned_sha1};sha256={returned_sha256}
  - name: Filters by composed query
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        q: file={returned_file};md5~{returned_md5};sha1={returned_sha1};sha256={returned_sha256}
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - file: "{returned_file}"
              md5: "{returned_md5}"
              sha1: "{returned_sha1}"
              sha256: "{returned_sha256}"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

    # GET /syscheck/000?limit=1&summary=True
  - name: Try to get limited syscheck scan results for agent 000 using summarize (date,file,mtime) parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        summary: True
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - date: !anystr
              file: !anystr
              mtime: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # We implement a dual stage to check offset parameter behaviour
    # GET /syscheck/000?limit=2&offset=0
  - name: Try to get syscheck scan results for agent 000 using limit and offset parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *full_items_array
            - <<: *full_items_array
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      # Save second item to check offset in next stage
      save:
        json:
          offset_item: data.affected_items[1]

    # GET /syscheck/000?limit=1&offset=1
  - name: Try to get syscheck scan results for agent 000 using limit and offset parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
              # Save second item to check offset in next stage
            - changes: !int "{offset_item.changes}"
              date: "{offset_item.date}"
              file: "{offset_item.file}"
              gid: "{offset_item.gid}"
              gname: "{offset_item.gname}"
              inode: !int "{offset_item.inode}"
              md5: "{offset_item.md5}"
              mtime: "{offset_item.mtime}"
              perm: "{offset_item.perm}"
              sha1: "{offset_item.sha1}"
              sha256: "{offset_item.sha256}"
              size: !int "{offset_item.size}"
              type: "{offset_item.type}"
              uid: "{offset_item.uid}"
              uname: "{offset_item.uname}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&search=a
  - name: Try to get limited syscheck scan results for agent 000 using search parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        search: a
    response:
      status_code: 200
      json:
        error: 0 
        data:
          affected_items: *full_items_array
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&type=file
  - name: Try to get limited syscheck scan results for agent 000 using type filtering (file)
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        type: file
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&md5={returned_md5}
  - name: Try to get limited syscheck scan results for agent 000 using md5 filtering
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        md5: "{returned_md5:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - md5: "{tavern.request_vars.params.md5}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&sha1={returned_sha1}
  - name: Try to get limited syscheck scan results for agent 000 using sha1 filtering
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        sha1: "{returned_sha1:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha1: "{tavern.request_vars.params.sha1}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&sha256={returned_sha256}
  - name: Try to get limited syscheck scan results for agent 000 using sha256 filtering
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        sha256: "{returned_sha256:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha256: "{tavern.request_vars.params.sha256}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&hash=returned_md5
  - name: Try to get limited syscheck scan results for agent 000 using hash filtering (md5)
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        hash: "{returned_md5:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - md5: "{tavern.request_vars.params.hash}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&hash={returned_sha1}
  - name: Try to get limited syscheck scan results for agent 000 using hash filtering (sha1)
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        hash: "{returned_sha1:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha1: "{tavern.request_vars.params.hash}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&hash={returned_sha256}
  - name: Try to get limited syscheck scan results for agent 000 using hash filtering (sha256)
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        hash: "{returned_sha256:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha256: "{tavern.request_vars.params.hash}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=1&file={returned_file}
  - name: Try to get limited syscheck scan results for agent 000 using filename filtering
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        file: "{returned_file:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - file: "{tavern.request_vars.params.file}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/000?limit=2&sort=file,date
  - name: Try to get syscheck scan results for agent 000 using limit and sort ascending filtering
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 20
        sort: file,date
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "file,date"
      status_code: 200

    # GET /syscheck/000?limit=1&sort=wrongparam
  - name: Try to get syscheck scan results for agent 000 using an invalid sort parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        sort: wrongparam
    response:
      status_code: 400
      json:
        error: 1403

    # GET /syscheck/000?limit=1&select=wrongparam
  - name: Try to get syscheck scan results for agent 000 using an invalid select parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        select: wrongparam
    response:
      status_code: 400
      json:
        error: 1724

    # GET /syscheck/000?pretty=false&wait_for_complete=true&offset=0&select=uname%2Cperm&distinct=true
  - name: Try to get syscheck scan results for agent 000 using distinct parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        distinct: true
        select: uname,perm
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

    # GET /syscheck/000?limit=1&distinct=tr
  - name: Try to get syscheck scan results for agent 000 using an invalid distinct parameter
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        distinct: tr
    response:
      status_code: 400

  # GET /syscheck/012
  - name: Try to get 012 agent's syscheck data (never connected)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/012"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        error: 2007

---
# Another GET /syscheck/{agent_id} test to parametrize values for sort and select parameters
test_name: GET /syscheck/000

marks:
  - parametrize:
      key: field
      vals:
        - date
        - mtime
        - file
        - size
        - perm
        - uname
        - gname
        - md5
        - sha1
        - sha256
        - inode
        - gid
        - uid
        - type

stages:

    # GET /syscheck/000?limit=1&sort={field}
  - name: Try to get syscheck scan results for agent 000 with a sorted field answer
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 10
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

    # GET /syscheck/000?limit=1&select={field}
  - name: Try to get syscheck scan results for agent 000 with a selected field answer
    request:
      verify: False
      <<: *get_syscheck_agent
      params:
        limit: 1
        select: "{field}"
    response:
      status_code: 200
      verify_response_with:
          # Check response item keys are the selected keys
          function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: "{field:s}"
      json:
        error: 0
        data:
          total_affected_items: !anyint

---
test_name: GET /syscheck/000/last_scan

stages:

    # GET /syscheck/000/last_scan
  - name: Try to get when the last scan for agent 000 started and ended
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/000/last_scan"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - end: !anything
              start: !anystr
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: PUT /syscheck

stages:

  - name: Try to run a syscheck scan in agent 000
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '000'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '000'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to run a syscheck scan for a list of agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '000,002,004,006,008,010,012'
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '000'
            - '002'
            - '004'
            - '006'
            - '008'
          failed_items:
            - error:
                code: 1707
              id:
                - '010'
                - '012'
          total_affected_items: 5
          total_failed_items: 2

  - name: Try to run a syscheck scan for non existing agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '002,040,050,060'
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '002'
          failed_items:
            - error:
                code: 1701
              id:
                - '040'
                - '050'
                - '060'
          total_affected_items: 1
          total_failed_items: 3

  - name: Run a syscheck scan in all agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 9
          total_failed_items: 0

---
test_name: GET /syscheck/002

stages:

  - name: Try to get syscheck scan results for agent 002
    request: &get_syscheck_agent_002
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/002"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1
  - name: Try to get syscheck scan results for agent 002 with a set limit of 1 answer
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          # We define this items answer as a common one array full answer
          affected_items: &full_items_array_002
            - changes: !anyint
              date: !anystr
              file: !anystr
              gid: !anystr
              gname: !anystr
              inode: !anyint
              md5: !anystr
              mtime: !anystr
              perm: !anystr
              sha1: !anystr
              sha256: !anystr
              size: !anyint
              type: !anystr
              uid: !anystr
              uname: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      # Save some data for future use in the test
      save:
        json:
          returned_file: data.affected_items[0].file
          returned_md5: data.affected_items[0].md5
          returned_sha1: data.affected_items[0].sha1
          returned_sha256: data.affected_items[0].sha256
          returned_total: data.total_affected_items

    # GET /syscheck/002?limit=1&summary=True
  - name: Try to get limited syscheck scan results for agent 002 using summarize (date,file,mtime) parameter
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        summary: True
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - date: !anystr
              file: !anystr
              mtime: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # We implement a dual stage to check offset parameter behaviour
    # GET /syscheck/002?limit=2&offset=0
  - name: Try to get syscheck scan results for agent 002 using limit and offset parameter
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *full_items_array_002
            - <<: *full_items_array_002
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      # Save second item to check offset in next stage
      save:
        json:
          offset_item: data.affected_items[1]

    # GET /syscheck/002?limit=1&offset=1
  - name: Try to get syscheck scan results for agent 002 using limit and offset parameter
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
              # Save second item to check offset in next stage
            - changes: !int "{offset_item.changes}"
              date: "{offset_item.date}"
              file: "{offset_item.file}"
              gid: "{offset_item.gid}"
              gname: "{offset_item.gname}"
              inode: !int "{offset_item.inode}"
              md5: "{offset_item.md5}"
              mtime: "{offset_item.mtime}"
              perm: "{offset_item.perm}"
              sha1: "{offset_item.sha1}"
              sha256: "{offset_item.sha256}"
              size: !int "{offset_item.size}"
              type: "{offset_item.type}"
              uid: "{offset_item.uid}"
              uname: "{offset_item.uname}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&search=a
  - name: Try to get limited syscheck scan results for agent 002 using search parameter
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        search: a
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: *full_items_array_002
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&type=file
  - name: Try to get limited syscheck scan results for agent 002 using type filtering (file)
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        type: file
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&md5={returned_md5}
  - name: Try to get limited syscheck scan results for agent 002 using md5 filtering
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        md5: "{returned_md5:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - md5: "{tavern.request_vars.params.md5}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&sha1={returned_sha1}
  - name: Try to get limited syscheck scan results for agent 002 using sha1 filtering
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        sha1: "{returned_sha1:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha1: "{tavern.request_vars.params.sha1}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&sha256={returned_sha256}
  - name: Try to get limited syscheck scan results for agent 002 using sha256 filtering
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        sha256: "{returned_sha256:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha256: "{tavern.request_vars.params.sha256}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&hash=returned_md5
  - name: Try to get limited syscheck scan results for agent 002 using hash filtering (md5)
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        hash: "{returned_md5:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - md5: "{tavern.request_vars.params.hash}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&hash={returned_sha1}
  - name: Try to get limited syscheck scan results for agent 002 using hash filtering (sha1)
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        hash: "{returned_sha1:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha1: "{tavern.request_vars.params.hash}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&hash={returned_sha256}
  - name: Try to get limited syscheck scan results for agent 002 using hash filtering (sha256)
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        hash: "{returned_sha256:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - sha256: "{tavern.request_vars.params.hash}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # GET /syscheck/002?limit=1&file={returned_file}
  - name: Try to get limited syscheck scan results for agent 002 using filename filtering
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        file: "{returned_file:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - file: "{tavern.request_vars.params.file}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

    # We implement a dual stage to check sort parameter behaviour
    # GET /syscheck/002?limit=2&sort=file,date
  - name: Try to get syscheck scan results for agent 002 using limit and sort ascending filtering
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 5
        sort: file,date
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "file,date"
      status_code: 200

    # GET /syscheck/002?limit=1&sort=wrongparam
  - name: Try to get syscheck scan results for agent 002 using an invalid sort parameter
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        sort: wrongparam
    response:
      status_code: 400
      json:
        error: 1403

    # GET /syscheck/002?limit=1&select=wrongparam
  - name: Try to get syscheck scan results for agent 002 using an invalid select parameter
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        select: wrongparam
    response:
      status_code: 400
      json:
        error: 1724

---
# Another GET /syscheck/{agent_id} test to parametrize values for sort and select parameters
test_name: GET /syscheck/002

marks:
  - parametrize:
      key: field
      vals:
        - date
        - mtime
        - file
        - size
        - perm
        - uname
        - gname
        - md5
        - sha1
        - sha256
        - inode
        - gid
        - uid
        - type

stages:

    # GET /syscheck/002?limit=1&sort={field}
  - name: Try to get syscheck scan results for agent 002 with a sorted field answer
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 5
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

    # GET /syscheck/002?limit=1&select={field}
  - name: Try to get syscheck scan results for agent 002 with a selected field answer
    request:
      verify: False
      <<: *get_syscheck_agent_002
      params:
        limit: 1
        select: "{field}"
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: 0
        data:
          total_affected_items: !anyint

---
test_name: GET /syscheck/002/last_scan

stages:

    # GET /syscheck/002/last_scan
  - name: Try to get when the last scan for agent 002 started and ended
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/002/last_scan"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - end: !anything
              start: !anystr
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: DELETE /syscheck

stages:

  - name: Try to delete syscheck scans in agent 001 (Failed, agent is newer than expected)
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/001"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 1760
              id:
                - '001'
          total_affected_items: 0
          total_failed_items: 1
