# GRCOS Wazuh SIEM/XDR Integration Documentation

## Overview

This document provides comprehensive technical specifications for integrating Wazuh SIEM/XDR capabilities into the GRCOS Monitor module. The integration enables real-time security monitoring, threat detection, and automated incident response within GRCOS's blockchain-secured compliance management framework.

Wazuh provides a powerful REST API for accessing security events, agent management, rule configuration, and threat intelligence data. This integration transforms reactive security monitoring into proactive, policy-driven threat detection aligned with OSCAL security controls.

## Integration Architecture

### GRCOS Monitor Module Integration Points

```mermaid
graph TB
    subgraph "GRCOS Platform"
        A[System Agent] --> B[Monitor Module]
        B --> C[OPA Policy Engine]
        B --> D[OSCAL Control Mapping]
        B --> E[Blockchain Evidence Store]
    end

    subgraph "Wazuh Platform"
        F[Wazuh Manager] --> G[REST API]
        F --> H[Agent Network]
        F --> I[Rule Engine]
        F --> J[Alert Database]
    end

    B --> G
    G --> K[Real-time Events]
    G --> L[Agent Status]
    G --> M[Security Alerts]
    G --> N[Vulnerability Data]

    K --> C
    L --> D
    M --> E
    N --> E
```

### Data Flow Architecture

1. **Real-time Event Streaming**: Continuous monitoring of Wazuh security events
2. **OSCAL Control Mapping**: Automatic mapping of security events to OSCAL controls
3. **Policy-Driven Response**: OPA-based automated response and escalation
4. **Blockchain Evidence**: Immutable storage of security incidents and responses
5. **Cross-Domain Intelligence**: Unified threat intelligence across IT/OT/IoT environments

## Wazuh API Base Configuration

### Base URL Structure

```
https://your-wazuh-manager:55000/
```

### API Versioning

Wazuh API uses versioning in the URL path:
- **Current Version**: `/v1/` (recommended)
- **Legacy Support**: Available for backward compatibility

**Complete Base URL:**
```
https://your-wazuh-manager:55000/v1/
```

## Authentication & Connection Management

### Authentication Methods

Wazuh API supports multiple authentication methods for GRCOS integration:

1. **JWT Token Authentication** (Recommended for GRCOS)
2. **Basic Authentication** (Development/Testing)
3. **API Key Authentication** (Service-to-Service)

### JWT Token Authentication (Recommended)

**Endpoint:** `POST /security/user/authenticate`

**Request:**
```json
{
  "user": "wazuh-admin",
  "password": "your_secure_password"
}
```

**Response:**
```json
{
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "policies": {
      "rbac_mode": "white",
      "rbac_policies": ["policy1", "policy2"]
    }
  },
  "message": "User authenticated successfully",
  "error": 0
}
```

### Token Management for GRCOS Integration

**Token Refresh Strategy:**
```javascript
class WazuhTokenManager {
  constructor(baseURL, credentials) {
    this.baseURL = baseURL;
    this.credentials = credentials;
    this.token = null;
    this.tokenExpiry = null;
    this.refreshBuffer = 300; // 5 minutes before expiry
  }

  async getValidToken() {
    if (!this.token || this.isTokenExpiring()) {
      await this.refreshToken();
    }
    return this.token;
  }

  async refreshToken() {
    const response = await fetch(`${this.baseURL}/security/user/authenticate`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(this.credentials)
    });

    const data = await response.json();
    this.token = data.data.token;
    this.tokenExpiry = Date.now() + (15 * 60 * 1000); // 15 minutes
  }

  isTokenExpiring() {
    return Date.now() > (this.tokenExpiry - this.refreshBuffer * 1000);
  }
}
```

### Required Headers for GRCOS Integration

For all authenticated requests, include:

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
Accept: application/json
X-GRCOS-Request-ID: <unique_request_id>
X-GRCOS-Module: monitor
```

### Rate Limiting and Connection Best Practices

**Rate Limits:**
- **Default**: 300 requests per minute per IP
- **Burst**: Up to 50 requests in 10 seconds
- **Concurrent**: Maximum 10 concurrent connections

**Connection Pool Configuration:**
```python
import aiohttp
import asyncio

class WazuhConnectionPool:
    def __init__(self, base_url, max_connections=10):
        self.base_url = base_url
        self.connector = aiohttp.TCPConnector(
            limit=max_connections,
            limit_per_host=max_connections,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        self.session = aiohttp.ClientSession(
            connector=self.connector,
            timeout=aiohttp.ClientTimeout(total=30)
        )

    async def request(self, method, endpoint, **kwargs):
        url = f"{self.base_url}{endpoint}"
        async with self.session.request(method, url, **kwargs) as response:
            return await response.json()

    async def close(self):
        await self.session.close()
```

## Core Data Retrieval Endpoints

### Agent Management API

#### Get All Agents
- **URL:** `GET /agents`
- **Description:** Retrieve all Wazuh agents with status and configuration information
- **OSCAL Mapping:** Maps to OSCAL Component Definition for asset inventory

**Example Request:**
```http
GET /v1/agents?limit=500&offset=0&sort=+id&search=status=active
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "id": "001",
        "name": "web-server-01",
        "ip": "*************",
        "status": "active",
        "os": {
          "arch": "x86_64",
          "major": "20",
          "minor": "04",
          "name": "Ubuntu",
          "platform": "ubuntu",
          "version": "20.04.3 LTS"
        },
        "version": "4.7.0",
        "manager": "wazuh-manager",
        "node_name": "worker-01",
        "dateAdd": "2024-01-15T10:30:00.000Z",
        "lastKeepAlive": "2024-01-15T15:45:30.000Z",
        "group": ["default", "web-servers"],
        "configSum": "ab73af41699f13fdd81903b5f23d8d00",
        "mergedSum": "f69c8a56d2cd8b401643a52cf423ad1c"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "All selected agents information was returned",
  "error": 0
}
```

#### Get Agent Details
- **URL:** `GET /agents/{agent_id}`
- **Description:** Retrieve detailed information for a specific agent
- **GRCOS Integration:** Used for asset discovery and inventory management

**Example Request:**
```http
GET /v1/agents/001
```

#### Get Agent Configuration
- **URL:** `GET /agents/{agent_id}/config/{component}`
- **Description:** Retrieve agent configuration for specific components
- **Components:** `agent`, `agentless`, `analysis`, `auth`, `com`, `csyslog`, `integrator`, `logcollector`, `mail`, `monitor`, `request`, `syscheck`, `wmodules`

**Example Request:**
```http
GET /v1/agents/001/config/syscheck
```

**Example Response:**
```json
{
  "data": {
    "syscheck": {
      "disabled": "no",
      "frequency": 43200,
      "scan_on_start": "yes",
      "auto_ignore": "no",
      "directories": [
        {
          "path": "/etc",
          "check_all": "yes",
          "report_changes": "yes",
          "realtime": "no"
        },
        {
          "path": "/usr/bin",
          "check_all": "yes",
          "report_changes": "yes",
          "realtime": "no"
        }
      ],
      "ignore": [
        "/etc/mtab",
        "/etc/hosts.deny",
        "/etc/mail/statistics"
      ]
    }
  },
  "message": "Agent configuration was successfully read",
  "error": 0
}
```

### Security Alerts and Events API

#### Get Security Alerts
- **URL:** `GET /security/events`
- **Description:** Retrieve security alerts and events for real-time monitoring
- **OSCAL Mapping:** Maps to OSCAL Assessment Results for continuous monitoring
- **GRCOS Integration:** Primary endpoint for Monitor module event ingestion

**Query Parameters:**
- `limit`: Maximum number of alerts to return (default: 500, max: 10000)
- `offset`: Number of alerts to skip for pagination
- `sort`: Sort field (+/-field_name for ascending/descending)
- `search`: Search query using Wazuh query syntax
- `date_range`: Time range for alerts (e.g., "1d", "7d", "30d")
- `rule.level`: Filter by alert severity level (1-15)
- `agent.id`: Filter by specific agent ID
- `rule.groups`: Filter by rule groups

**Example Request:**
```http
GET /v1/security/events?limit=100&offset=0&sort=-timestamp&search=rule.level>=7&date_range=1d
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "timestamp": "2024-01-15T15:45:30.123Z",
        "rule": {
          "id": 5712,
          "level": 10,
          "description": "Multiple authentication failures",
          "groups": ["authentication_failed", "authentication_failures"],
          "firedtimes": 1,
          "mail": false,
          "pci_dss": ["10.2.4", "10.2.5"],
          "gdpr": ["IV_35.7.d", "IV_32.2"],
          "hipaa": ["164.312.b"],
          "nist_800_53": ["AU.14", "AC.7"],
          "tsc": ["CC6.1", "CC6.8", "CC7.2", "CC7.3"]
        },
        "agent": {
          "id": "001",
          "name": "web-server-01",
          "ip": "*************"
        },
        "manager": {
          "name": "wazuh-manager"
        },
        "id": "1642263930.123456",
        "cluster": {
          "name": "wazuh-cluster",
          "node": "worker-01"
        },
        "full_log": "Jan 15 15:45:30 web-server-01 sshd[12345]: Failed password for invalid user admin from ************* port 22 ssh2",
        "decoder": {
          "name": "sshd",
          "parent": "sshd"
        },
        "data": {
          "srcip": "*************",
          "srcport": "22",
          "dstuser": "admin",
          "protocol": "ssh"
        },
        "location": "/var/log/auth.log",
        "syscheck": null,
        "previous_output": "Jan 15 15:44:28 web-server-01 sshd[12344]: Failed password for invalid user admin from ************* port 22 ssh2"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Security events information was returned",
  "error": 0
}
```

#### Get Real-time Events Stream
- **URL:** `GET /security/events/stream` (Server-Sent Events)
- **Description:** Real-time streaming of security events for immediate response
- **GRCOS Integration:** Used by Monitor module for real-time threat detection

**Example Request:**
```http
GET /v1/security/events/stream?rule.level>=5&agent.id=001
Accept: text/event-stream
```

**Example Response (SSE Format):**
```
data: {"timestamp":"2024-01-15T15:45:30.123Z","rule":{"id":5712,"level":10},"agent":{"id":"001"}}

data: {"timestamp":"2024-01-15T15:46:15.456Z","rule":{"id":1002,"level":2},"agent":{"id":"002"}}
```

#### Get Alert Summary
- **URL:** `GET /security/events/summary`
- **Description:** Aggregated security alert statistics for dashboards
- **GRCOS Integration:** Used for compliance reporting and trend analysis

**Example Request:**
```http
GET /v1/security/events/summary?date_range=7d&group_by=rule.level,agent.id
```

**Example Response:**
```json
{
  "data": {
    "summary": {
      "total_alerts": 1247,
      "critical_alerts": 23,
      "high_alerts": 156,
      "medium_alerts": 489,
      "low_alerts": 579,
      "by_agent": {
        "001": 234,
        "002": 189,
        "003": 167
      },
      "by_rule_group": {
        "authentication_failed": 89,
        "web_attack": 45,
        "system_error": 123,
        "file_integrity": 67
      },
      "trend": {
        "last_24h": 178,
        "previous_24h": 145,
        "change_percentage": 22.8
      }
    }
  },
  "message": "Alert summary was successfully generated",
  "error": 0
}
```

### Vulnerability Detection API

#### Get Vulnerability Scan Results
- **URL:** `GET /vulnerability/{agent_id}`
- **Description:** Retrieve vulnerability assessment results for specific agents
- **OSCAL Mapping:** Maps to OSCAL Assessment Results for vulnerability management
- **GRCOS Integration:** Used for risk assessment and compliance reporting

**Query Parameters:**
- `limit`: Maximum number of vulnerabilities to return
- `offset`: Number of vulnerabilities to skip for pagination
- `sort`: Sort field (+/-field_name)
- `search`: Search query for vulnerability filtering
- `severity`: Filter by severity (Critical, High, Medium, Low)
- `status`: Filter by status (Pending, Fixed, Ignored)

**Example Request:**
```http
GET /v1/vulnerability/001?limit=100&sort=-severity&search=severity=Critical
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "agent": {
          "id": "001",
          "name": "web-server-01",
          "ip": "*************"
        },
        "cve": "CVE-2024-1234",
        "title": "Critical Remote Code Execution Vulnerability",
        "severity": "Critical",
        "score": {
          "base": 9.8,
          "temporal": 9.5,
          "environmental": 9.2
        },
        "package": {
          "name": "openssl",
          "version": "1.1.1f-1ubuntu2.16",
          "architecture": "amd64",
          "condition": "Package less than 1.1.1f-1ubuntu2.17"
        },
        "published": "2024-01-10T00:00:00.000Z",
        "updated": "2024-01-12T00:00:00.000Z",
        "references": [
          "https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2024-1234",
          "https://ubuntu.com/security/CVE-2024-1234"
        ],
        "description": "A critical vulnerability in OpenSSL allows remote code execution...",
        "detection_time": "2024-01-15T10:30:00.000Z",
        "status": "Pending",
        "rationale": null,
        "external_references": [
          {
            "type": "advisory",
            "url": "https://www.openssl.org/news/secadv/20240110.txt"
          }
        ]
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Vulnerability information was returned",
  "error": 0
}
```

#### Get Vulnerability Summary
- **URL:** `GET /vulnerability/summary`
- **Description:** Aggregated vulnerability statistics across all agents
- **GRCOS Integration:** Used for risk dashboards and compliance reporting

**Example Request:**
```http
GET /v1/vulnerability/summary?date_range=30d
```

**Example Response:**
```json
{
  "data": {
    "summary": {
      "total_vulnerabilities": 1247,
      "critical": 23,
      "high": 156,
      "medium": 489,
      "low": 579,
      "by_agent": {
        "001": 234,
        "002": 189,
        "003": 167
      },
      "top_cves": [
        {
          "cve": "CVE-2024-1234",
          "affected_agents": 15,
          "severity": "Critical"
        }
      ],
      "remediation_status": {
        "pending": 892,
        "in_progress": 234,
        "fixed": 121
      }
    }
  },
  "message": "Vulnerability summary was successfully generated",
  "error": 0
}
```

### File Integrity Monitoring (FIM) API

#### Get FIM Events
- **URL:** `GET /syscheck/{agent_id}`
- **Description:** Retrieve file integrity monitoring events and changes
- **OSCAL Mapping:** Maps to OSCAL Assessment Results for system integrity monitoring
- **GRCOS Integration:** Used for change management and compliance auditing

**Query Parameters:**
- `limit`: Maximum number of FIM events to return
- `offset`: Number of events to skip for pagination
- `sort`: Sort field (+/-field_name)
- `search`: Search query for FIM event filtering
- `type`: Filter by event type (added, modified, deleted)
- `file`: Filter by specific file path

**Example Request:**
```http
GET /v1/syscheck/001?limit=100&sort=-date&search=type=modified
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "agent": {
          "id": "001",
          "name": "web-server-01",
          "ip": "*************"
        },
        "file": "/etc/passwd",
        "event": "modified",
        "date": "2024-01-15T15:45:30.123Z",
        "changes": 1,
        "size_before": 2847,
        "size_after": 2891,
        "perm_before": "rw-r--r--",
        "perm_after": "rw-r--r--",
        "uid_before": "0",
        "uid_after": "0",
        "gid_before": "0",
        "gid_after": "0",
        "md5_before": "d41d8cd98f00b204e9800998ecf8427e",
        "md5_after": "098f6bcd4621d373cade4e832627b4f6",
        "sha1_before": "da39a3ee5e6b4b0d3255bfef95601890afd80709",
        "sha1_after": "356a192b7913b04c54574d18c28d46e6395428ab",
        "sha256_before": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
        "sha256_after": "6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b",
        "attributes": ["checksum", "size", "owner", "group", "permission"],
        "content_changes": "+user:x:1001:1001:New User:/home/<USER>/bin/bash"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Syscheck information was returned",
  "error": 0
}
```

### Rules and Decoders API

#### Get Security Rules
- **URL:** `GET /rules`
- **Description:** Retrieve Wazuh security rules and their configurations
- **OSCAL Mapping:** Maps to OSCAL Control Implementation for security controls
- **GRCOS Integration:** Used for policy mapping and compliance validation

**Query Parameters:**
- `limit`: Maximum number of rules to return
- `offset`: Number of rules to skip for pagination
- `sort`: Sort field (+/-field_name)
- `search`: Search query for rule filtering
- `level`: Filter by rule level (1-15)
- `group`: Filter by rule group
- `pci_dss`: Filter by PCI DSS requirement
- `gdpr`: Filter by GDPR requirement
- `nist_800_53`: Filter by NIST 800-53 control

**Example Request:**
```http
GET /v1/rules?limit=100&sort=-level&search=nist_800_53=AU.14
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "id": 5712,
        "level": 10,
        "status": "enabled",
        "description": "Multiple authentication failures",
        "groups": ["authentication_failed", "authentication_failures"],
        "pci_dss": ["10.2.4", "10.2.5"],
        "gdpr": ["IV_35.7.d", "IV_32.2"],
        "hipaa": ["164.312.b"],
        "nist_800_53": ["AU.14", "AC.7"],
        "tsc": ["CC6.1", "CC6.8", "CC7.2", "CC7.3"],
        "mitre": {
          "tactic": ["Credential Access"],
          "id": ["T1110"],
          "technique": ["Brute Force"]
        },
        "details": {
          "frequency": 8,
          "timeframe": 240,
          "if_matched_sid": 5710,
          "same_source_ip": true
        },
        "filename": "/var/ossec/ruleset/rules/0095-sshd_rules.xml",
        "relative_dirname": "ruleset/rules",
        "file": "0095-sshd_rules.xml"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Rules information was returned",
  "error": 0
}
```

#### Get Rule Details
- **URL:** `GET /rules/{rule_id}`
- **Description:** Retrieve detailed information for a specific rule
- **GRCOS Integration:** Used for control mapping and policy validation

**Example Request:**
```http
GET /v1/rules/5712
```

#### Get Decoders
- **URL:** `GET /decoders`
- **Description:** Retrieve log decoders and parsing rules
- **GRCOS Integration:** Used for log analysis and event correlation

**Example Request:**
```http
GET /v1/decoders?limit=100&search=name=sshd
```

### System Inventory and Asset Discovery API

#### Get System Inventory
- **URL:** `GET /syscollector/{agent_id}`
- **Description:** Retrieve comprehensive system inventory for agents
- **OSCAL Mapping:** Maps to OSCAL Component Definition for asset inventory
- **GRCOS Integration:** Primary source for asset discovery and inventory management

**Inventory Types:**
- `hardware`: Hardware information (CPU, memory, storage)
- `os`: Operating system details
- `network`: Network interfaces and configuration
- `packages`: Installed software packages
- `processes`: Running processes
- `ports`: Open network ports

**Example Request:**
```http
GET /v1/syscollector/001/hardware
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "agent_id": "001",
        "board_serial": "0",
        "cpu_name": "Intel(R) Core(TM) i7-8700K CPU @ 3.70GHz",
        "cpu_cores": 6,
        "cpu_mhz": 3700.0,
        "ram_total": 16777216,
        "ram_free": 8388608,
        "ram_usage": 50,
        "scan_time": "2024-01-15T10:30:00.000Z"
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Hardware information was returned",
  "error": 0
}
```

#### Get Network Information
- **URL:** `GET /syscollector/{agent_id}/network`
- **Description:** Retrieve network interface and configuration details
- **GRCOS Integration:** Used for network topology mapping and security assessment

**Example Request:**
```http
GET /v1/syscollector/001/network
```

**Example Response:**
```json
{
  "data": {
    "affected_items": [
      {
        "agent_id": "001",
        "name": "eth0",
        "type": "ethernet",
        "state": "up",
        "mtu": 1500,
        "mac": "00:0c:29:12:34:56",
        "tx_packets": 1234567,
        "rx_packets": 2345678,
        "tx_bytes": 987654321,
        "rx_bytes": 1234567890,
        "tx_errors": 0,
        "rx_errors": 0,
        "tx_dropped": 0,
        "rx_dropped": 0,
        "scan_time": "2024-01-15T10:30:00.000Z",
        "ipv4": {
          "address": "*************",
          "netmask": "*************",
          "broadcast": "*************",
          "gateway": "***********"
        },
        "ipv6": {
          "address": "fe80::20c:29ff:fe12:3456",
          "netmask": "ffff:ffff:ffff:ffff::",
          "gateway": "::"
        }
      }
    ],
    "total_affected_items": 1,
    "total_failed_items": 0,
    "failed_items": []
  },
  "message": "Network information was returned",
  "error": 0
}
```

#### Get Installed Packages
- **URL:** `GET /syscollector/{agent_id}/packages`
- **Description:** Retrieve installed software packages and versions
- **GRCOS Integration:** Used for software inventory and vulnerability correlation

**Example Request:**
```http
GET /v1/syscollector/001/packages?limit=100&search=name=openssl
```

## Data Structures & Schemas

### Wazuh Security Event Schema

```json
{
  "timestamp": "2024-01-15T15:45:30.123Z",
  "rule": {
    "id": 5712,
    "level": 10,
    "description": "Multiple authentication failures",
    "groups": ["authentication_failed", "authentication_failures"],
    "firedtimes": 1,
    "mail": false,
    "pci_dss": ["10.2.4", "10.2.5"],
    "gdpr": ["IV_35.7.d", "IV_32.2"],
    "hipaa": ["164.312.b"],
    "nist_800_53": ["AU.14", "AC.7"],
    "tsc": ["CC6.1", "CC6.8", "CC7.2", "CC7.3"],
    "mitre": {
      "tactic": ["Credential Access"],
      "id": ["T1110"],
      "technique": ["Brute Force"]
    }
  },
  "agent": {
    "id": "001",
    "name": "web-server-01",
    "ip": "*************",
    "groups": ["default", "web-servers"]
  },
  "manager": {
    "name": "wazuh-manager"
  },
  "id": "1642263930.123456",
  "cluster": {
    "name": "wazuh-cluster",
    "node": "worker-01"
  },
  "full_log": "Original log message",
  "decoder": {
    "name": "sshd",
    "parent": "sshd"
  },
  "data": {
    "srcip": "*************",
    "srcport": "22",
    "dstuser": "admin",
    "protocol": "ssh"
  },
  "location": "/var/log/auth.log",
  "previous_output": "Previous related log message"
}
```

**Field Descriptions:**
- `timestamp` (string): ISO 8601 timestamp of the event
- `rule` (object): Security rule that triggered the alert
  - `id` (integer): Unique rule identifier
  - `level` (integer): Severity level (1-15, where 15 is most critical)
  - `description` (string): Human-readable rule description
  - `groups` (array): Rule classification groups
  - `pci_dss`, `gdpr`, `hipaa`, `nist_800_53`, `tsc` (arrays): Compliance framework mappings
  - `mitre` (object): MITRE ATT&CK framework mapping
- `agent` (object): Source agent information
- `manager` (object): Wazuh manager information
- `id` (string): Unique event identifier
- `cluster` (object): Cluster node information
- `full_log` (string): Original log message that triggered the alert
- `decoder` (object): Log decoder information
- `data` (object): Extracted data fields from the log
- `location` (string): Log file path or source

### CmdbType Schema

```json
{
  "public_id": 1,
  "name": "server-type",
  "label": "Server Type",
  "author_id": 1,
  "editor_id": null,
  "creation_time": {"$date": 1640995200000},
  "last_edit_time": null,
  "active": true,
  "selectable_as_parent": true,
  "global_template_ids": [],
  "fields": [
    {
      "type": "text",
      "name": "hostname",
      "label": "Hostname",
      "required": true
    }
  ],
  "version": "1.0.0",
  "description": "Type for server objects",
  "render_meta": {
    "icon": "fas fa-server",
    "sections": []
  }
}
```

### CmdbCategory Schema

```json
{
  "public_id": 1,
  "name": "infrastructure",
  "label": "Infrastructure",
  "parent": null,
  "types": [1, 2, 3],
  "meta": {
    "icon": "fas fa-network-wired",
    "order": 1
  }
}
```

## Collection Parameters

All collection endpoints support the following query parameters for pagination, filtering, and sorting:

### Parameters

- `limit` (integer, default: 10): Maximum number of results to return (0 = unlimited)
- `page` (integer, default: 1): Page number for pagination
- `sort` (string, default: "public_id"): Field to sort by
- `order` (integer, default: 1): Sort order (1 = ascending, -1 = descending)
- `filter` (object): MongoDB-style filter criteria
- `projection` (object, optional): Fields to include/exclude in results

### Example Usage

```http
GET /rest/objects/?limit=20&page=2&sort=creation_time&order=-1&filter={"active":true,"type_id":1}
```

### Filter Examples

**Simple filter:**
```json
{"type_id": 1}
```

**Complex filter with multiple conditions:**
```json
{
  "active": true,
  "type_id": {"$in": [1, 2, 3]},
  "creation_time": {"$gte": {"$date": 1640995200000}}
}
```

**Aggregation pipeline filter:**
```json
[
  {"$match": {"active": true}},
  {"$match": {"type_id": {"$in": [1, 2, 3]}}}
]
```

## Response Structure

### Standard Response Format

All API responses follow a consistent structure:

```json
{
  "response_type": "GET|POST|PUT|DELETE",
  "model": "Object|Type|Category",
  "time": "2022-01-01T00:00:00.000000",
  "result": {},  // For single item responses
  "results": [], // For collection responses
  "count": 10,   // Number of items in current page
  "total": 100,  // Total number of items
  "pager": {
    "page": 1,
    "page_size": 10,
    "total_pages": 10
  },
  "pagination": {
    "current": "http://example.com/rest/objects/?page=1",
    "first": "http://example.com/rest/objects/?page=1",
    "prev": null,
    "next": "http://example.com/rest/objects/?page=2",
    "last": "http://example.com/rest/objects/?page=10"
  }
}
```

### Error Response Format

```json
{
  "status": 400,
  "prefix": "Bad Request",
  "description": "The request could not be understood by the server",
  "message": "Specific error message",
  "joke": "... cause the access was nuts!"
}
```

## HTTP Status Codes

- `200 OK`: Successful GET, PUT, PATCH requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Invalid request data or parameters
- `401 Unauthorized`: Authentication required or invalid token
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side error

## Code Examples

### JavaScript/React Examples

#### Authentication and Setup

```javascript
// Authentication
const login = async (username, password) => {
  const response = await fetch('/rest/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      user_name: username,
      password: password
    })
  });
  
  const data = await response.json();
  localStorage.setItem('access-token', JSON.stringify({
    token: data.token,
    expire: data.token_expire
  }));
  
  return data;
};

// API Client Setup
class DatagerryAPI {
  constructor(baseURL, token) {
    this.baseURL = baseURL;
    this.token = token;
  }
  
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      ...options
    };
    
    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }
}
```

#### Fetching Objects

```javascript
// Get objects with pagination and filtering
const getObjects = async (api, params = {}) => {
  const queryParams = new URLSearchParams();
  
  if (params.limit) queryParams.append('limit', params.limit);
  if (params.page) queryParams.append('page', params.page);
  if (params.sort) queryParams.append('sort', params.sort);
  if (params.order) queryParams.append('order', params.order);
  if (params.filter) queryParams.append('filter', JSON.stringify(params.filter));
  
  const endpoint = `/objects/?${queryParams.toString()}`;
  return api.request(endpoint);
};

// Usage
const api = new DatagerryAPI('/rest', 'your-token-here');

const objects = await getObjects(api, {
  limit: 20,
  page: 1,
  sort: 'creation_time',
  order: -1,
  filter: { type_id: 1, active: true }
});

console.log(`Found ${objects.total} objects`);
objects.results.forEach(obj => {
  console.log(`Object ${obj.public_id}: ${obj.fields.find(f => f.name === 'hostname')?.value}`);
});
```

#### Creating Objects

```javascript
// Create a new object
const createObject = async (api, objectData) => {
  return api.request('/objects/', {
    method: 'POST',
    body: JSON.stringify(objectData)
  });
};

// Usage
const newObject = {
  type_id: 1,
  author_id: 1,
  active: true,
  fields: [
    { name: 'hostname', value: 'server01.example.com' },
    { name: 'ip_address', value: '*************' },
    { name: 'os', value: 'Ubuntu 20.04' }
  ]
};

const result = await createObject(api, newObject);
console.log(`Created object with ID: ${result.raw.public_id}`);
```

#### Updating Objects

```javascript
// Update an existing object
const updateObject = async (api, objectId, updateData) => {
  return api.request(`/objects/${objectId}`, {
    method: 'PUT',
    body: JSON.stringify(updateData)
  });
};

// Usage
const updatedData = {
  ...existingObject,
  fields: [
    { name: 'hostname', value: 'server01-updated.example.com' },
    { name: 'ip_address', value: '*************' }
  ]
};

const result = await updateObject(api, 1, updatedData);
console.log('Object updated successfully');
```

#### Working with Types

```javascript
// Get all types
const getTypes = async (api) => {
  return api.request('/types/');
};

// Create a new type
const createType = async (api, typeData) => {
  return api.request('/types/', {
    method: 'POST',
    body: JSON.stringify(typeData)
  });
};

// Usage
const newType = {
  name: 'network-device',
  label: 'Network Device',
  author_id: 1,
  active: true,
  fields: [
    {
      type: 'text',
      name: 'device_name',
      label: 'Device Name',
      required: true
    },
    {
      type: 'text',
      name: 'ip_address',
      label: 'IP Address',
      required: true
    },
    {
      type: 'select',
      name: 'device_type',
      label: 'Device Type',
      options: ['Router', 'Switch', 'Firewall']
    }
  ],
  render_meta: {
    icon: 'fas fa-network-wired',
    sections: []
  }
};

const typeResult = await createType(api, newType);
console.log(`Created type with ID: ${typeResult.raw.public_id}`);
```

## Best Practices

1. **Always use HTTPS** in production environments
2. **Store tokens securely** and implement token refresh logic
3. **Handle errors gracefully** with proper error handling
4. **Use pagination** for large datasets to avoid performance issues
5. **Implement proper filtering** to reduce data transfer
6. **Cache frequently accessed data** like types and categories
7. **Validate data** before sending to the API
8. **Use appropriate HTTP methods** (GET for reading, POST for creating, PUT/PATCH for updating, DELETE for removing)
9. **Include proper headers** for content type and authorization
10. **Monitor API rate limits** and implement retry logic if needed

## Rate Limiting and Performance

- Use pagination (`limit` and `page` parameters) for large datasets
- Implement client-side caching for static data like types and categories
- Use projection to limit returned fields when full objects aren't needed
- Consider using HEAD requests to get counts without transferring data
- Batch operations when possible to reduce API calls

This documentation provides a comprehensive guide for integrating with DATAGERRY's REST API in your React application. The examples show practical usage patterns that you can adapt to your specific needs.
