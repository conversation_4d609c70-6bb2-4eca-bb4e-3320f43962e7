# Wazuh GRCOS Integration Deployment Guide

## Overview

This comprehensive deployment guide provides step-by-step instructions for deploying the Wazuh SIEM/XDR integration with GRCOS in production environments. The guide covers infrastructure setup, component deployment, configuration management, and operational validation.

## Prerequisites

### Infrastructure Requirements

#### Minimum System Requirements
```yaml
GRCOS Monitor Module:
  CPU: 8 cores (16 threads)
  Memory: 32 GB RAM
  Storage: 500 GB SSD (NVMe preferred)
  Network: 10 Gbps network interface

Wazuh Manager Cluster:
  Master Node:
    CPU: 16 cores (32 threads)
    Memory: 64 GB RAM
    Storage: 1 TB SSD for logs, 500 GB SSD for OS
  Worker Nodes (2+):
    CPU: 12 cores (24 threads)
    Memory: 48 GB RAM
    Storage: 1 TB SSD for logs, 500 GB SSD for OS

Database Infrastructure:
  MongoDB Cluster (3 nodes):
    CPU: 8 cores per node
    Memory: 32 GB RAM per node
    Storage: 2 TB SSD per node
  Redis Cluster (3 nodes):
    CPU: 4 cores per node
    Memory: 16 GB RAM per node
    Storage: 100 GB SSD per node

Kubernetes Cluster:
  Master Nodes (3):
    CPU: 4 cores per node
    Memory: 16 GB RAM per node
    Storage: 200 GB SSD per node
  Worker Nodes (5+):
    CPU: 16 cores per node
    Memory: 64 GB RAM per node
    Storage: 1 TB SSD per node
```

#### Network Requirements
```yaml
Network Segmentation:
  Management Network: 10.0.1.0/24
  Wazuh Cluster Network: 10.0.2.0/24
  GRCOS Platform Network: 10.0.3.0/24
  Database Network: 10.0.4.0/24
  Agent Network: 10.0.5.0/24

Firewall Rules:
  Wazuh Manager API: Port 55000 (HTTPS)
  Wazuh Agent Communication: Port 1514 (TCP)
  MongoDB: Port 27017 (Internal only)
  Redis: Port 6379 (Internal only)
  Kubernetes API: Port 6443 (HTTPS)
  GRCOS API: Port 8080 (HTTPS)

SSL/TLS Requirements:
  - Valid SSL certificates for all external endpoints
  - Internal certificate authority for cluster communication
  - Certificate rotation automation
```

### Software Dependencies

#### Required Software Versions
```yaml
Operating System:
  - Ubuntu 22.04 LTS (recommended)
  - CentOS 8 / RHEL 8
  - Amazon Linux 2

Container Runtime:
  - Docker 24.0+
  - containerd 1.7+

Orchestration:
  - Kubernetes 1.28+
  - Helm 3.12+

Databases:
  - MongoDB 7.0+
  - Redis 7.0+
  - ClickHouse 23.8+ (for analytics)

Monitoring:
  - Prometheus 2.45+
  - Grafana 10.0+
  - Jaeger 1.47+ (for tracing)
```

## Deployment Architecture

### Production Deployment Topology

```mermaid
graph TB
    subgraph "Load Balancer Tier"
        A[External Load Balancer]
        B[Internal Load Balancer]
    end
    
    subgraph "Application Tier"
        C[GRCOS Monitor Pod 1]
        D[GRCOS Monitor Pod 2]
        E[GRCOS Monitor Pod 3]
        F[Wazuh Integration Service 1]
        G[Wazuh Integration Service 2]
    end
    
    subgraph "Wazuh Cluster"
        H[Wazuh Master]
        I[Wazuh Worker 1]
        J[Wazuh Worker 2]
    end
    
    subgraph "Data Tier"
        K[MongoDB Primary]
        L[MongoDB Secondary 1]
        M[MongoDB Secondary 2]
        N[Redis Master]
        O[Redis Replica 1]
        P[Redis Replica 2]
    end
    
    subgraph "Storage Tier"
        Q[Persistent Volumes]
        R[Backup Storage]
        S[Archive Storage]
    end
    
    A --> C
    A --> D
    A --> E
    B --> F
    B --> G
    
    C --> H
    D --> I
    E --> J
    
    F --> K
    G --> L
    
    K --> Q
    L --> Q
    M --> Q
    
    R --> S
```

## Step-by-Step Deployment

### Phase 1: Infrastructure Preparation

#### 1.1 Kubernetes Cluster Setup

```bash
#!/bin/bash
# Kubernetes cluster initialization script

# Install required packages
sudo apt-get update
sudo apt-get install -y apt-transport-https ca-certificates curl

# Add Kubernetes repository
curl -fsSL https://packages.cloud.google.com/apt/doc/apt-key.gpg | sudo apt-key add -
echo "deb https://apt.kubernetes.io/ kubernetes-xenial main" | sudo tee /etc/apt/sources.list.d/kubernetes.list

# Install Kubernetes components
sudo apt-get update
sudo apt-get install -y kubelet=1.28.2-00 kubeadm=1.28.2-00 kubectl=1.28.2-00
sudo apt-mark hold kubelet kubeadm kubectl

# Initialize cluster (on master node)
sudo kubeadm init --pod-network-cidr=**********/16 --service-cidr=*********/12

# Configure kubectl
mkdir -p $HOME/.kube
sudo cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
sudo chown $(id -u):$(id -g) $HOME/.kube/config

# Install CNI plugin (Flannel)
kubectl apply -f https://raw.githubusercontent.com/flannel-io/flannel/master/Documentation/kube-flannel.yml

# Install Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash
```

#### 1.2 Database Cluster Setup

```yaml
# MongoDB deployment configuration
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb-cluster
  namespace: grcos-data
spec:
  serviceName: mongodb-service
  replicas: 3
  selector:
    matchLabels:
      app: mongodb
  template:
    metadata:
      labels:
        app: mongodb
    spec:
      containers:
      - name: mongodb
        image: mongo:7.0
        ports:
        - containerPort: 27017
        env:
        - name: MONGO_INITDB_ROOT_USERNAME
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: username
        - name: MONGO_INITDB_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: password
        volumeMounts:
        - name: mongodb-storage
          mountPath: /data/db
        - name: mongodb-config
          mountPath: /etc/mongo
        resources:
          requests:
            memory: "32Gi"
            cpu: "8"
          limits:
            memory: "32Gi"
            cpu: "8"
  volumeClaimTemplates:
  - metadata:
      name: mongodb-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 2Ti
```

#### 1.3 Wazuh Cluster Deployment

```yaml
# Wazuh Manager deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wazuh-manager
  namespace: wazuh-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wazuh-manager
  template:
    metadata:
      labels:
        app: wazuh-manager
    spec:
      containers:
      - name: wazuh-manager
        image: wazuh/wazuh-manager:4.7.0
        ports:
        - containerPort: 1514
        - containerPort: 1515
        - containerPort: 514
        - containerPort: 55000
        env:
        - name: WAZUH_MANAGER_CONF
          valueFrom:
            configMapKeyRef:
              name: wazuh-manager-config
              key: ossec.conf
        volumeMounts:
        - name: wazuh-manager-data
          mountPath: /var/ossec/data
        - name: wazuh-manager-logs
          mountPath: /var/ossec/logs
        - name: wazuh-manager-etc
          mountPath: /var/ossec/etc
        resources:
          requests:
            memory: "16Gi"
            cpu: "8"
          limits:
            memory: "16Gi"
            cpu: "8"
      volumes:
      - name: wazuh-manager-data
        persistentVolumeClaim:
          claimName: wazuh-manager-data-pvc
      - name: wazuh-manager-logs
        persistentVolumeClaim:
          claimName: wazuh-manager-logs-pvc
      - name: wazuh-manager-etc
        configMap:
          name: wazuh-manager-config
```

### Phase 2: GRCOS Integration Deployment

#### 2.1 GRCOS Monitor Module Deployment

```yaml
# GRCOS Monitor Module deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grcos-monitor
  namespace: grcos-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: grcos-monitor
  template:
    metadata:
      labels:
        app: grcos-monitor
    spec:
      containers:
      - name: grcos-monitor
        image: grcos/monitor:2.0.0
        ports:
        - containerPort: 8080
        - containerPort: 8443
        env:
        - name: WAZUH_API_URL
          value: "https://wazuh-manager.wazuh-system.svc.cluster.local:55000"
        - name: WAZUH_API_USER
          valueFrom:
            secretKeyRef:
              name: wazuh-api-secret
              key: username
        - name: WAZUH_API_PASSWORD
          valueFrom:
            secretKeyRef:
              name: wazuh-api-secret
              key: password
        - name: MONGODB_URL
          valueFrom:
            secretKeyRef:
              name: mongodb-secret
              key: connection_string
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: connection_string
        volumeMounts:
        - name: grcos-config
          mountPath: /app/config
        - name: grcos-logs
          mountPath: /app/logs
        resources:
          requests:
            memory: "8Gi"
            cpu: "4"
          limits:
            memory: "8Gi"
            cpu: "4"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: grcos-config
        configMap:
          name: grcos-monitor-config
      - name: grcos-logs
        emptyDir: {}
```

#### 2.2 Wazuh Integration Service Deployment

```yaml
# Wazuh Integration Service
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wazuh-integration-service
  namespace: grcos-platform
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wazuh-integration
  template:
    metadata:
      labels:
        app: wazuh-integration
    spec:
      containers:
      - name: wazuh-integration
        image: grcos/wazuh-integration:1.0.0
        ports:
        - containerPort: 8081
        env:
        - name: WAZUH_MANAGER_URL
          value: "https://wazuh-manager.wazuh-system.svc.cluster.local:55000"
        - name: GRCOS_MONITOR_URL
          value: "http://grcos-monitor.grcos-platform.svc.cluster.local:8080"
        - name: EVENT_STREAM_BUFFER_SIZE
          value: "10000"
        - name: PROCESSING_WORKERS
          value: "8"
        volumeMounts:
        - name: integration-config
          mountPath: /app/config
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "4Gi"
            cpu: "2"
      volumes:
      - name: integration-config
        configMap:
          name: wazuh-integration-config
```

### Phase 3: Configuration Management

#### 3.1 Wazuh Manager Configuration

```xml
<!-- Wazuh Manager Configuration (ossec.conf) -->
<ossec_config>
  <global>
    <jsonout_output>yes</jsonout_output>
    <alerts_log>yes</alerts_log>
    <logall>no</logall>
    <logall_json>no</logall_json>
    <email_notification>no</email_notification>
    <smtp_server>localhost</smtp_server>
    <email_from><EMAIL></email_from>
    <email_to><EMAIL></email_to>
    <hostname>wazuh-manager</hostname>
    <stats>8</stats>
  </global>

  <alerts>
    <log_alert_level>3</log_alert_level>
    <email_alert_level>12</email_alert_level>
  </alerts>

  <remote>
    <connection>secure</connection>
    <port>1514</port>
    <protocol>tcp</protocol>
    <queue_size>131072</queue_size>
  </remote>

  <auth>
    <disabled>no</disabled>
    <port>1515</port>
    <use_source_ip>no</use_source_ip>
    <force_insert>no</force_insert>
    <force_time>0</force_time>
    <purge>no</purge>
    <use_password>no</use_password>
    <ssl_verify_host>no</ssl_verify_host>
    <ssl_manager_cert>/var/ossec/etc/sslmanager.cert</ssl_manager_cert>
    <ssl_manager_key>/var/ossec/etc/sslmanager.key</ssl_manager_key>
    <ssl_auto_negotiate>no</ssl_auto_negotiate>
  </auth>

  <cluster>
    <name>wazuh-cluster</name>
    <node_name>wazuh-master</node_name>
    <node_type>master</node_type>
    <key>c98b62a9b6169ac5f67dae55ae4a9088</key>
    <port>1516</port>
    <bind_addr>0.0.0.0</bind_addr>
    <nodes>
        <node>wazuh-master</node>
        <node>wazuh-worker-1</node>
        <node>wazuh-worker-2</node>
    </nodes>
    <hidden>no</hidden>
    <disabled>no</disabled>
  </cluster>

  <api>
    <host>0.0.0.0</host>
    <port>55000</port>
    <https>yes</https>
    <https_cert>/var/ossec/api/configuration/ssl/server.crt</https_cert>
    <https_key>/var/ossec/api/configuration/ssl/server.key</https_key>
    <logging_level>info</logging_level>
    <logging_path>logs/api.log</logging_path>
    <cors>
      <enabled>yes</enabled>
      <source_route>*</source_route>
      <expose_headers>*</expose_headers>
      <allow_headers>*</allow_headers>
      <allow_credentials>no</allow_credentials>
    </cors>
    <cache>
      <enabled>yes</enabled>
      <time>0.750</time>
    </cache>
    <access>
      <max_login_attempts>50</max_login_attempts>
      <block_time>300</block_time>
      <max_request_per_minute>300</max_request_per_minute>
    </access>
    <drop_privileges>yes</drop_privileges>
    <experimental_features>no</experimental_features>
  </api>

  <integration>
    <name>grcos</name>
    <hook_url>http://wazuh-integration-service.grcos-platform.svc.cluster.local:8081/webhook</hook_url>
    <level>7</level>
    <rule_id>100001,100002,100003</rule_id>
    <alert_format>json</alert_format>
  </integration>
</ossec_config>
```

#### 3.2 GRCOS Monitor Configuration

```yaml
# GRCOS Monitor Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: grcos-monitor-config
  namespace: grcos-platform
data:
  application.yml: |
    grcos:
      monitor:
        wazuh:
          api:
            url: ${WAZUH_API_URL}
            username: ${WAZUH_API_USER}
            password: ${WAZUH_API_PASSWORD}
            timeout: 30s
            retry_attempts: 3
            connection_pool_size: 10
          event_streaming:
            enabled: true
            buffer_size: 10000
            batch_size: 100
            flush_interval: 1s
            max_latency: 100ms
          agent_monitoring:
            enabled: true
            sync_interval: 5m
            health_check_interval: 1m
        ai_agents:
          threat_detection:
            enabled: true
            model_version: "v2.1"
            confidence_threshold: 0.7
          incident_response:
            enabled: true
            auto_escalation: true
            max_response_time: 30s
          compliance_monitoring:
            enabled: true
            frameworks: ["NIST_800_53", "PCI_DSS", "SOX"]
        database:
          mongodb:
            url: ${MONGODB_URL}
            database: grcos_monitor
            connection_timeout: 10s
            socket_timeout: 30s
            max_pool_size: 100
          redis:
            url: ${REDIS_URL}
            timeout: 5s
            max_connections: 50
        security:
          encryption:
            enabled: true
            algorithm: AES-256-GCM
          authentication:
            jwt_secret: ${JWT_SECRET}
            token_expiry: 1h
        logging:
          level: INFO
          format: json
          output: stdout
        metrics:
          enabled: true
          port: 9090
          path: /metrics
```

### Phase 4: Deployment Validation

#### 4.1 Health Check Script

```bash
#!/bin/bash
# GRCOS Wazuh Integration Health Check Script

echo "=== GRCOS Wazuh Integration Health Check ==="

# Check Kubernetes cluster status
echo "Checking Kubernetes cluster..."
kubectl cluster-info
if [ $? -ne 0 ]; then
    echo "ERROR: Kubernetes cluster not accessible"
    exit 1
fi

# Check Wazuh Manager status
echo "Checking Wazuh Manager..."
kubectl get pods -n wazuh-system -l app=wazuh-manager
WAZUH_POD=$(kubectl get pods -n wazuh-system -l app=wazuh-manager -o jsonpath='{.items[0].metadata.name}')
kubectl exec -n wazuh-system $WAZUH_POD -- /var/ossec/bin/wazuh-control status
if [ $? -ne 0 ]; then
    echo "ERROR: Wazuh Manager not running properly"
    exit 1
fi

# Check GRCOS Monitor status
echo "Checking GRCOS Monitor..."
kubectl get pods -n grcos-platform -l app=grcos-monitor
MONITOR_POD=$(kubectl get pods -n grcos-platform -l app=grcos-monitor -o jsonpath='{.items[0].metadata.name}')
kubectl exec -n grcos-platform $MONITOR_POD -- curl -f http://localhost:8080/health
if [ $? -ne 0 ]; then
    echo "ERROR: GRCOS Monitor health check failed"
    exit 1
fi

# Check Wazuh Integration Service
echo "Checking Wazuh Integration Service..."
kubectl get pods -n grcos-platform -l app=wazuh-integration
INTEGRATION_POD=$(kubectl get pods -n grcos-platform -l app=wazuh-integration -o jsonpath='{.items[0].metadata.name}')
kubectl exec -n grcos-platform $INTEGRATION_POD -- curl -f http://localhost:8081/health
if [ $? -ne 0 ]; then
    echo "ERROR: Wazuh Integration Service health check failed"
    exit 1
fi

# Test Wazuh API connectivity
echo "Testing Wazuh API connectivity..."
WAZUH_API_URL="https://wazuh-manager.wazuh-system.svc.cluster.local:55000"
kubectl exec -n grcos-platform $MONITOR_POD -- curl -k -f $WAZUH_API_URL/
if [ $? -ne 0 ]; then
    echo "ERROR: Cannot connect to Wazuh API"
    exit 1
fi

# Test event streaming
echo "Testing event streaming..."
kubectl logs -n grcos-platform $INTEGRATION_POD --tail=10 | grep "Event stream connected"
if [ $? -ne 0 ]; then
    echo "WARNING: Event streaming may not be working properly"
fi

# Check database connectivity
echo "Checking database connectivity..."
kubectl exec -n grcos-platform $MONITOR_POD -- curl -f http://localhost:8080/health/database
if [ $? -ne 0 ]; then
    echo "ERROR: Database connectivity issues"
    exit 1
fi

echo "=== Health Check Completed Successfully ==="
```

#### 4.2 Performance Validation

```bash
#!/bin/bash
# Performance validation script

echo "=== Performance Validation ==="

# Test event processing throughput
echo "Testing event processing throughput..."
kubectl exec -n grcos-platform $MONITOR_POD -- curl -s http://localhost:9090/metrics | grep grcos_events_processed_total
kubectl exec -n grcos-platform $MONITOR_POD -- curl -s http://localhost:9090/metrics | grep grcos_event_processing_duration

# Test API response times
echo "Testing API response times..."
for i in {1..10}; do
    kubectl exec -n grcos-platform $MONITOR_POD -- curl -w "%{time_total}\n" -o /dev/null -s http://localhost:8080/api/v1/status
done

# Test memory and CPU usage
echo "Checking resource usage..."
kubectl top pods -n grcos-platform
kubectl top pods -n wazuh-system

echo "=== Performance Validation Completed ==="
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS DevOps Team
