---
test_name: PUT ACTIVE-RESPONSE OVER A LIST OF AGENTS

stages:

  - name: Runs an Active Response command on a specified agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "!custom"
      params:
        agents_list: '001'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '001'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Send a message to an agent (Status=Active) (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "restart-wazuh0"
        arguments: ["-", "null", "(from_the_server)", "(no_rule_id)"]
      params:
        agents_list: '002'
    response: &permission_denied
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

  - name: Send a message to a list of agents
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "!custom"
      params:
        agents_list: 002,004,005,007,010,011
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '004'
            - '007'
          failed_items:
            - error:
                code: 1707
              id:
                - '011'
            - error:
                code: 4000
              id:
                - '002'
                - '005'
                - '010'
          total_affected_items: 2
          total_failed_items: 4

  - name: Try to send a message to an agent (status=disconnected/never_connected) (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "restart-wazuh0"
        arguments: ["-", "null", "(from_the_server)", "(no_rule_id)"]
      params:
        agents_list: '009'
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1707
              id:
                - '009'
          total_affected_items: 0
          total_failed_items: 1

  - name: Try to send a message to an unexisting agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "restart-wazuh0"
        arguments: ["-", "null", "(from_the_server)", "(no_rule_id)"]
      params:
        agents_list: 251,252
    response:
      <<: *permission_denied

---
test_name: PUT /active-response

stages:

    # PUT /active-response
  - name: Runs an Active Response command on all agents
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "restart-wazuh0"
        arguments: ["-", "null", "(from_the_server)", "(no_rule_id)"]
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 1707
              id:
                - '009'
                - '011'
            - error:
                code: 1750
              id:
                - '003'
          total_affected_items: 3
          total_failed_items: 3
