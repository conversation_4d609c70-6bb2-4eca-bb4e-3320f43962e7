# USE THIS FILE AS A TEMPLATE. UNCOMMENT LINES TO APPLY CUSTOM CONFIGURATION

# host: ['0.0.0.0', '::']
# port: 55000

# Advanced configuration

# https:
#  enabled: yes
#  key: "server.key"
#  cert: "server.crt"
#  use_ca: False
#  ca: "ca.crt"
#  ssl_protocol: "auto"
#  ssl_ciphers: ""

# Modify API's intervals (time in seconds)
# intervals:
#   request_timeout: 10

# Logging configuration
# Values for API log level: disabled, info, warning, error, debug, debug2 (each level includes the previous level).
# Values for API log max_size: <value><unit>. Valid units: K (kilobytes), M (megabytes)
# Enabling the API log max_size will disable the time based rotation (on midnight)
# logs:
#  level: "info"
#  format: "plain"
#  max_size:
#    enabled: False
#    size: "1M"

# Cross-origin resource sharing: https://www.starlette.io/middleware/#corsmiddleware
# cors:
#  enabled: no
#  source_route: "*"
#  expose_headers: "*"
#  allow_headers: "*"
#  allow_credentials: no

# Access parameters
# access:
#  max_login_attempts: 50
#  block_time: 300
#  max_request_per_minute: 300

# Drop privileges (Run as wazuh user)
# drop_privileges: yes

# Enable features under development
# experimental_features: no

# Maximum body size that the API can accept, in bytes (0 -> limitless)
# max_upload_size: 10485760

# Number of processes dedicated to processing authentication requests.
# authentication_pool_size: 2

# Uploadable Wazuh configuration sections
# upload_configuration:
#   remote_commands:
#     localfile:
#       allow: yes
#       exceptions: []
#     wodle_command:
#       allow: yes
#       exceptions: []
#   limits:
#     eps:
#       allow: yes
#   agents:
#     allow_higher_versions:
#       allow: yes
#   indexer:
#     allow: yes
#   integrations:
#     virustotal:
#       public_key:
#         allow: yes
#         minimum_quota: 240
