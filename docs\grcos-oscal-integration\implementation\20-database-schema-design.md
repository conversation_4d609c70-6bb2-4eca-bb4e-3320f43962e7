# GRCOS OSCAL Database Schema Design

## Overview

This document defines the comprehensive database schema design for OSCAL document storage and management within the GRCOS platform. It covers MongoDB document structures, indexing strategies, data relationships, and performance optimization patterns for handling large-scale OSCAL deployments.

## Database Architecture

### Multi-Database Strategy

#### Database Allocation
```
Primary Databases:
- grcos_oscal_documents: Core OSCAL document storage
- grcos_oscal_metadata: Document metadata and relationships
- grcos_oscal_cache: Frequently accessed data cache
- grcos_oscal_audit: Audit trails and change history
- grcos_oscal_analytics: Analytics and reporting data
```

#### MongoDB Collections Structure
```javascript
// Database: grcos_oscal_documents
{
  "oscal_catalogs": "OSCAL Catalog documents",
  "oscal_profiles": "OSCAL Profile documents", 
  "oscal_components": "Component Definition documents",
  "oscal_systems": "System Security Plan documents",
  "oscal_assessments": "Assessment Plan and Results",
  "oscal_poams": "Plan of Action and Milestones",
  "oscal_evidence": "Evidence and back-matter resources"
}

// Database: grcos_oscal_metadata
{
  "document_metadata": "Document metadata and properties",
  "document_relationships": "Cross-document relationships",
  "control_mappings": "Control implementation mappings",
  "blockchain_registry": "Blockchain verification records"
}
```

## OSCAL Document Schemas

### Catalog Collection Schema

#### MongoDB Document Structure
```javascript
// Collection: oscal_catalogs
{
  "_id": ObjectId("..."),
  "uuid": "550e8400-e29b-41d4-a716-************",
  "document_type": "catalog",
  "oscal_version": "1.1.3",
  "metadata": {
    "title": "NIST SP 800-53 Rev 5",
    "version": "5.1.1",
    "oscal_version": "1.1.3",
    "published": ISODate("2020-09-23T00:00:00Z"),
    "last_modified": ISODate("2024-01-15T10:30:00Z"),
    "props": [
      {
        "name": "framework-type",
        "value": "security"
      }
    ],
    "responsible_parties": [
      {
        "role_id": "publisher",
        "party_uuids": ["party-nist"]
      }
    ]
  },
  "groups": [
    {
      "id": "ac",
      "title": "Access Control",
      "controls": [
        {
          "id": "ac-1",
          "title": "Policy and Procedures",
          "parts": [...],
          "props": [...],
          "links": [...]
        }
      ]
    }
  ],
  "controls": [...],
  "back_matter": {...},
  
  // GRCOS-specific fields
  "grcos_metadata": {
    "created_at": ISODate("2024-01-15T10:30:00Z"),
    "updated_at": ISODate("2024-01-15T10:30:00Z"),
    "created_by": "grcos-compliance-agent",
    "organization_id": "org-001",
    "environment": "production",
    "status": "active"
  },
  "blockchain_verification": {
    "content_hash": "sha256:abc123def456...",
    "blockchain_tx_id": "tx789012345",
    "verified_at": ISODate("2024-01-15T10:35:00Z"),
    "verification_status": "verified"
  },
  "search_index": {
    "control_ids": ["ac-1", "ac-2", "ac-3", ...],
    "keywords": ["access", "control", "policy", "procedures", ...],
    "framework_families": ["ac", "au", "ca", ...]
  }
}
```

### System Security Plan Collection Schema

#### SSP Document Structure
```javascript
// Collection: oscal_systems
{
  "_id": ObjectId("..."),
  "uuid": "ssp-production-001",
  "document_type": "system-security-plan",
  "oscal_version": "1.1.3",
  "metadata": {
    "title": "Production Web Application SSP",
    "version": "2.1.0",
    "last_modified": ISODate("2024-01-15T14:30:00Z"),
    "props": [
      {
        "name": "system-type",
        "value": "production"
      },
      {
        "name": "authorization-boundary",
        "value": "system-boundary-001"
      }
    ]
  },
  "import_profile": {
    "href": "#grcos-high-baseline",
    "resolved_profile_uuid": "profile-grcos-high"
  },
  "system_characteristics": {
    "system_ids": [
      {
        "identifier_type": "grcos",
        "id": "PROD-SYS-001"
      }
    ],
    "system_name": "Production Web Application",
    "description": "Customer-facing web application with database backend",
    "security_sensitivity_level": "high",
    "system_information": {
      "information_types": [...]
    },
    "security_impact_level": {
      "security_objective_confidentiality": "moderate",
      "security_objective_integrity": "high", 
      "security_objective_availability": "moderate"
    },
    "status": {
      "state": "operational"
    },
    "authorization_boundary": {
      "description": "System authorization boundary includes...",
      "diagrams": [...]
    }
  },
  "system_implementation": {
    "users": [...],
    "components": [
      {
        "uuid": "comp-001-web-server",
        "type": "hardware",
        "title": "Production Web Server",
        "description": "Primary web application server",
        "status": {
          "state": "operational"
        },
        "props": [
          {
            "name": "asset-type",
            "value": "IT"
          },
          {
            "name": "criticality",
            "value": "high"
          }
        ]
      }
    ],
    "inventory_items": [...],
    "leveraged_authorizations": [...]
  },
  "control_implementation": {
    "description": "Implementation of security controls",
    "implemented_requirements": [
      {
        "uuid": "req-ac-1",
        "control_id": "ac-1",
        "statements": [
          {
            "statement_id": "ac-1_stmt.a",
            "uuid": "stmt-ac-1-a",
            "description": "Access control policy documented and approved",
            "implementation_status": {
              "state": "implemented"
            },
            "by_components": [
              {
                "component_uuid": "comp-001-web-server",
                "uuid": "impl-ac-1-web",
                "description": "Web server implements access control policy",
                "implementation_status": {
                  "state": "implemented"
                }
              }
            ]
          }
        ]
      }
    ]
  },
  
  // GRCOS-specific fields
  "grcos_metadata": {
    "created_at": ISODate("2024-01-10T09:00:00Z"),
    "updated_at": ISODate("2024-01-15T14:30:00Z"),
    "created_by": "system-administrator",
    "organization_id": "org-001",
    "environment": "production",
    "system_owner": "infrastructure-team",
    "status": "active",
    "compliance_status": {
      "overall_score": 91.5,
      "last_assessed": ISODate("2024-01-15T10:00:00Z"),
      "next_assessment": ISODate("2024-04-15T10:00:00Z"),
      "risk_level": "medium"
    }
  },
  "implementation_summary": {
    "total_controls": 156,
    "implemented": 142,
    "partially_implemented": 12,
    "planned": 2,
    "not_applicable": 0,
    "implementation_percentage": 91.0
  }
}
```

### Assessment Results Collection Schema

#### Assessment Results Structure
```javascript
// Collection: oscal_assessments
{
  "_id": ObjectId("..."),
  "uuid": "ar-q1-2024-production",
  "document_type": "assessment-results",
  "oscal_version": "1.1.3",
  "metadata": {
    "title": "Q1 2024 Production System Assessment Results",
    "version": "1.0.0",
    "last_modified": ISODate("2024-01-22T16:30:00Z"),
    "props": [
      {
        "name": "assessment-completion",
        "value": "100"
      },
      {
        "name": "overall-score", 
        "value": "85"
      }
    ]
  },
  "import_ap": {
    "href": "#ap-q1-2024-production",
    "assessment_plan_uuid": "ap-q1-2024-production"
  },
  "results": [
    {
      "uuid": "result-001",
      "title": "Production System Assessment Results",
      "description": "Comprehensive assessment results",
      "start": ISODate("2024-01-20T09:00:00Z"),
      "end": ISODate("2024-01-22T17:00:00Z"),
      "reviewed_controls": {
        "control_selections": [...]
      },
      "observations": [
        {
          "uuid": "obs-001",
          "title": "Account Management Review",
          "description": "Review of user account management procedures",
          "methods": ["examine", "test"],
          "types": ["control-objective"],
          "subjects": [...],
          "collected": ISODate("2024-01-21T10:00:00Z"),
          "relevant_evidence": [...]
        }
      ],
      "findings": [
        {
          "uuid": "finding-001",
          "title": "Inactive Account Management",
          "description": "Inactive user accounts not disabled within required timeframe",
          "target": {
            "type": "objective-id",
            "target_id": "ac-2"
          },
          "implementation_statement_uuid": "impl-ac-2-web",
          "related_observations": [
            {
              "observation_uuid": "obs-001"
            }
          ]
        }
      ],
      "risks": [...]
    }
  ],
  
  // GRCOS-specific fields
  "grcos_metadata": {
    "created_at": ISODate("2024-01-22T16:30:00Z"),
    "assessment_type": "quarterly",
    "automated_percentage": 75,
    "manual_percentage": 25,
    "assessor_team": ["security-team", "external-assessor"],
    "system_uuid": "ssp-production-001"
  },
  "assessment_summary": {
    "total_controls_assessed": 156,
    "controls_passed": 132,
    "controls_failed": 24,
    "overall_score": 85,
    "findings_by_severity": {
      "critical": 0,
      "high": 2,
      "medium": 5,
      "low": 17
    },
    "risk_score": 3.2
  }
}
```

## Indexing Strategy

### Primary Indexes

#### Document-Level Indexes
```javascript
// Catalog collection indexes
db.oscal_catalogs.createIndex({"uuid": 1}, {unique: true})
db.oscal_catalogs.createIndex({"document_type": 1, "grcos_metadata.status": 1})
db.oscal_catalogs.createIndex({"metadata.title": "text", "metadata.description": "text"})
db.oscal_catalogs.createIndex({"search_index.control_ids": 1})
db.oscal_catalogs.createIndex({"search_index.framework_families": 1})
db.oscal_catalogs.createIndex({"grcos_metadata.organization_id": 1, "grcos_metadata.environment": 1})

// System collection indexes  
db.oscal_systems.createIndex({"uuid": 1}, {unique: true})
db.oscal_systems.createIndex({"system_characteristics.system_ids.id": 1})
db.oscal_systems.createIndex({"grcos_metadata.compliance_status.overall_score": -1})
db.oscal_systems.createIndex({"grcos_metadata.compliance_status.last_assessed": -1})
db.oscal_systems.createIndex({"control_implementation.implemented_requirements.control_id": 1})

// Assessment collection indexes
db.oscal_assessments.createIndex({"uuid": 1}, {unique: true})
db.oscal_assessments.createIndex({"grcos_metadata.system_uuid": 1, "metadata.last_modified": -1})
db.oscal_assessments.createIndex({"results.findings.target.target_id": 1})
db.oscal_assessments.createIndex({"assessment_summary.overall_score": -1})
db.oscal_assessments.createIndex({"assessment_summary.findings_by_severity.critical": -1})
```

#### Compound Indexes for Complex Queries
```javascript
// Multi-field compound indexes
db.oscal_systems.createIndex({
  "grcos_metadata.organization_id": 1,
  "grcos_metadata.environment": 1,
  "grcos_metadata.status": 1,
  "grcos_metadata.compliance_status.overall_score": -1
})

db.oscal_assessments.createIndex({
  "grcos_metadata.system_uuid": 1,
  "assessment_summary.overall_score": -1,
  "metadata.last_modified": -1
})

// Text search indexes
db.oscal_catalogs.createIndex({
  "metadata.title": "text",
  "metadata.description": "text", 
  "groups.title": "text",
  "groups.controls.title": "text",
  "groups.controls.parts.prose": "text"
}, {
  weights: {
    "metadata.title": 10,
    "groups.controls.title": 5,
    "groups.controls.parts.prose": 1
  }
})
```

## Data Relationships and References

### Cross-Document Relationships

#### Relationship Tracking Schema
```javascript
// Collection: document_relationships
{
  "_id": ObjectId("..."),
  "source_document": {
    "uuid": "ssp-production-001",
    "type": "system-security-plan"
  },
  "target_document": {
    "uuid": "grcos-high-baseline", 
    "type": "profile"
  },
  "relationship_type": "imports",
  "relationship_details": {
    "import_type": "profile",
    "href": "#grcos-high-baseline"
  },
  "created_at": ISODate("2024-01-15T10:30:00Z"),
  "status": "active"
}

// Index for relationship queries
db.document_relationships.createIndex({"source_document.uuid": 1, "relationship_type": 1})
db.document_relationships.createIndex({"target_document.uuid": 1, "relationship_type": 1})
db.document_relationships.createIndex({"source_document.type": 1, "target_document.type": 1})
```

#### Control Implementation Mapping
```javascript
// Collection: control_mappings
{
  "_id": ObjectId("..."),
  "control_id": "ac-2",
  "catalog_uuid": "nist-800-53-rev5",
  "implementations": [
    {
      "system_uuid": "ssp-production-001",
      "implementation_uuid": "req-ac-2",
      "implementation_status": "implemented",
      "components": [
        {
          "component_uuid": "comp-001-web-server",
          "implementation_uuid": "impl-ac-2-web",
          "status": "implemented"
        }
      ],
      "last_assessed": ISODate("2024-01-15T10:00:00Z"),
      "assessment_result": "passed"
    }
  ],
  "updated_at": ISODate("2024-01-15T14:30:00Z")
}

// Indexes for control mapping queries
db.control_mappings.createIndex({"control_id": 1})
db.control_mappings.createIndex({"implementations.system_uuid": 1})
db.control_mappings.createIndex({"implementations.implementation_status": 1})
```

## Performance Optimization

### Query Optimization Patterns

#### Aggregation Pipeline Examples
```javascript
// Get compliance summary across all systems
db.oscal_systems.aggregate([
  {
    $match: {
      "grcos_metadata.status": "active",
      "grcos_metadata.environment": "production"
    }
  },
  {
    $group: {
      _id: "$grcos_metadata.organization_id",
      total_systems: {$sum: 1},
      avg_compliance_score: {$avg: "$grcos_metadata.compliance_status.overall_score"},
      systems: {
        $push: {
          uuid: "$uuid",
          title: "$metadata.title",
          compliance_score: "$grcos_metadata.compliance_status.overall_score"
        }
      }
    }
  },
  {
    $sort: {"avg_compliance_score": -1}
  }
])

// Get recent findings across systems
db.oscal_assessments.aggregate([
  {
    $match: {
      "metadata.last_modified": {
        $gte: ISODate("2024-01-01T00:00:00Z")
      }
    }
  },
  {
    $unwind: "$results"
  },
  {
    $unwind: "$results.findings"
  },
  {
    $group: {
      _id: "$results.findings.target.target_id",
      finding_count: {$sum: 1},
      systems_affected: {$addToSet: "$grcos_metadata.system_uuid"},
      latest_finding: {$max: "$metadata.last_modified"}
    }
  },
  {
    $sort: {"finding_count": -1}
  }
])
```

### Caching Strategy

#### Redis Cache Schema
```javascript
// Cache key patterns
{
  "oscal:catalog:{uuid}": "Cached catalog document",
  "oscal:system:{uuid}:summary": "Cached system compliance summary", 
  "oscal:control:{control_id}:implementations": "Cached control implementations",
  "oscal:assessment:{uuid}:summary": "Cached assessment summary",
  "oscal:search:{query_hash}": "Cached search results"
}

// Cache TTL configuration
{
  "catalog_documents": 3600,      // 1 hour
  "system_summaries": 1800,       // 30 minutes  
  "assessment_results": 900,      // 15 minutes
  "search_results": 300,          // 5 minutes
  "control_mappings": 1800        // 30 minutes
}
```

## Data Archival and Retention

### Archival Strategy

#### Document Lifecycle Management
```javascript
// Collection: document_lifecycle
{
  "_id": ObjectId("..."),
  "document_uuid": "ar-q4-2023-production",
  "document_type": "assessment-results",
  "lifecycle_stage": "archived",
  "retention_policy": "7-years",
  "archived_at": ISODate("2024-01-15T00:00:00Z"),
  "archive_location": "s3://grcos-archive/2024/assessments/",
  "disposal_date": ISODate("2031-01-15T00:00:00Z"),
  "legal_hold": false
}

// Archival process
function archiveOldDocuments() {
  const cutoffDate = new Date();
  cutoffDate.setFullYear(cutoffDate.getFullYear() - 1);
  
  const documentsToArchive = db.oscal_assessments.find({
    "metadata.last_modified": {$lt: cutoffDate},
    "grcos_metadata.status": "active"
  });
  
  documentsToArchive.forEach(doc => {
    // Move to archive collection
    db.oscal_assessments_archive.insertOne(doc);
    
    // Update lifecycle tracking
    db.document_lifecycle.insertOne({
      document_uuid: doc.uuid,
      document_type: doc.document_type,
      lifecycle_stage: "archived",
      archived_at: new Date()
    });
    
    // Remove from active collection
    db.oscal_assessments.deleteOne({_id: doc._id});
  });
}
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Database Architecture Team
