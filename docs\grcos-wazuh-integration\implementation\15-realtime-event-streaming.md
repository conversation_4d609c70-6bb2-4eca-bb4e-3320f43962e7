# Real-time Event Streaming Implementation

## Overview

This document provides comprehensive implementation guidance for real-time event streaming between Wazuh SIEM/XDR and GRCOS. The implementation ensures sub-second event delivery, high throughput processing, and reliable event handling for critical security operations.

## Streaming Architecture

### Event Streaming Infrastructure

```mermaid
graph TB
    subgraph "Wazuh Manager"
        A[Event Generator]
        B[Stream Publisher]
        C[Event Buffer]
    end
    
    subgraph "Streaming Layer"
        D[Load Balancer]
        E[Stream Router]
        F[Event Transformer]
        G[Quality Assurance]
    end
    
    subgraph "GRCOS Ingestion"
        H[Stream Consumer]
        I[Event Validator]
        J[Event Enricher]
        K[Event Dispatcher]
    end
    
    subgraph "Processing Pipeline"
        L[Real-time Processor]
        M[Batch Processor]
        N[Archive Processor]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    K --> M
    K --> N
```

### Streaming Protocols and Technologies

#### 1. Server-Sent Events (SSE)
- **Use Case**: Real-time dashboard updates and low-latency notifications
- **Advantages**: Simple implementation, automatic reconnection, built-in browser support
- **Throughput**: Up to 1,000 events/second per connection
- **Latency**: 10-50ms

#### 2. WebSocket Streaming
- **Use Case**: Bidirectional communication and high-frequency updates
- **Advantages**: Full-duplex communication, low overhead, custom protocols
- **Throughput**: Up to 10,000 events/second per connection
- **Latency**: 5-20ms

#### 3. Apache Kafka Integration
- **Use Case**: High-throughput, fault-tolerant event streaming
- **Advantages**: Horizontal scaling, message persistence, exactly-once delivery
- **Throughput**: 100,000+ events/second
- **Latency**: 2-10ms

## Implementation Components

### Wazuh Event Stream Publisher

```python
import asyncio
import json
import aiohttp
from aiohttp import web, WSMsgType
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import hashlib

class WazuhEventStreamPublisher:
    def __init__(self, wazuh_client, config):
        self.wazuh_client = wazuh_client
        self.config = config
        self.active_connections = {}
        self.event_buffer = asyncio.Queue(maxsize=10000)
        self.metrics = {
            'events_published': 0,
            'connections_active': 0,
            'events_dropped': 0,
            'avg_latency': 0.0
        }
        self.filters = {}
        
    async def start_publisher(self):
        """Start the event stream publisher"""
        # Start event collection from Wazuh
        asyncio.create_task(self._collect_wazuh_events())
        
        # Start event distribution
        asyncio.create_task(self._distribute_events())
        
        # Start metrics collection
        asyncio.create_task(self._collect_metrics())
        
        # Start web server for SSE/WebSocket connections
        app = web.Application()
        app.router.add_get('/events/stream', self._handle_sse_connection)
        app.router.add_get('/events/websocket', self._handle_websocket_connection)
        app.router.add_post('/events/subscribe', self._handle_subscription)
        
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, self.config['host'], self.config['port'])
        await site.start()
        
        logger.info(f"Event stream publisher started on {self.config['host']}:{self.config['port']}")
    
    async def _collect_wazuh_events(self):
        """Collect events from Wazuh in real-time"""
        while True:
            try:
                # Get recent events from Wazuh
                events = await self.wazuh_client.get_recent_events(
                    limit=1000,
                    since_last_poll=True
                )
                
                for event in events.get('data', {}).get('affected_items', []):
                    # Add timestamp and metadata
                    enriched_event = {
                        'stream_timestamp': datetime.utcnow().isoformat(),
                        'event_id': event.get('id', ''),
                        'source': 'wazuh',
                        'data': event
                    }
                    
                    # Add to buffer
                    try:
                        await self.event_buffer.put_nowait(enriched_event)
                        self.metrics['events_published'] += 1
                    except asyncio.QueueFull:
                        self.metrics['events_dropped'] += 1
                        logger.warning("Event buffer full, dropping event")
                
                # Poll every 100ms for real-time performance
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error collecting Wazuh events: {e}")
                await asyncio.sleep(1)
    
    async def _distribute_events(self):
        """Distribute events to active connections"""
        while True:
            try:
                # Get event from buffer
                event = await self.event_buffer.get()
                
                # Distribute to all active connections
                disconnected_connections = []
                
                for connection_id, connection in self.active_connections.items():
                    try:
                        # Apply filters if configured
                        if self._should_send_event(event, connection.get('filters', {})):
                            await self._send_event_to_connection(event, connection)
                    except Exception as e:
                        logger.warning(f"Failed to send event to connection {connection_id}: {e}")
                        disconnected_connections.append(connection_id)
                
                # Clean up disconnected connections
                for connection_id in disconnected_connections:
                    del self.active_connections[connection_id]
                    self.metrics['connections_active'] -= 1
                
                self.event_buffer.task_done()
                
            except Exception as e:
                logger.error(f"Error distributing events: {e}")
                await asyncio.sleep(0.1)
    
    async def _handle_sse_connection(self, request):
        """Handle Server-Sent Events connection"""
        response = web.StreamResponse(
            status=200,
            reason='OK',
            headers={
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )
        
        await response.prepare(request)
        
        connection_id = self._generate_connection_id()
        connection = {
            'type': 'sse',
            'response': response,
            'filters': self._parse_filters(request.query),
            'connected_at': datetime.utcnow().isoformat()
        }
        
        self.active_connections[connection_id] = connection
        self.metrics['connections_active'] += 1
        
        try:
            # Send initial connection confirmation
            await response.write(b'data: {"type": "connection_established", "connection_id": "' + 
                                connection_id.encode() + b'"}\n\n')
            
            # Keep connection alive
            while True:
                await asyncio.sleep(30)  # Send keepalive every 30 seconds
                await response.write(b'data: {"type": "keepalive"}\n\n')
                
        except Exception as e:
            logger.info(f"SSE connection {connection_id} closed: {e}")
        finally:
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
                self.metrics['connections_active'] -= 1
        
        return response
    
    async def _handle_websocket_connection(self, request):
        """Handle WebSocket connection"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        connection_id = self._generate_connection_id()
        connection = {
            'type': 'websocket',
            'websocket': ws,
            'filters': {},
            'connected_at': datetime.utcnow().isoformat()
        }
        
        self.active_connections[connection_id] = connection
        self.metrics['connections_active'] += 1
        
        try:
            # Send connection confirmation
            await ws.send_str(json.dumps({
                'type': 'connection_established',
                'connection_id': connection_id
            }))
            
            # Handle incoming messages
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    try:
                        message = json.loads(msg.data)
                        await self._handle_websocket_message(connection_id, message)
                    except json.JSONDecodeError:
                        await ws.send_str(json.dumps({
                            'type': 'error',
                            'message': 'Invalid JSON message'
                        }))
                elif msg.type == WSMsgType.ERROR:
                    logger.error(f'WebSocket error: {ws.exception()}')
                    break
                    
        except Exception as e:
            logger.info(f"WebSocket connection {connection_id} closed: {e}")
        finally:
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
                self.metrics['connections_active'] -= 1
        
        return ws
    
    async def _send_event_to_connection(self, event: Dict[str, Any], 
                                      connection: Dict[str, Any]):
        """Send event to a specific connection"""
        start_time = datetime.utcnow()
        
        try:
            if connection['type'] == 'sse':
                # Format for SSE
                event_data = json.dumps(event)
                sse_message = f"data: {event_data}\n\n"
                await connection['response'].write(sse_message.encode())
                
            elif connection['type'] == 'websocket':
                # Format for WebSocket
                await connection['websocket'].send_str(json.dumps({
                    'type': 'security_event',
                    'event': event
                }))
            
            # Update latency metrics
            latency = (datetime.utcnow() - start_time).total_seconds() * 1000
            self._update_latency_metric(latency)
            
        except Exception as e:
            logger.error(f"Failed to send event to connection: {e}")
            raise
    
    def _should_send_event(self, event: Dict[str, Any], 
                          filters: Dict[str, Any]) -> bool:
        """Check if event matches connection filters"""
        if not filters:
            return True
        
        event_data = event.get('data', {})
        
        # Rule level filter
        if 'min_level' in filters:
            rule_level = event_data.get('rule', {}).get('level', 0)
            if rule_level < filters['min_level']:
                return False
        
        # Agent filter
        if 'agent_ids' in filters:
            agent_id = event_data.get('agent', {}).get('id', '')
            if agent_id not in filters['agent_ids']:
                return False
        
        # Rule groups filter
        if 'rule_groups' in filters:
            rule_groups = event_data.get('rule', {}).get('groups', [])
            if not any(group in filters['rule_groups'] for group in rule_groups):
                return False
        
        return True
```

### High-Performance Event Consumer

```python
class HighPerformanceEventConsumer:
    def __init__(self, stream_config, processing_pipeline):
        self.stream_config = stream_config
        self.processing_pipeline = processing_pipeline
        self.consumer_pools = {}
        self.metrics = {
            'events_consumed': 0,
            'events_processed': 0,
            'processing_errors': 0,
            'avg_processing_time': 0.0
        }
        self.circuit_breakers = {}
        
    async def start_consumption(self):
        """Start high-performance event consumption"""
        # Initialize consumer pools for different event types
        await self._initialize_consumer_pools()
        
        # Start consumption tasks
        consumption_tasks = []
        for pool_name, pool_config in self.consumer_pools.items():
            for i in range(pool_config['worker_count']):
                task = asyncio.create_task(
                    self._consumer_worker(pool_name, i)
                )
                consumption_tasks.append(task)
        
        # Start metrics collection
        asyncio.create_task(self._collect_consumption_metrics())
        
        await asyncio.gather(*consumption_tasks)
    
    async def _initialize_consumer_pools(self):
        """Initialize consumer pools for different event types"""
        self.consumer_pools = {
            'critical_events': {
                'worker_count': 8,
                'queue_size': 1000,
                'batch_size': 10,
                'timeout': 1.0,
                'filters': {'min_level': 10}
            },
            'high_priority_events': {
                'worker_count': 6,
                'queue_size': 2000,
                'batch_size': 25,
                'timeout': 2.0,
                'filters': {'min_level': 7}
            },
            'standard_events': {
                'worker_count': 4,
                'queue_size': 5000,
                'batch_size': 50,
                'timeout': 5.0,
                'filters': {'min_level': 1}
            }
        }
        
        # Initialize queues for each pool
        for pool_name, config in self.consumer_pools.items():
            config['queue'] = asyncio.Queue(maxsize=config['queue_size'])
            config['circuit_breaker'] = CircuitBreaker(
                failure_threshold=10,
                recovery_timeout=30
            )
    
    async def _consumer_worker(self, pool_name: str, worker_id: int):
        """High-performance consumer worker"""
        pool_config = self.consumer_pools[pool_name]
        queue = pool_config['queue']
        batch_size = pool_config['batch_size']
        timeout = pool_config['timeout']
        circuit_breaker = pool_config['circuit_breaker']
        
        logger.info(f"Started consumer worker {pool_name}_{worker_id}")
        
        while True:
            try:
                # Check circuit breaker
                if circuit_breaker.is_open():
                    await asyncio.sleep(1)
                    continue
                
                # Collect batch of events
                events_batch = []
                batch_start_time = datetime.utcnow()
                
                while len(events_batch) < batch_size:
                    try:
                        event = await asyncio.wait_for(
                            queue.get(), 
                            timeout=timeout
                        )
                        events_batch.append(event)
                        queue.task_done()
                    except asyncio.TimeoutError:
                        break
                
                if events_batch:
                    # Process batch
                    processing_start = datetime.utcnow()
                    
                    try:
                        await self._process_events_batch(events_batch, pool_name)
                        
                        # Update success metrics
                        processing_time = (datetime.utcnow() - processing_start).total_seconds()
                        self._update_processing_metrics(len(events_batch), processing_time)
                        
                        # Reset circuit breaker on success
                        circuit_breaker.record_success()
                        
                    except Exception as e:
                        logger.error(f"Error processing batch in {pool_name}_{worker_id}: {e}")
                        self.metrics['processing_errors'] += len(events_batch)
                        circuit_breaker.record_failure()
                
            except Exception as e:
                logger.error(f"Error in consumer worker {pool_name}_{worker_id}: {e}")
                await asyncio.sleep(1)
    
    async def _process_events_batch(self, events: List[Dict[str, Any]], 
                                  pool_name: str):
        """Process batch of events efficiently"""
        # Pre-process events for batch optimization
        preprocessed_events = []
        for event in events:
            preprocessed_event = await self._preprocess_event(event)
            preprocessed_events.append(preprocessed_event)
        
        # Batch processing through pipeline
        processing_tasks = []
        
        # Group events by processing requirements
        event_groups = self._group_events_for_processing(preprocessed_events)
        
        for group_type, group_events in event_groups.items():
            task = asyncio.create_task(
                self.processing_pipeline.process_event_group(group_type, group_events)
            )
            processing_tasks.append(task)
        
        # Wait for all processing to complete
        results = await asyncio.gather(*processing_tasks, return_exceptions=True)
        
        # Handle any processing exceptions
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Processing group {i} failed: {result}")
                raise result
    
    async def _preprocess_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """Preprocess event for efficient batch processing"""
        # Add processing metadata
        event['processing_metadata'] = {
            'received_at': datetime.utcnow().isoformat(),
            'consumer_id': id(self),
            'preprocessing_version': '1.0'
        }
        
        # Normalize event structure
        normalized_event = self._normalize_event_structure(event)
        
        # Extract processing hints
        processing_hints = self._extract_processing_hints(normalized_event)
        normalized_event['processing_hints'] = processing_hints
        
        return normalized_event
```

### Stream Quality Assurance

```python
class StreamQualityAssurance:
    def __init__(self, config):
        self.config = config
        self.quality_metrics = {}
        self.alert_thresholds = {
            'latency_ms': 1000,
            'error_rate': 0.05,
            'throughput_drop': 0.3,
            'duplicate_rate': 0.01
        }
        self.quality_checks = {}
        
    async def monitor_stream_quality(self):
        """Continuously monitor stream quality"""
        while True:
            try:
                # Collect quality metrics
                current_metrics = await self._collect_quality_metrics()
                
                # Perform quality checks
                quality_issues = await self._perform_quality_checks(current_metrics)
                
                # Handle quality issues
                if quality_issues:
                    await self._handle_quality_issues(quality_issues)
                
                # Update quality metrics
                self.quality_metrics = current_metrics
                
                await asyncio.sleep(self.config['quality_check_interval'])
                
            except Exception as e:
                logger.error(f"Error in stream quality monitoring: {e}")
                await asyncio.sleep(10)
    
    async def _collect_quality_metrics(self) -> Dict[str, Any]:
        """Collect comprehensive stream quality metrics"""
        metrics = {
            'timestamp': datetime.utcnow().isoformat(),
            'latency': await self._measure_end_to_end_latency(),
            'throughput': await self._measure_throughput(),
            'error_rate': await self._calculate_error_rate(),
            'duplicate_rate': await self._calculate_duplicate_rate(),
            'data_integrity': await self._check_data_integrity(),
            'connection_stability': await self._check_connection_stability()
        }
        
        return metrics
    
    async def _measure_end_to_end_latency(self) -> Dict[str, float]:
        """Measure end-to-end latency of event streaming"""
        # Send test events and measure round-trip time
        test_events = []
        for i in range(10):
            test_event = {
                'test_id': f"latency_test_{datetime.utcnow().timestamp()}_{i}",
                'timestamp': datetime.utcnow().isoformat(),
                'type': 'latency_test'
            }
            test_events.append(test_event)
        
        # Measure latency for each test event
        latencies = []
        for test_event in test_events:
            start_time = datetime.utcnow()
            
            # Send test event through stream
            await self._send_test_event(test_event)
            
            # Wait for event to be processed
            processed_event = await self._wait_for_processed_event(test_event['test_id'])
            
            if processed_event:
                end_time = datetime.utcnow()
                latency = (end_time - start_time).total_seconds() * 1000
                latencies.append(latency)
        
        if latencies:
            return {
                'avg_latency_ms': sum(latencies) / len(latencies),
                'min_latency_ms': min(latencies),
                'max_latency_ms': max(latencies),
                'p95_latency_ms': self._calculate_percentile(latencies, 95),
                'p99_latency_ms': self._calculate_percentile(latencies, 99)
            }
        else:
            return {
                'avg_latency_ms': 0,
                'min_latency_ms': 0,
                'max_latency_ms': 0,
                'p95_latency_ms': 0,
                'p99_latency_ms': 0
            }
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Streaming Infrastructure Team
