test_name: GET /sca/{agent_id} for agents with Wazuh version >=4.2.0 (001) and <4.2.0 (006)

stages:

  # Testing GET /sca/001
  - name: Request
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data: !anything

  - name: Parameters -> limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - &sca_agent_result_001
              name: !anystr
              pass: !anyint
              score: !anyint
              references: !anystr #THIS FIELD IS NOT INCLUDED IN ALL ANSWERS
              fail: !anyint
              description: !anystr
              policy_id: !anystr
              start_scan: !anystr
              end_scan: !anystr
              hash_file: !anystr
              total_checks: !anyint
              invalid: !anyint
          total_affected_items: 1
          failed_items: []
          total_failed_items: 0
      save:
        json:
          expected_name: data.affected_items[0].name
          expected_description: data.affected_items[0].description

  - name: Parameters -> offset=1,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        offset: 1
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Parameters -> search=cis,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        search: cis
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_001
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Parameters -> q=score>0,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        q: score>0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_001
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Parameters -> limit=999999
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 999999
    response:
      status_code: 400

  - name: Parameters -> references=https://www.cisecurity.org/cis-benchmarks/, limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        references: https://www.cisecurity.org/cis-benchmarks/
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_001
              references: https://www.cisecurity.org/cis-benchmarks/
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Parameters -> description,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        description: "{expected_description:s}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_001
              description: !anystr
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Parameters -> name=CIS Benchmark for Debian/Linux 9, limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        name: "{expected_name:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_001
              name: "{expected_name:s}"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Parameters -> sort=-score,limit=10
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 10
        sort: -score
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "score"
            reverse: True

  # Testing GET /sca/006
  - name: Parameters -> limit=2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - &sca_agent_result_006
              name: !anystr
              pass: !anyint
              score: !anyint
              references: !anystr #THIS FIELD IS NOT INCLUDED IN ALL ANSWERS
              fail: !anyint
              description: !anystr
              policy_id: !anystr
              start_scan: !anystr
              end_scan: !anystr
              hash_file: !anystr
              total_checks: !anyint
              invalid: !anyint
            - <<: *sca_agent_result_006
          total_affected_items: 2
          failed_items: []
          total_failed_items: 0
      save:
        json:
          expected_name: data.affected_items[0].name
          expected_description: data.affected_items[0].description

  - name: Parameters -> offset=1,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        offset: 1
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_006
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Parameters -> search=cis,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        search: cis
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_006
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Parameters -> q=score>0,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        q: score>0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_006
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Parameters -> limit=999999
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 999999
    response:
      status_code: 400

  - name: Parameters -> references=https://www.cisecurity.org/cis-benchmarks/, limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        references: https://www.cisecurity.org/cis-benchmarks/
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_006
              references: https://www.cisecurity.org/cis-benchmarks/
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Parameters -> description,limit=1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        description: "{expected_description:s}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_agent_result_006
              description: !anystr
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Parameters -> sort=-score,limit=2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 2
        sort: -score
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "score"
            reverse: True
      status_code: 200

  - name: Parameters -> sort=+score,limit=2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 2
        sort: +score
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "score"
      status_code: 200

  - name: Try to get 012 agent's SCA data (never connected)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/012"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        error: 2007

  - name: Get distinct sca agent data
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/006"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /sca/{agent_id} using select (parametrized)

marks:
  - parametrize:
      key: field
      vals:
        - policy_id
        - name
        - description
        - references
        - pass
        - fail
        - score
        - invalid
        - total_checks
        - hash_file
        - end_scan
        - start_scan

stages:

  - name: Get agent SCA data using select
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        select: "{field}"
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        - function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: "{field:s}"

---
test_name: GET /sca/{agent_id} using select (parametrized) and distinct

marks:
  - parametrize:
      key: field
      vals:
        - policy_id
        - name
        - description
        - references
        - pass
        - fail
        - score
        - invalid
        - total_checks
        - hash_file
        - end_scan
        - start_scan

stages:

  - name: Get agent SCA data using select and distinct
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        select: "{field}"
        distinct: True
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        - function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: "{field:s}"
        # Check distinct is applied properly
        - function: tavern_utils:test_distinct_key

---
test_name: GET /sca/{agent_id}/checks/{policy_id}

stages:
  - name: Get policy ID
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      save:
        json:
          sca_policy_id: data.affected_items[0].policy_id

  # Save SCA check items and q tests

  - name: Get generic SCA check item
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - &sca_check_result_001
              remediation: !anystr
              rationale: !anystr
              title: !anystr
              policy_id: !anystr
              description: !anystr
              id: !anyint
              result: !anystr
              compliance: !anything
              rules: !anything
              condition: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get item with the command field (q="rules.type=command")
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        q: "rules.type=command"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - remediation: !anystr
              rationale: !anystr
              title: !anystr
              policy_id: !anystr
              description: !anystr
              id: !anyint
              result: !anystr
              compliance: !anything
              rules: !anything
              condition: !anystr
              reason: !anystr
              command: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      save:
        json:
          expected_remediation_command: data.affected_items[0].remediation
          expected_rationale_command: data.affected_items[0].rationale
          expected_title_command: data.affected_items[0].title
          expected_policy_id_command: data.affected_items[0].policy_id
          expected_description_command: data.affected_items[0].description
          expected_check_id_command: data.affected_items[0].id
          expected_compliance_command: data.affected_items[0].compliance
          expected_rules_command: data.affected_items[0].rules
          expected_condition_command: data.affected_items[0].condition
          expected_command_command: data.affected_items[0].command
  
  - name: Get items with the condition 'all'
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        q: condition=all
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get item with the file field (q="rules.type=file")
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        q: "rules.type=file"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - remediation: !anystr
              rationale: !anystr
              title: !anystr
              policy_id: !anystr
              description: !anystr
              id: !anyint
              result: !anystr
              compliance: !anything
              rules: !anything
              condition: !anystr
              reason: !anystr
              file: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      save:
        json:
          expected_remediation_file: data.affected_items[0].remediation
          expected_rationale_file: data.affected_items[0].rationale
          expected_title_file: data.affected_items[0].title
          expected_policy_id_file: data.affected_items[0].policy_id
          expected_description_file: data.affected_items[0].description
          expected_check_id_file: data.affected_items[0].id
          expected_compliance_file: data.affected_items[0].compliance
          expected_rules_file: data.affected_items[0].rules
          expected_condition_file: data.affected_items[0].condition
          expected_file_file: data.affected_items[0].file

  # limit and offset tests

  - name: Get SCA checks using limit=4
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 4
    response:
      verify_response_with:
        - function: tavern_utils:test_count_elements
          extra_kwargs:
            n_expected_items: 4
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001
            - <<: *sca_check_result_001
            - <<: *sca_check_result_001
            - <<: *sca_check_result_001
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      save:
        json:
          expected_check_id_2: data.affected_items[2].id
          expected_check_id_3: data.affected_items[3].id

  - name: Get SCA checks using limit=2, offset=2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 2
        offset: 2
    response:
      verify_response_with:
        - function: tavern_utils:test_count_elements
          extra_kwargs:
            n_expected_items: 2
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: !int "{expected_check_id_2}"
            - id: !int "{expected_check_id_3}"
          failed_items: [ ]
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to get SCA checks using limit=999999
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 999999
    response:
      status_code: 400

  # sort tests

  - name: Get SCA checks using sort=-id, limit=2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 2
        sort: -id
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "id"
            reverse: True
        - function: tavern_utils:test_count_elements
          extra_kwargs:
            n_expected_items: 2

  - name: Get SCA checks using sort=-title
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 3
        sort: -title
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "title"
            reverse: True
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get SCA checks using sort=command
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 3
        sort: command
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "command"
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: [ ]
          total_affected_items: !anyint
          total_failed_items: 0

  # search tests

  - name: Get SCA checks using search=pci_dss
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        search: pci_dss
    response:
      verify_response_with:
        - function: tavern_utils:test_validate_search
          extra_kwargs:
            search_param: "pci_dss"
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: [ ]
          total_affected_items: !anyint
          total_failed_items: 0

  # q tests

  - name: Get SCA check using query (command)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        q: "id={expected_check_id_command}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - &sca_check_result_001_command
              remediation: "{expected_remediation_command}"
              rationale: "{expected_rationale_command}"
              title: "{expected_title_command}"
              policy_id: "{expected_policy_id_command}"
              description: "{expected_description_command}"
              id: !int "{expected_check_id_command}"
              compliance: !force_format_include "{expected_compliance_command}"
              rules: !force_format_include "{expected_rules_command}"
              condition: "{expected_condition_command}"
              command: "{expected_command_command}"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Get SCA check using query (file)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        q: "id={expected_check_id_file}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - &sca_check_result_001_file
              remediation: "{expected_remediation_file}"
              rationale: "{expected_rationale_file}"
              title: "{expected_title_file}"
              policy_id: "{expected_policy_id_file}"
              description: "{expected_description_file}"
              id: !int "{expected_check_id_file}"
              compliance: !force_format_include "{expected_compliance_file}"
              rules: !force_format_include "{expected_rules_file}"
              condition: "{expected_condition_file}"
              file: "{expected_file_file}"
          failed_items: [ ]
          total_affected_items: 1
          total_failed_items: 0

  # filters tests

  - name: Get SCA checks filtering by rationale
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        rationale: "{expected_rationale_command:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_command
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  - name: Get SCA checks filtering by command
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        command: "{expected_command_command:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_command
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  - name: Get SCA checks filtering by condition
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        condition: "{expected_condition_command:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_command
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  - name: Get SCA checks filtering by description
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        description: "{expected_description_command:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_command
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  - name: Get SCA checks filtering by title
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        title: "{expected_title_command:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_command
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  - name: Get SCA checks filtering by file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        file: "{expected_file_file:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_file
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  - name: Get SCA checks filtering by remediation
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        remediation: "{expected_remediation_file:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_file
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  - name: Get SCA checks filtering by rationale
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        rationale: "{expected_rationale_command:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *sca_check_result_001_command
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

---
test_name: GET /sca/{agent_id}/checks/{policy_id} using select and select + distinct (parametrized)

marks:
  - parametrize:
      key: field
      vals:
        - policy_id
        - remediation
        - condition
        - rules.rule
        - rationale
        - id
        - command
        - title
        - reason
        - description
        - registry
        - process
        - rules.type
        - file
        - compliance.value
        - directory
        - result
        - compliance.key
        - references

stages:

  - name: Get policy ID
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      save:
        json:
          sca_policy_id: data.affected_items[0].policy_id

  - name: Get agent SCA data using select
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        select: "{field}"
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        - function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: "{field:s}"
            flag_nested_key_list: True

  - name: Get agent SCA data using select and distinct
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/{sca_policy_id:s}"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        select: "{field}"
        distinct: True
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        - function: tavern_utils:test_select_distinct_nested_sca_checks
          extra_kwargs:
            select_key: "{field:s}"
        # Check distinct is applied properly
        - function: tavern_utils:test_distinct_key
