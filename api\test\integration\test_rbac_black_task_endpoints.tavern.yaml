---
test_name: GET /tasks/status

stages:

  - name: Get all existent tasks (At this point there is no task created)
    request: &get_tasks
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

  - name: Get a specified task
    request:
      verify: False
      <<: *get_tasks
      params:
        tasks_list: 1
    response:
      <<: *permission_denied
