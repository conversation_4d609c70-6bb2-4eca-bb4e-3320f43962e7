# Excluded endpoints (no permissions required):
# - GET /
# - DELETE /security/user/authenticate
# - POST /security/user/authenticate
# - GET /security/users/me
# - POST /security/user/authenticate/run_as
# - GET /security/users/me/policies
# - GET /security/actions
# - GET /security/resources

---
test_name: PUT /active-response

stages:
  - name: Try to run an Active Response command (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "restart-ossec0"
        arguments: [ "-", "null", "(from_the_server)", "(no_rule_id)" ]
    response: &empty_response
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Try to run an Active Response command (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/active-response"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        command: "restart-ossec0"
        arguments: [ "-", "null", "(from_the_server)", "(no_rule_id)" ]
      params:
        agents_list: '001'
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

---
test_name: DELETE /agents

stages:
  - name: Try to remove all agents (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: DELETE
      params:
        agents_list: all
        status: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to remove one agent (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: DELETE
      params:
        agents_list: '001'
        status: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /agents

stages:
  - name: Try to get all agents (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get one agent (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /agents

stages:
  - name: Try to create a new agent
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: POST
      headers:
        Authorization: "Bearer {test_login_token}"
      json:
        name: "NewAgentPost"
        ip: "any"
    response:
      <<: *permission_denied

---
test_name: GET /agents/{agent_id}/daemons/stats

stages:
  - name: Try to get daemon stats from agent 001
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied


---
test_name: GET /agents/{agent_id}/config/{component}/{configuration}

stages:
  - name: Try to get client configuration from agent component
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/config/agent/client"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /agents/{agent_id}/group

stages:
  - name: Try to remove agent 005 from all groups
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/005/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /agents/{agent_id}/group/is_sync

stages:
  - name: Try to get agent sync
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/group/is_sync"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /agents/{agent_id}/group/{group_id}

stages:
  - name: Try to remove agent 001 from group1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/group/group1"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /agents/{agent_id}/group/{group_id}

stages:
  - name: Try to remove agent 005 from all groups
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/group/group1"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /agents/{agent_id}/key

stages:
  - name: Try get agent's 001 key
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/key"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /agents/{agent_id}/restart

stages:
  - name: Try to restart agent 001
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /agents/upgrade

stages:
  - name: Try to upgrade all agents (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: all
        version: "4.0.0"
    response:
      <<: *empty_response

  - name: Try to upgrade agent 008
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: 008
    response:
      <<: *permission_denied

---
test_name: PUT /agents/upgrade_custom

stages:
  - name: Try to customly upgrade all agents (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_custom"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: all
        file_path: "{custom_wpk_path:s}"
    response:
      <<: *empty_response

  - name: Try to customly upgrade agent 008 using default installer
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_custom"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: 008
        file_path: "{custom_wpk_path:s}"
    response:
      <<: *permission_denied

---
test_name: GET /agents/upgrade_result

stages:
  - name: Try to get all upgrade results (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_result"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 007 upgrade result (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_result"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '007'
    response:
      <<: *permission_denied

---
test_name: DELETE /agents/group

stages:
  - name: Try to remove agents 006 and 010 from group2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: 006,010
        group_id: group2
    response:
      <<: *permission_denied

---
test_name: PUT /agents/group

stages:
  - name: Try to assign all agents to group2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        group_id: group2
    response:
      <<: *permission_denied

---
test_name: PUT /agents/group/{group_id}/restart

stages:
  - name: Try to restart agents from group2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group/group2/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /agents/insert

stages:
  - name: Try to create a new agent specifying id, ip, key and name
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/insert"
      method: POST
      headers:
        Authorization: "Bearer {test_login_token}"
      json:
        name: "NewAgentPostInsert"
        ip: "any"
        id: "750"
        key: "1abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghi64"
    response:
      <<: *permission_denied

---
test_name: POST /agents/insert/quick

stages:
  - name: Try to create new agent specifying its name
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/insert/quick"
      method: POST
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agent_name: test_agent
    response:
      <<: *permission_denied

---
test_name: GET /agents/no_group

stages:
  - name: Try to get agents without group
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/no_group"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: PUT /agents/node/{node_id}/restart

stages:
  - name: Try to restart all agents belonging to a node (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/node/worker1/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /agents/outdated

stages:
  - name: Try to get the outdated agents
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/outdated"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: PUT /agents/reconnect

stages:
  - name: Try to reconnect all agents (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/reconnect"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to reconnect agent 001 (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/reconnect"
      method: PUT
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /agents/restart

stages:
  - name: Try to restart all agents (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to restart agent 001 (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/restart"
      method: PUT
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /agents/stats/distinct

stages:
  - name: Try to return all different combinations that agents have
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/stats/distinct"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: GET /agents/summary/os

stages:
  - name: Try to get the summary/os of all agents
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/summary/os"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: GET /agents/summary/status

stages:
  - name: Try to get the agents status summary
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/summary/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          connection:
            active: 0
            disconnected: 0
            never_connected: 0
            pending: 0
            total: 0
          configuration:
            synced: 0
            not_synced: 0
            total: 0

---
test_name: GET /ciscat/{agent_id}/results

stages:
  - name: Try to get ciscat results for agent 001
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/ciscat/001/results"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/local/info

stages:
  - name: Try to get cluster node info
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/local/info"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/nodes

stages:
  - name: Try to get cluster nodes
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/nodes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: GET /cluster/healthcheck

stages:
  - name: Try to get cluster healtcheck
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/healthcheck"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: GET /cluster/ruleset/synchronization

stages:

  - name: Get cluster ruleset synchronization status (empty response, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/ruleset/synchronization"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Get cluster ruleset synchronization status (specific node, permission denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/ruleset/synchronization"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: "master-node"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/local/config

stages:
  - name: Try to get cluster config
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/local/config"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/status

stages:
  - name: Try to get cluster status
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/api/config

stages:
  - name: Try to get all nodes api config (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/api/config"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response


  - name: Try to get master-node api config (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/api/config"
      method: GET
      params:
        nodes_list: master-node
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/status

stages:
  - name: Try to get status (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/info

stages:
  - name: Try to get info (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/info"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/configuration

stages:
  - name: Try to get configuration (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/configuration"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /cluster/{node_id}/configuration

stages:
  - name: Try to update configuration (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/configuration"
      method: PUT
      data: "{valid_ossec_conf:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/daemons/stats

stages:
  - name: Try to get all daemons' statistics from a specified node (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/stats

stages:
  - name: Try to get stats (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/stats/hourly

stages:
  - name: Try to get hourly stats (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/stats/hourly"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/stats/weekly

stages:
  - name: Try to get weekly stats (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/stats/weekly"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/stats/analysisd

stages:
  - name: Try to get analysisd stats (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/stats/analysisd"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/stats/remoted

stages:
  - name: Try to get remoted stats (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/stats/remoted"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/logs

stages:
  - name: Try to get logs (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/logs"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/logs/summary

stages:
  - name: Try to get summary logs (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/logs/summary"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /cluster/restart

stages:
  - name: Try to restart all nodes (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to restart master-node node (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/restart"
      method: PUT
      params:
        nodes_list: master-node
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/configuration/validation

stages:
  - name: Try to request cluster validation (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/configuration/validation"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to request master-node validation (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/configuration/validation"
      method: GET
      params:
        nodes_list: master-node
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/configuration/{component}/{configuration}

stages:
  - name: Try to get analysis global configuration (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/configuration/analysis/global"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /decoders

stages:
  - name: Try to get decoders (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to one decoder (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders"
      method: GET
      params:
        decoder_names: agent-buffer
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1504
          total_affected_items: 0
          total_failed_items: 1


---
test_name: GET /decoders/files

stages:
  - name: Try to get decoders files (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get one decoder file (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files"
      method: GET
      params:
        filename: local_decoder.xml
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /decoders/files/{filename}

stages:
  - name: Try to get decoder file content
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/0005-wazuh_decoders.xml"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /decoders/files/{filename}

stages:
  - name: Try to delete decoder file content
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/0005-wazuh_decoders.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /decoders/files/{filename}

stages:
  - name: Try to upload decoder file content
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/0005-wazuh_decoders.xml"
      method: PUT
      data: "{new_decoder:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      <<: *permission_denied

---
test_name: GET /decoders/parents

stages:
  - name: Try to get information about all decoders parents
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/parents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: DELETE /experimental/rootcheck

stages:
  - name: Try to delete rootcheck data (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/rootcheck"
      method: DELETE
      params:
        agents_list: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to delete agent 001 rootcheck data (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/rootcheck"
      method: DELETE
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /experimental/syscheck

stages:
  - name: Try to delete all experimental syscheck data (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscheck"
      method: DELETE
      params:
        agents_list: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to delete agent 001 experimental syscheck data (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscheck"
      method: DELETE
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/ciscat/results

stages:
  - name: Try to get agents ciscat results (experimental) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/ciscat/results"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 ciscat results (experimental) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/ciscat/results"
      params:
        agents_list: '001'
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/hardware

stages:
  - name: Try to get agents hardware (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hardware"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 hardware (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hardware"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/netaddr

stages:
  - name: Try to get agents netaddr (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netaddr"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 netaddr (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netaddr"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/netiface

stages:
  - name: Try to get agents netiface (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netiface"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 netiface (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netiface"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/netproto

stages:
  - name: Try to get agents netproto (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netproto"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 netproto (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netproto"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/os

stages:
  - name: Try to get agents OS (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/os"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 OS (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/os"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/packages

stages:
  - name: Try to get agents packages (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/packages"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 packages (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/packages"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/ports

stages:
  - name: Try to get agents ports (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/ports"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 ports (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/ports"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/processes

stages:
  - name: Try to get agents processes (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/processes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 processes (experimental syscollector) (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/processes"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /experimental/syscollector/hotfixes

stages:
  - name: Try to get agents hotfixes (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hotfixes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get agent 001 hotfixes (experimental syscollector) (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hotfixes"
      method: GET
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /groups

stages:
  - name: Try to delete all groups (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: DELETE
      params:
        groups_list: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to delete group3 (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: DELETE
      params:
        groups_list: group3
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /groups

stages:
  - name: Try to get all groups (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get group1 (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: GET
      params:
        groups_list: group1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /groups

stages:
  - name: Try to create new group
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: POST
      json:
        group_id: "group5"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /groups/{group_id}/agents

stages:
  - name: Try to get agents in group1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups/group1/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /groups/{group_id}/configuration

stages:
  - name: Try to get group1 configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups/group1/configuration"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /groups/{group_id}/configuration

stages:
  - name: Try to update group1 configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups/group1/configuration"
      method: PUT
      data:
        "{file_xml:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: "application/xml"
    response:
      <<: *permission_denied

---
test_name: GET /groups/{group_id}/files/{filename}

stages:
  - name: Try to get one file of a group
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups/default/files/agent.conf"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /groups/{group_id}/files

stages:
  - name: Try to get group files
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups/default/files"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /lists

stages:
  - name: Try to get all CDB lists (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get one CDB list (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists"
      method: GET
      params:
        relative_dirname: etc/lists
        filename: audit-keys
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /lists/files/{filename}

stages:
  - name: Try to get CDB list file content
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/audit-keys"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /lists/files/{filename}

stages:
  - name: Try to upload CDB list file content
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/new-list"
      method: PUT
      data: "{new_cdb_list:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      <<: *permission_denied

---
test_name: DELETE /lists/files/{filename}

stages:
  - name: Try to delete CDB list file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/audit-keys"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /lists/files

stages:
  - name: Try to get all CDB lists paths (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get one CDB list path (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files"
      method: GET
      params:
        relative_dirname: etc/lists
        filename: audit-keys
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /logtest

stages:
  - name: Try to run logtest
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/logtest"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        token: "token_without_session"
        event: "Jun 24 11:54:19 Master systemd[2099]: Started VTE child process 20118 launched by terminator process 17756."
        log_format: "syslog"
        location: "master->/var/log/syslog"
    response:
      <<: *permission_denied

---
test_name: DELETE /logtest/sessions/{token}

stages:
  - name: Try to end logtest session
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/logtest/sessions/testtoken"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/status

stages:
  - name: Try to get manager status
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/info

stages:
  - name: Try to get manager info
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/info"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/configuration

stages:
  - name: Try to get manager configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /manager/configuration

stages:
  - name: Try to update manager configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration"
      method: PUT
      data: "{valid_ossec_conf:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats

stages:
  - name: Try to get all daemons' statistics
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats

stages:
  - name: Try to get manager stats
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/hourly

stages:
  - name: Try to get manager hourly stats
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/hourly"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/weekly

stages:
  - name: Try to get manager weekly stats
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/weekly"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/analysisd

stages:
  - name: Try to get manager analysisd stats
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/analysisd"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/remoted

stages:
  - name: Try to get manager remoted stats
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/remoted"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/logs

stages:
  - name: Try to get manager logs
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/logs"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/logs/summary

stages:
  - name: Try to get manager summary logs
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/logs/summary"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/api/config

stages:
  - name: Try to get manager API config
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/api/config"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /manager/restart

stages:
  - name: Try to restart manager
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /managerer/configuration/{component}/{configuration}

stages:
  - name: Try to show the config of analysis/global in the manager
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration/analysis/global"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/configuration/validation

stages:
  - name: Try to validate manager configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration/validation"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /mitre/metadata

stages:
  - name: Try to get MITRE metadata
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/metadata"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /mitre/mitigations

stages:

  - name: Try to get MITRE mitigations
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/mitigations"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /mitre/references

stages:

  - name: Try to get MITRE references
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/references"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /mitre/techniques

stages:

  - name: Try to get MITRE techniques
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/techniques"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /mitre/tactics

stages:

  - name: Try to get MITRE tactics
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/tactics"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /mitre/groups

stages:

  - name: Try to get MITRE groups
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/groups"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /mitre/software

stages:

  - name: Try to get MITRE software
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/mitre/software"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /overview/agents

stages:
  - name: Try to get agents overview
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/overview/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      json:
        data:
          nodes: []
          groups: []
          agent_os: []
          agent_status:
            connection:
              active: 0
              disconnected: 0
              never_connected: 0
              pending: 0
              total: 0
            configuration:
              synced: 0
              total: 0
              not_synced: 0
          agent_version: []
          last_registered_agent: []
        error: 0

---
test_name: PUT /rootcheck

stages:
  - name: Try to run rootcheck scan (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to run rootcheck scan (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      method: PUT
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /rootcheck/{agent_id}

stages:

  - name: Try to delete agent 001 rootcheck data
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /rootcheck/{agent_id}/last_scan

stages:
  - name: Try to get last scan date for agent 001
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001/last_scan"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /rootcheck/{agent_id}

stages:
  - name: Try to get rootcheck results for agent 001
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /rules

stages:
  - name: Try to get all rules (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get a rule (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      method: GET
      params:
        rule_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1208
          total_affected_items: 0
          total_failed_items: 1
---
test_name: GET /rules/groups

stages:
  - name: Try to get rules groups
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/groups"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: GET /rules/requirement/{requirement}

stages:
  - name: Try to get rules with pci_dss requirement
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/pci_dss"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

---
test_name: GET /rules/files/{filename}

stages:
  - name: Try to get rule file content
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/local_rules.xml"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /rules/files

stages:
  - name: Try to get rules files (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get rules file (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files"
      method: GET
      params:
        relative_dirname: ruleset/rules
        filename: 0010-rules_config.xml
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /rules/files/{filename}

stages:
  - name: Try to upload rule file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: PUT
      data: "{new_rules:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      <<: *permission_denied

---
test_name: DELETE /rules/files/{filename}

stages:
  - name: Try to delete rule file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /security/user/revoke

stages:
  - name: Try to revoke all JWT tokens
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/revoke"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /security/users

stages:
  - name: Try to get API users (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get API user (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        user_ids: 1
    response:
      <<: *permission_denied

---
test_name: POST /security/users

stages:
  - name: Try to add API user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      method: POST
      json:
        username: newUser
        password: stringA1!
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /security/users/{user_id}/run_as

stages:
  - name: Try to change the allow_run_as field of a user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/1/run_as"
      method: PUT
      params:
        allow_run_as: false
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/users

stages:
  - name: Try to delete API users (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      method: DELETE
      params:
        user_ids: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to delete API user (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      method: DELETE
      params:
        user_ids: 002
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /security/users/{user_id}

stages:
  - name: Try to update API user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/1"
      method: PUT
      json:
        password: stringB1!
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /security/roles

stages:
  - name: Try to get API roles (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get API role (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      method: GET
      params:
        role_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /security/roles

stages:
  - name: Try to add API role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      method: POST
      json:
        name: test_role
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/roles

stages:
  - name: Try to delete API roles (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      method: DELETE
      params:
        role_ids: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response


  - name: Try to delete API role (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      method: DELETE
      params:
        role_ids: 8
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /security/roles/{role_id}

stages:
  - name: Try to update API role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/8"
      method: PUT
      json:
        name: new_name
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /security/rules

stages:
  - name: Try to get API rules (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get API rule (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      method: GET
      params:
        rule_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /security/rules

stages:
  - name: Try to add API rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      method: POST
      json:
        name: new_rule
        rule:
          FIND$:
            definition: "normal_rule"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /security/rules/{rule_id}

stages:
  - name: Try to update API rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/1"
      method: PUT
      json:
        name: changed_name
        rule:
          MATCH$:
            definition: "normal_rule"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/rules

stages:
  - name: Try to delete API rules (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      method: DELETE
      params:
        rule_ids: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to delete API rule (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      method: DELETE
      params:
        rule_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /security/policies

stages:
  - name: Try to get API policies (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to get API policy (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      method: GET
      params:
        policy_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/policies

stages:
  - name: Try to delete API policy (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      method: DELETE
      params:
        policy_ids: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to delete API policy (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      method: DELETE
      params:
        policy_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /security/policies

stages:
  - name: Try to add API policy
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      method: POST
      json:
        name: "test_i2"
        policy:
          actions:
            - "agent:upgrade"
          resources:
            - "agent:id:004"
            - "agent:id:005"
            - "agent:id:006"
          effect: "deny"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /security/policies/{policy_id}

stages:
  - name: Try to update API policy
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/1"
      method: PUT
      json:
        name: "test_i3"
        policy:
          actions:
            - "agent:read"
          resources:
            - "agent:id:004"
            - "agent:id:005"
            - "agent:id:006"
          effect: "allow"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /security/users/{user_id}/roles

stages:
  - name: Try to add roles to user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/99/roles"
      method: POST
      params:
        role_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/users/{user_id}/roles

stages:
  - name: Try to remove roles from user (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/99/roles"
      method: DELETE
      params:
        role_ids: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to remove role from user (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/99/roles"
      method: DELETE
      params:
        role_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /security/roles/{role_id}/policies

stages:
  - name: Try to add policies to role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/1/policies"
      method: POST
      params:
        policy_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: POST /security/roles/{role_id}/rules

stages:
  - name: Try to add rules to role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/1/rules"
      method: POST
      params:
        rule_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/roles/{role_id}/policies

stages:
  - name: Try to remove policies from role (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/1/policies"
      method: DELETE
      params:
        policy_ids: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to remove policy from role (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/1/policies"
      method: DELETE
      params:
        policy_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/roles/{role_id}/rules

stages:
  - name: Try to remove rules from role (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/1/rules"
      method: DELETE
      params:
        rule_ids: all
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to remove rule from role (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/1/rules"
      method: DELETE
      params:
        rule_ids: 1
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /security/config

stages:
  - name: Try to get API security config
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /security/config

stages:
  - name: Try to update API security config
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      method: PUT
      json:
        auth_token_exp_timeout: 700
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /security/config

stages:
  - name: Try to restore default API security config
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /sca/{agent_id}

stages:
  - name: Try to get SCA data
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /sca/{agent_id}/checks/{policy_id}

stages:
  - name: Try to request policy checks for 000
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/000/checks/cis_debian9_L1"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /syscheck

stages:
  - name: Try to run syscheck scan (generic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *empty_response

  - name: Try to run syscheck scan (specific)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      method: PUT
      params:
        agents_list: '001'
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscheck/{agent_id}

stages:
  - name: Try to get syscheck data
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: DELETE /syscheck/{agent_id}

stages:
  - name: Try to remove syscheck data
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/001"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscheck/{agent_id}/last_scan

stages:
  - name: Try to get syscheck last scan for agent 001
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/001/last_scan"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/hardware

stages:
  - name: Try to get agent hardware (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/hardware"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/hotfixes

stages:
  - name: Try to get agent hotfixes (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/hotfixes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/netaddr

stages:
  - name: Try to get agent netaddr (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/netaddr"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/netiface

stages:
  - name: Try to get agent netiface (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/netiface"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/netproto

stages:
  - name: Try to get agent netproto (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/netproto"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/os

stages:
  - name: Try to get agent os (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/os"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/packages

stages:
  - name: Try to get agent packages (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/packages"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/ports

stages:
  - name: Try to get agent ports (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/ports"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /syscollector/{agent_id}/processes

stages:
  - name: Try to get agent processes (syscollector)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/processes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied


---
test_name: GET /tasks/status

stages:
  - name: Try to get tasks status
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied
