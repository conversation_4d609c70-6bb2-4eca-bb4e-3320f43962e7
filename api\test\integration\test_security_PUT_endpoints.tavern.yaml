---
test_name: PUT /security/roles

stages:

  # PUT /security/roles/{role_id}
  - name: Modify a role in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/102"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModified"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              name: testModified
              policies: !anything
              users: !anything
              rules: !anything
          total_affected_items: 1

  # PUT /security/roles/{role_id}
  - name: Modify a role in the system (using same name)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/102"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModified"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              name: testModified
              policies: !anything
              rules: !anything
          total_affected_items: 1

  # PUT /security/roles/{role_id}
  - name: Modify a role in the system (using a very long name)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/102"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "test_very_long_role_name_so_the_request_fails_because_of_maximum_length"
    response:
      status_code: 400

  # PUT /security/roles/{role_id}
  - name: Modify a role in the system (using same name for a different ID)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/103"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModified"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4005
              id:
                - 103
          total_affected_items: 0
          total_failed_items: 1

  # PUT /security/roles/{non-existent role_id}
  - name: Modify a non-existent role in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/999"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "Unexistent"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4002
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1
---
test_name: PUT /security/policies

stages:

  # PUT /security/policies/{policy_id}
  - name: Modify a policy in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModifiedPolicy"
        policy:
          actions:
            - "agent:delete"
          resources:
            - "agent:id:001"
            - "agent:id:002"
            - "agent:id:003"
          effect: "allow"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              name: testModifiedPolicy
              policy:
                actions:
                  - agent:delete
                effect: allow
                resources:
                  - agent:id:001
                  - agent:id:002
                  - agent:id:003
              roles: !anything
          total_affected_items: 1

  # PUT /security/policies/{policy_id}
  - name: Modify a policy in the system (without change policy definition)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModifiedPolicy2"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              name: testModifiedPolicy2
              policy:
                actions:
                  - agent:delete
                effect: allow
                resources:
                  - agent:id:001
                  - agent:id:002
                  - agent:id:003
              roles: !anything
          total_affected_items: 1

  # PUT /security/policies/{policy_id}
  - name: Modify a policy in the system (using a very long name)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "test_very_long_policy_name_so_the_request_fails_because_of_maximum_length"
    response:
      status_code: 400

  # PUT /security/policies/{policy_id}
  - name: Modify a policy in the system (without change name)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        policy:
          actions:
            - "agent:delete"
          resources:
            - "agent:id:001"
            - "agent:id:002"
            - "agent:id:003"
          effect: "deny"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              name: testModifiedPolicy2
              policy:
                actions:
                  - agent:delete
                effect: deny
                resources:
                  - agent:id:001
                  - agent:id:002
                  - agent:id:003
              roles: !anything
          total_affected_items: 1

  # PUT /security/policies/{policy_id}
  - name: Modify a policy in the system (case insensitive)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        policy:
          actions:
            - "aGenT:dELEte"
          resources:
            - "aGeNt:ID:001"
            - "AGENT:iD:002"
            - "agEnT:Id:003"
          effect: "dEnY"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              name: testModifiedPolicy2
              policy:
                actions:
                  - agent:delete
                effect: deny
                resources:
                  - agent:id:001
                  - agent:id:002
                  - agent:id:003
              roles: !anything
          total_affected_items: 1

  # PUT /security/policies/{non-existent policy_id}
  - name: Modify a non-existent policy in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/999"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "Unexistent"
        policy:
          actions:
            - "agent:delete"
          resources:
            - "agent:id:001"
            - "agent:id:002"
            - "agent:id:003"
          effect: "allow"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4007
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1

  # PUT /security/policies/{non-existent policy_id}
  - name: Modify a policy in the system, bad policy definition
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "incorrect"
        policy:
          actions:
            - "agent:delete"
          resources:
            - "agent:id:001:"
            - "agent:id:002"
            - "agent:id:003"
    response:
      status_code: 400

---
test_name: PUT /security/rules

stages:

# PUT /security/rules/{rule_id}
  - name: Modify a rule in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/102"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModified"
        rule: &modified_rule
          MATCH:
            normal_rule: "modifiedR"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              name: "testModified"
              rule:
                <<: *modified_rule
              roles: !anything
          total_affected_items: 1

  # PUT /security/rules/{rule_id}
  - name: Modify a rule in the system (using same name)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/102"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModified"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              name: "testModified"
              rule:
                <<: *modified_rule
              roles: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  # PUT /security/rules/{rule_id}
  - name: Modify a rule in the system (using a very long name)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/102"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "test_very_long_rule_name_so_the_request_fails_because_of_maximum_length"
    response:
      status_code: 400

  # PUT /security/rules/{rule_id}
  - name: Modify a rule in the system (using same name for a different ID)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/103"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModified"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4005
              id:
                - 103
          total_affected_items: 0
          total_failed_items: 1

  # PUT /security/rules/{rule_id}
  - name: Modify a rule in the system (using different name but same rule)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/103"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "testModified2"
        rule:
          MATCH:
            normal_rule: "modifiedR2"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              name: "testModified2"
              rule:
                MATCH:
                  normal_rule: "modifiedR2"
              roles: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  # PUT /security/rules/{non-existent rule_id}
  - name: Modify a non-existent rule in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/999"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: "Unexistent"
        rule:
          MATCH:
            user: "no"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4022
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1

---
test_name: PUT /security/user/revoke

stages:

  - name: Revoke all tokens
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/revoke"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
    response:
      status_code: 200

  - name: Revoke all tokens (Invalid token after previous call)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/revoke"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
    response:
      status_code: 401

---
test_name: DELETE /security/user/authenticate

stages:

  - name: Logout current user
    delay_after: 10
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/authenticate"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
    response:
      status_code: 200

---
test_name: PUT /security/users/{user_id}

stages:

  - name: Update an existent user (empty body)
    request: &put_users_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
    response:
      status_code: 400

  - name: Update an existent user (insecure password)
    request:
      verify: False
      <<: *put_users_request
      json:
        password: "new_user"
    response:
      status_code: 400

  - name: Update an existent user (secure password)
    request:
      verify: False
      <<: *put_users_request
      json:
        password: "new_user2A!"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              username: python
              allow_run_as: false
          total_affected_items: 1

  - name: Update an non-existent user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/200"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        password: "new_user1A"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 5001
              id:
                - 200
          total_affected_items: 0
          total_failed_items: 1

  - name: Update an existent user (invalid body)
    request:
      verify: False
      <<: *put_users_request
      json:
        pass: "ossec"
    response:
      status_code: 400

  - name: Update an existent user (valid body)
    request:
      verify: False
      <<: *put_users_request
      json:
        password: "new_pa55worD!"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - username: python
              allow_run_as: false
          total_affected_items: 1


---
test_name: PUT /security/users/{user_id}/run_as

stages:

  - name: Update user's allow_run_as flag
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103/run_as"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      params:
        allow_run_as: True
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              username: python
              allow_run_as: true
          total_affected_items: 1

  - name: Update user's allow_run_as flag (invalid_user)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/INVALID/run_as"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      params:
        allow_run_as: True
    response:
      status_code: 400

  - name: Update user's allow_run_as flag (nonexistent user)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/999/run_as"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      params:
        allow_run_as: True
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: [ ]
          failed_items:
            - error:
                code: 5001
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1

---
test_name: PUT /security/config

stages:

  - name: Update security configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        auth_token_exp_timeout: 3000
    response:
      status_code: 200

---
test_name: PUT /security/config (Check)

stages:

  - name: Get security configuration to check if PUT method worked correctly
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          auth_token_exp_timeout: 3000
          rbac_mode: !anystr
