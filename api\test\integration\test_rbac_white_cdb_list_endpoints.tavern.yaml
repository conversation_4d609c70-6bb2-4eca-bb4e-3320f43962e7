---
test_name: GET LISTS RBAC

stages:

  - name: Show the lists of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - items: !anything
              filename: audit-keys
              relative_dirname: etc/lists
            - items: !anything
              filename: aws-eventnames
              relative_dirname: etc/lists/amazon
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Show an specified filename (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        filename: audit-keys
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - items: !anything
              filename: audit-keys
              relative_dirname: etc/lists
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to show an specified path (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        relative_dirname: etc/lists/amazon
        filename: aws-sources
    response:
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

---
test_name: GET LISTS FILES RBAC

stages:
  - name: Show the lists files of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - filename: audit-keys
              relative_dirname: etc/lists
            - filename: aws-eventnames
              relative_dirname: etc/lists/amazon
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

---
test_name: GET /lists/files/{filename}

stages:

  - name: Show content from a list (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/aws-eventnames"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to show content from a list (deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/security-eventchannel"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

---
test_name: PUT /lists/files/{filename}

stages:
  - name: Try to overwrite security-eventchannel (indirectly denied deletion)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/security-eventchannel"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      data: "{new_cdb_list:s}"
      params:
        overwrite: True
    response:
      status_code: 200
      json:
        error: 1
        data:
          failed_items:
            - error:
                code: 4000
              id:
                - 'etc/lists/security-eventchannel'
          total_affected_items: 0
          total_failed_items: 1

  - name: Overwrite audit-keys list (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/audit-keys"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      data: "{new_cdb_list:s}"
      params:
        overwrite: True
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: [ ]
          total_affected_items: 1
          total_failed_items: 0

  - name: Updload new list file (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/new_cdb_list"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      data: "{new_cdb_list:s}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: [ ]
          total_affected_items: 1
          total_failed_items: 0

---
test_name: DELETE /lists/files/{filename}

stages:
  - name: Delete a CDB list (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/audit-keys"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - 'etc/lists/audit-keys'
          failed_items: [ ]
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to delete a CDB list (deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/lists/files/security-eventchannel"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
    response:
      <<: *permission_denied
