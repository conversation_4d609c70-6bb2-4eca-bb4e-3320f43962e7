---
test_name: GET /rootcheck

stages:

  - name: Get rootcheck scan results for agent 000 (Allow)
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/000"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to get rootcheck scan results for agent 002 (<PERSON><PERSON>)
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/002"
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &permission_denied
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

---
test_name: GET /rootcheck/{agent_id}/last_scan

stages:

  - name: Get when the last scan for agent 001 started and ended
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001/last_scan"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - end: !anything
              start: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to get when the last scan for agent 002 started and ended
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/002/last_scan"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT /rootcheck

stages:

  - name: Run a rootcheck scan in all agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 4
          total_failed_items: 0

  - name: Try to run a rootcheck scan on a list of agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '000,003,001,004,008'
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 4000
              id:
                - '004'
                - '008'
          total_affected_items: 3
          total_failed_items: 2

  - name: Try to run a rootcheck scan on a list of agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '004'
    response:
      <<: *permission_denied

---
test_name: DELETE /rootcheck/{agent_id}

stages:

  - name: Delete rootcheck scans in agent 001 (Allow)
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to delete rootcheck scans in agent 002 (Deny)
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/002"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied
