---
test_name: GET USERS RBAC

stages:

  - name: Get all users in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
              username: wazuh
              allow_run_as: true
              roles:
                - 1
            - id: 2
              username: wazuh-wui
              allow_run_as: true
              roles:
                - 1
            - id: 99
              username: testing
              allow_run_as: false
              roles:
                - 99
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
            - id: 104
              username: rbac
              allow_run_as: false
              roles:
                - 104
                - 102
                - 103
          total_affected_items: 5
          total_failed_items: 0
          failed_items: []

  - name: Get a specified user by its username (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 102
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Get a specified user by its username (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 100
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

  - name: Get a list of users by its username (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 1,102,104
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
              username: wazuh
              allow_run_as: true
              roles:
                - 1
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
            - id: 104
              username: rbac
              allow_run_as: false
              roles:
                - 104
                - 102
                - 103
          total_affected_items: 3
          total_failed_items: 0
          failed_items: []

  - name: Get a list of users by its username (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 100,101
    response:
      <<: *permission_denied

  - name: Get a list of users by its username (Both)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        user_ids: 101,103,105,102,1,100
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
              username: wazuh
              allow_run_as: true
              roles:
                - 1
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
          total_affected_items: 2
          total_failed_items: 4
          failed_items:
            - error:
                code: 4000
                remediation: !anystr
              id:
                - 100
                - 101
                - 103
                - 105

---
test_name: GET ROLES RBAC

stages:

  - name: Get all roles in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Get a specified role by its id (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        role_ids: 1
    response:
      <<: *permission_denied

---
test_name: GET RULES RBAC

stages:

  - name: Get all rules in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 1
            - id: 2
            - id: 100
            - id: 101
            - id: 102
            - id: 104
            - id: 105
          failed_items: []
          total_affected_items: 7
          total_failed_items: 0

  - name: Get a specified rule by its id (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        rule_ids: 103
    response:
      <<: *permission_denied

---
test_name: GET POLICIES RBAC

stages:

  - name: Get all policies in the system (All denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0


  - name: Get a specified policy by its id (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        policy_ids: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Get a specified policy by its id (It doesn't exist but we have all the permissions on the resource policies)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        policy_ids: 999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4007
                remediation: !anystr
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1

  - name: Get a list of policies by its id (Existent and no existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        policy_ids: 1,2,999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 4007
                remediation: !anystr
              id:
                - 999
          total_affected_items: 2
          total_failed_items: 1

---
test_name: GET SECURITY CONFIG

stages:

  - name: Get current security config (deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: UPDATE SECURITY CONFIG

stages:

  - name: Update default security config (deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        auth_token_exp_timeout: 3000
    response:
      <<: *permission_denied

---
test_name: UPDATE USERS RBAC

stages:

  - name: Update one specified user in the system (All allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/105"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        password: stringA1!
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 105
              username: guest
              allow_run_as: false
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Update one specified user in the system (All allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        password: stringA1!
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              username: python
              allow_run_as: false
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: UPDATE USER'S ALLOW_RUN_AS FLAG

stages:

  - name: Update one specified user's allow_run_as flag (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/104/run_as"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      params:
        allow_run_as: false
    response:
      <<: *permission_denied

---
test_name: UPDATE ROLES RBAC

stages:

  - name: Update one specified role in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: normalUserModified
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              name: normalUserModified
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

  - name: Update one specified role in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/103"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: wazuh-wuiModified
    response:
      <<: *permission_denied

  - name: Update one admin role in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/1"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: wazuhModified
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4008
              id:
                - 1
          total_affected_items: 0
          total_failed_items: 1

---
test_name: UPDATE RULES RBAC

stages:

  - name: Update one specified rule in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/105"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: normalRuleModified
        rule:
          MATCH:
            modified: "modified_rule"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 105
              name: normalRuleModified
              rule:
                MATCH:
                  modified: "modified_rule"
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

  - name: Update one specified rule in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: normalRuleModified
        rule:
          MATCH:
            modified: "modified_rule"
    response:
      <<: *permission_denied

  - name: Update one admin rule in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules/1"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: normalRuleModified
        rule:
          MATCH:
            modified: "modified_rule"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4008
              id:
                - 1
          total_affected_items: 0
          total_failed_items: 1

---
test_name: UPDATE POLICIES RBAC

stages:

  - name: Update one specified policy in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/103"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: administratorPolicyModified
        policy:
          actions:
            - agent:read
          effect: deny
          resources:
            - agent:id:097
            - agent:id:002
            - agent:id:003
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              name: administratorPolicyModified
              policy:
                actions:
                  - agent:read
                effect: deny
                resources:
                  - agent:id:097
                  - agent:id:002
                  - agent:id:003
              roles: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Update one specified policy in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies/104"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        name: normalPolicyModified
        policy:
          actions:
            - agent:read
          effect: allow
          resources:
            - agent:id:096
            - agent:id:002
            - agent:id:003
    response:
      <<: *permission_denied

---
test_name: CREATE LINK USER-ROLES RBAC

stages:

  - name: Create one specified link between one user and a list of roles (Allow and Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/103/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        role_ids: 100,101,102,103,106
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              username: python
              allow_run_as: false
              roles:
                - 101
                - 100
          total_affected_items: 1
          total_failed_items: 4
          failed_items:
            - error:
                code: 4000
              id:
                - 102
                - 103
            - error:
                code: 4002
              id:
                - 106
            - error:
                code: 4017
              id:
                - 101

  - name: Create one specified link between one user and a list of roles (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/102/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        role_ids: 100,104
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              roles:
                - 101
                - 104
                - 100
              username: ossec
          failed_items:
            - error:
                code: 4017
              id:
                - 104
          total_affected_items: 1
          total_failed_items: 1

---
test_name: CREATE USERS RBAC

stages:

  - name: Create one specified user (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      json:
        username: newUser
        password: stringA1!
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 106
              username: newUser
              allow_run_as: false
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: CREATE ROLES AND POLICIES RBAC

stages:

  - name: Create one specified role (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      json:
        name: newUserRole
    response:
      <<: *permission_denied

  - name: Create one specified policy (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      json:
        name: newUserPolicy
        policy:
          actions:
            - security:delete
          effect: allow
          resources:
            - role:id:*
            - policy:id:*
    response:
      <<: *permission_denied

---
test_name: DELETE LINK ROLE-POLICIES RBAC

stages:

  - name: Delete one specified link between one user and a list of roles (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/104/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 100,103,104,102,101,5
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              roles: []
              username: rbac
          failed_items:
            - error:
                code: 4016
              id:
                - 5
                - 100
                - 101
          total_affected_items: 3
          total_failed_items: 3

---
test_name: CREATE LINK ROLE-POLICIES RBAC

stages:

  - name: Create one specified link between one role and a list of policies (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/104/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        policy_ids: 104,105,106
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              policies:
                - 101
                - 102
                - 105
                - 106
          total_affected_items: 2
          total_failed_items: 1
          failed_items:
            - error:
                code: 4000
              id:
                - 104

---
test_name: DELETE LINK ROLE-POLICIES RBAC

stages:

  - name: Delete one specified link between one role and a list of policies (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/104/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 104,105,106
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              policies:
                - 101
                - 102
                - 109
          total_affected_items: 2
          total_failed_items: 1
          failed_items:
            - error:
                code: 4010
              id:
                - 104

---
test_name: CREATE LINK ROLE-RULES RBAC

stages:

  - name: Create one specified link between one role and a list of rules (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/101/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: POST
      params:
        rule_ids: 103,104,105
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 101
              rules:
                - 101
                - 103
                - 105
          total_affected_items: 2
          total_failed_items: 1
          failed_items:
            - id:
                - 104
              error:
                code: 4000

---
test_name: DELETE LINK ROLE-RULES RBAC

stages:

  - name: Delete one specified link between one role and a list of rules (Partially allow) (Agnostic user)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/101/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: "all"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 101
              rules:
                - 105
          total_affected_items: 2
          failed_items: []
          total_failed_items: 0

  - name: Delete one specified link between one role and a list of rules (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/101/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids:
          - 105
    response:
      <<: *permission_denied

---
test_name: DELETE USERS RBAC

stages:

  - name: Delete one specified user in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 100
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 100
              username: administrator
              allow_run_as: false
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Delete all allowed user in the system (All)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        user_ids: all
      method: DELETE
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
                - 100
            - id: 103
              username: python
              allow_run_as: false
              roles:
                - 101
                - 100
            - id: 104
              username: rbac
              allow_run_as: false
              roles: []
            - id: 106
              username: newUser
              allow_run_as: false
              roles: []
          total_affected_items: 4
          total_failed_items: 0
          failed_items: []

  - name: Delete all allowed user in the system (All)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        user_ids: all
      method: DELETE
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Delete a list of users in the system (Allow and deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 1,100,105
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4000
              id:
                - 105
            - error:
                code: 5001
              id:
                - 100
            - error:
                code: 5004
              id:
                - 1
          total_affected_items: 0
          total_failed_items: 3

---
test_name: DELETE RULES RBAC

stages:

  - name: Delete one specified rule in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 103
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

  - name: Delete all rules in the system (Allow and deny)
    delay_after: 5
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        rule_ids: all
      method: DELETE
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: # Admin rules cannot be deleted. 105 is denied
            - id: 100
            - id: 101
            - id: 102
            - id: 104
          failed_items: []
          total_affected_items: 4
          total_failed_items: 0

---
test_name: DELETE /security/rules (invalid)

stages:

  - name: Delete one specified rule in the system (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 105
    response:
      <<: *permission_denied

---
test_name: DELETE POLICIES RBAC

stages:

  - name: Delete one specified policy in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 103
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              name: administratorPolicyModified
              policy:
                actions:
                  - agent:read
                effect: deny
                resources:
                  - agent:id:097
                  - agent:id:002
                  - agent:id:003
              roles: []
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Delete all policies in the system (Allow and deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 18
          total_failed_items: 0
    delay_after: 20

---
test_name: DELETE ROLES RBAC

stages:

  - name: Delete one specified role in the system (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 103
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

  - name: Delete all roles in the system (Allow and deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        role_ids: all
      method: DELETE
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 5
          total_failed_items: 0
    delay_after: 20

---
test_name: REVOKE TOKENS RBAC

stages:

  - name: Revoke all tokens (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/revoke"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
    response:
      status_code: 200

  - name: Revoke all tokens (Invalid token after previous call)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/revoke"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
    response:
      status_code: 401
