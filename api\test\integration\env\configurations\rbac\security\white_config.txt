| Resource    | List                                                             |
|-------------|------------------------------------------------------------------|
| user:id     | wazuh,wazuh-wui,administrator,normal,ossec,python,rbac,guest     |
| role:id     | 1,2,3,4,5,6,7,8,9,10,11,12,13                                    |
| policy:id   | 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20               |
| rule:id     | 1,2,100,101,102,103,104,105                                      |
| user:role   | (1:1,8),(2:2,9),(100:9),(101:12,13,11),                          |
|             | (102:9,12),(103:),(104:12,10,11),(105:)                          |
| role:rule   | (1:1),(2:2),(100,100),(101,101),(102,102),(103,103),(104,104),   |
|             | (105,105)                                                        |
| role:policy | (1:1),(2:1),(10:11,12,15,18),(11:14,15),(12:12,13,20),(13:*)     |


| Actions               | Resources | Allow           | Deny |
|-----------------------|-----------|-----------------|------|
| security:read         | user:id   | 100,101,103,105 |      |
| security:read         | role:id   | *               |      |
| security:read         | rule:id   | *               |      |
| security:read         | policy:id |                 | *    |
| security:update       | user:id   |                 | *    |
| security:update       | role:id   | 102,103,105     |      |
| security:update       | rule:id   | 102             |      |
| security:update       | policy:id | 104,102,107     |      |
| security:delete       | user:id   | 105,101,2       |      |
| security:delete       | role:id   |                 | *    |
| security:delete       | rule:id   |                 | *    |
| security:delete       | policy:id |                 | *    |
| security:create       | *         | *               |      |
| security:create_user  | *         |                 | *    |
| security:revoke       | *         |                 | *    |
| security:read_config  | *         | *               |      |
| security:update_config| *         | *               |      |
| security:edit_run_as  | *         | *               |      |
