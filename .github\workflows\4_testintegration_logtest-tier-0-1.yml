name: Integration tests for Logtest - Tier 0 and 1

on:
  workflow_dispatch:
    inputs:
      base_branch:
        description: 'Base branch'
        required: true
        default: 'main'

jobs:
  build:
    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
      BRANCH_BASE: ${{ github.base_ref || inputs.base_branch }}
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout Rep<PERSON>
        uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version-file: ".github/workflows/.python-version-it"
          architecture: x64
      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake
      # Build wazuh server for linux.
      - name: Build wazuh server for linux
        run: |
          make deps -C src TARGET=server -j2
          make -C src TARGET=server -j2
      # Install wazuh server for linux.
      - name: Install wazuh server for linux
        run: |
          echo 'USER_LANGUAGE="en"' > ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_NO_STOP="y"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_INSTALL_TYPE="server"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo "USER_DIR=/var/ossec" >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_EMAIL="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SYSCHECK="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_ROOTCHECK="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SYSCOLLECTOR="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SCA="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_WHITE_LIST="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SYSLOG="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_AUTHD="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_AUTO_START="y"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          sudo sh install.sh
          rm ./etc/preloaded-vars.conf
      # Download and install integration tests framework.
      - name: Download and install integration tests framework
        run: |
          if [ "X`git ls-remote https://github.com/wazuh/qa-integration-framework.git ${BRANCH_NAME}`" != "X" ]; then
              QA_BRANCH=${BRANCH_NAME}
          elif [ "X`git ls-remote https://github.com/wazuh/qa-integration-framework.git ${BRANCH_BASE}`" != "X" ]; then
              QA_BRANCH=${BRANCH_BASE}
          else
              QA_BRANCH="main"
          fi
          git clone -b ${QA_BRANCH} --single-branch https://github.com/wazuh/qa-integration-framework.git
          sudo pip install qa-integration-framework/
          sudo rm -rf qa-integration-framework/
      # Run logtest integration tests.
      - name: Run logtest integration tests
        run: |
          cd tests/integration
          sudo python -m pytest --tier 0 --tier 1 test_logtest/
