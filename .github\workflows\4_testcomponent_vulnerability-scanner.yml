name: Vulnerability Scanner

on:
  workflow_dispatch:

jobs:
  style-and-documentation:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          submodules: recursive
          fetch-depth: 0 # We need to fetch all the history to calculate the deltas.

      # Check which modules have been modified.
      # Could be used to avoid some steps if possible (Doxygen, Style, etc.)
      - name: Calculate deltas
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "ROUTER=true" >> "$GITHUB_ENV"
            echo "CONTENT_MANAGER=true" >> "$GITHUB_ENV"
            echo "INDEXER_CONNECTOR=true" >> "$GITHUB_ENV"
            echo "VULNERABILITY_SCANNER=true" >> "$GITHUB_ENV"
          else
            DIFFS=""
            # Get the list of modified files
            if [ -z ${{ env.ACT }} ]; then
              # CI execution
              DIFFS=$(git diff --name-only origin/${{ github.base_ref }})
            else
              # Local execution
              if [ -n "${{ env.REF_BRANCH }}" ]; then
                # If REF_BRANCH is set, use it to calculate the diff
                DIFFS=$(git diff --name-only origin/${{ env.REF_BRANCH }})
              else
                # If REF_BRANCH is not set, use main as base
                DIFFS=$(git diff --name-only origin/main)
              fi
            fi

            if echo $DIFFS | grep 'src/shared_modules/router/'; then
              echo "ROUTER=true" >> "$GITHUB_ENV"
            fi

            if echo $DIFFS | grep 'src/shared_modules/content_manager/'; then
              echo "CONTENT_MANAGER=true" >> "$GITHUB_ENV"
            fi

            if echo $DIFFS | grep 'src/shared_modules/indexer_connector/'; then
              echo "INDEXER_CONNECTOR=true" >> "$GITHUB_ENV"
            fi

            if echo $DIFFS | grep 'src/wazuh_modules/vulnerability_scanner/'; then
              echo "VULNERABILITY_SCANNER=true" >> "$GITHUB_ENV"
            fi
          fi
      # Router
      - name: Router - Coding style
        if: env.ROUTER
        uses: ./.github/actions/clang_format
        with:
          path: src/shared_modules/router
          id: router

      - name: Router - Doxygen
        if: env.ROUTER
        uses: ./.github/actions/doxygen
        with:
          path: src/shared_modules/router
          id: router

      # Indexer connector
      - name: Indexer connector - Coding style
        if: env.INDEXER_CONNECTOR
        uses: ./.github/actions/clang_format
        with:
          path: src/shared_modules/indexer_connector
          id: indexer_connector

      - name: Indexer connector - Doxygen
        if: env.INDEXER_CONNECTOR
        uses: ./.github/actions/doxygen
        with:
          path: src/shared_modules/indexer_connector
          id: indexer_connector

      # Content manager
      - name: Content manager - Coding style
        if: env.CONTENT_MANAGER
        uses: ./.github/actions/clang_format
        with:
          path: src/shared_modules/content_manager
          id: content_manager

      - name: Content manager - Doxygen
        if: env.CONTENT_MANAGER
        uses: ./.github/actions/doxygen
        with:
          path: src/shared_modules/content_manager
          id: content_manager

      # Vulnerability scanner
      - name: Vulnerability scanner - Coding style
        if: env.VULNERABILITY_SCANNER
        uses: ./.github/actions/clang_format
        with:
          path: src/wazuh_modules/vulnerability_scanner
          id: vulnerability_scanner

      - name: Vulnerability scanner - Doxygen
        if: env.VULNERABILITY_SCANNER
        uses: ./.github/actions/doxygen
        with:
          path: src/wazuh_modules/vulnerability_scanner
          id: vulnerability_scanner

  vulnerability-scanner-modules:
    needs: style-and-documentation
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake

      - name: Project dependencies
        uses: ./.github/actions/vulnerability_scanner_deps

      ########################
      # Compilation and test #
      ########################

      # Router
      - name: Router - Compilation and test
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/router

      - name: Router - Coverage
        uses: ./.github/actions/coverage
        with:
          path: src/shared_modules/router
          id: router

      # Indexer connector
      - name: Indexer connector - Compilation and test
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/indexer_connector

      - name: Indexer connector - Coverage
        uses: ./.github/actions/coverage
        with:
          path: src/shared_modules/indexer_connector
          id: indexer_connector

      # Content manager
      - name: Content manager - Compilation and test
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/content_manager

      - name: Content manager - Coverage
        uses: ./.github/actions/coverage
        with:
          path: src/shared_modules/content_manager
          id: content_manager

      # Utils
      - name: Utils - Compilation and test
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/utils
          flags: "-DTARGET=server"

      - name: Utils - Coverage
        uses: ./.github/actions/coverage
        with:
          path: src/shared_modules/utils
          id: utils

      # Vulnerability scanner
      - name: Vulnerability scanner - Compilation and test
        uses: ./.github/actions/compile_and_test
        with:
          path: src/wazuh_modules/vulnerability_scanner

      - name: Vulnerability scanner - Coverage
        uses: ./.github/actions/coverage
        with:
          path: src/wazuh_modules/vulnerability_scanner
          id: vulnerability_scanner

  vulnerability-scanner-modules-asan:
    needs: style-and-documentation

    strategy:
      matrix:
        os: [ ubuntu-22.04, ubuntu-24.04 ]
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake

      - name: Project dependencies
        uses: ./.github/actions/vulnerability_scanner_deps

      ########################
      # Compilation and test #
      ########################

      # Router
      - name: Router
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/router
          asan: "true"

      # indexer connector
      - name: Indexer connector
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/indexer_connector
          asan: "true"

      # content manager
      - name: Content manager
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/content_manager
          asan: "true"

      # Utils
      - name: Utils
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/utils
          asan: "true"
          flags: "-DTARGET=server"

      # Vulnerability scanner
      - name: Vulnerability scanner
        uses: ./.github/actions/compile_and_test
        with:
          path: src/wazuh_modules/vulnerability_scanner
          asan: "true"

  vulnerability-scanner-modules-qa:
    runs-on: ubuntu-22.04

    strategy:
      fail-fast: false
      matrix:
          # Identifiers of the generated content. The generated files will be named vd_1.0.0_vd_<content_version>.tar.xz
          # From 4.11.0 onwards we use 4.11.0 content
          content_version: ["4.11.0"]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake

      - name: Project dependencies
        uses: ./.github/actions/vulnerability_scanner_deps

      # Router
      - name: Router
        uses: ./.github/actions/compile
        with:
          path: src/shared_modules/router

      # indexer connector
      - name: Indexer connector
        uses: ./.github/actions/compile
        with:
          path: src/shared_modules/indexer_connector

      # content manager
      - name: Content manager
        uses: ./.github/actions/compile
        with:
          path: src/shared_modules/content_manager

      # Utils
      - name: Utils
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/utils
          flags: "-DTARGET=server"
          test: "false"

      # Vulnerability scanner
      - name: Vulnerability scanner
        uses: ./.github/actions/compile
        with:
          path: src/wazuh_modules/vulnerability_scanner

      # Download feed
      - name: Download feed
        run: |
          # Download feed
          cd src
          vd_filename="vd_1.0.0_vd_${{ matrix.content_version }}.tar.xz"
          curl -LO https://packages.wazuh.com/deps/vulnerability_model_database/${vd_filename}
          mkdir -p tmp
          mv ${vd_filename} tmp

      # Install python dependencies
      - name: Install dependencies
        run: |
          pip install -r src/wazuh_modules/vulnerability_scanner/qa/requirements.txt

      # Create folder for test logs
      - name: Create folder for test logs
        run: |
          mkdir -p ${{ github.workspace }}/qa_logs

      # Run vulnerability scan tests.
      - name: Run tests
        run: |
          cd src
          python -m pytest -vv wazuh_modules/vulnerability_scanner/qa/ --log-cli-level=DEBUG
          rm -rf tmp

      # Upload log files of the tests
      - name: Upload log files
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: QA log files
          path: ${{ github.workspace }}/qa_logs
