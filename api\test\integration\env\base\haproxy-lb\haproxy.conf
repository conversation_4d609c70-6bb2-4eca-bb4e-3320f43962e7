global
    log /dev/log    local0
    log /dev/log    local1 notice
    stats timeout 30s
    daemon

defaults
    log     global
    mode    tcp
    option  tcplog
    option  dontlognull
    timeout connect 5000
    timeout client  50000
    timeout server  50000
    default-server init-addr last,libc,none

frontend health
  mode http
  bind 127.0.0.1:80
  http-request return status 200 if { src 127.0.0.0/8 }

frontend mycluster
    bind *:1514
    default_backend mycluster_backend

frontend register
    bind *:1515
    default_backend register_backend

backend mycluster_backend
    balance leastconn
    server wazuh-master wazuh-master:1514
    server wazuh-worker1 wazuh-worker1:1514
    server wazuh-worker2 wazuh-worker2:1514

backend register_backend
    balance roundrobin
    server wazuh-master wazuh-master:1515
    server wazuh-worker1 wazuh-worker1:1515
    server wazuh-worker2 wazuh-worker2:1515

