name: Legacy unit tests for wazuh

on:
  workflow_dispatch:
    inputs:
      base_branch:
        description: 'Base branch'
        required: true
        default: 'main'

jobs:
  Unit-Tests:
    strategy:
          fail-fast: false
          matrix:
              target: [agent, winagent, server]
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
      - name: "Install dependencies"
        uses: ./.github/actions/install_build_deps
        with:
          target: ${{ matrix.target }}
      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake
      - name: "Build wazuh"
        uses: ./.github/actions/build_test_flags
        with:
          target: ${{ matrix.target }}
      - name: "Build wazuh legacy unit tests"
        uses: ./.github/actions/legacy_unit_tests_build
        with:
          target: ${{ matrix.target }}
      - name: "Run wazuh legacy unit tests"
        uses: ./.github/actions/legacy_unit_tests_run
        with:
          target: ${{ matrix.target }}
