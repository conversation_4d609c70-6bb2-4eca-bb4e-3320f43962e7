name: Vulnerability Scanner - Database Generation

on:
  schedule:
    - cron: '20 0 * * *'

  workflow_dispatch:
    inputs:
      content_version:
        description: 'Identifier of the generated content. The generated file will be named vd_1.0.0_vd_<content_version>.tar.xz'
        required: true
        type: string

      upload_to_s3:
        description: 'Upload the generated content to S3'
        required: false
        default: false
        type: boolean

jobs:
  vulnerability_scanner_database_scheduled_update:
    if: ${{ (github.event_name == 'schedule') || (github.event_name == 'pull_request') }}

    runs-on: ubuntu-24.04

    strategy:
      fail-fast: false
      matrix:
        # Identifiers of the generated content. The generated files will be named vd_1.0.0_vd_<content_version>.tar.xz
        content_version: ["4.8.0", "4.10.0", "4.11.0"]
        include:
          - content_version: "4.8.0"
          - content_version: "4.10.0"
          - content_version: "4.11.0"

    steps:
      # Checkout repository to the default branch
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      # Checkout to the tag. If it doesn't exist, continue with the branch
      - name: Checkout to ${{ matrix.content_version }}
        run: |
          # Backup current workflows and actions files before checkout new branch.
          cp -r .github/{actions,workflows} /tmp

          if git show-ref --tags --verify --quiet "refs/tags/v${{ matrix.content_version }}"; then
            git checkout --quiet "tags/v${{ matrix.content_version }}"
          else
            echo "Warning: Unable to find tag v${{ matrix.content_version }}. Continuing with branch ${{ matrix.content_version }}"
            git show-ref --quiet "refs/remotes/origin/${{ matrix.content_version }}"
            if [ $? -eq 0 ]; then
              git checkout --quiet "${{ matrix.content_version }}"
            else
              echo "Warning: Unable to find branch ${{ matrix.content_version }}. Exiting"
              exit 1
            fi
          fi
          echo "Git branch: $(git branch | grep "*")"

          # We restore the actions from main
          # to re-use them independently from the checkout branch
          cp -r /tmp/{actions,workflows} .github/

      # Restore missing template file.
      - name: Restore missing template file
        run: |
          # Previous version (4.8.0) expects the template file to be in templates/vd_states_template.json location
          template=src/wazuh_modules/vulnerability_scanner/indexer/template/index-template.json
          if [ -f "$template" ]; then
            if [ ! -d "templates" ]; then
              mkdir templates
            fi
            cp "$template" templates/vd_states_template.json
          fi

      ########################
      # Compilation          #
      ########################
      - name: Compile
        uses: ./.github/actions/vulnerability_scanner/compile

      ########################
      # Content generation   #
      ########################
      - name: Generate vulnerability database
        uses: ./.github/actions/vulnerability_scanner/content_generation
        with:
          content_version: ${{ matrix.content_version }}

      ########################
      # Content upload       #
      ########################
      - name: Upload database to S3
        if: ${{ github.event_name == 'schedule' }}
        run: |
          root_folder=$(pwd)
          bucket="${{ secrets.FEED_AWS_BUCKET }}"
          file="vd_1.0.0_vd_${{ matrix.content_version }}.tar.xz"
          dest_dir="deps/vulnerability_model_database"
          aws s3 cp ${file} s3://${bucket}/${dest_dir}/${file} --acl public-read
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.FEED_AWS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.FEED_AWS_SECRET_ACCESS_KEY}}
          AWS_DEFAULT_REGION: 'us-west-1'
        shell: bash

  vulnerability_scanner_database_manual_update:
      if: ${{ github.event_name == 'workflow_dispatch' }}

      runs-on: ubuntu-24.04

      steps:
        - name: Checkout repository
          uses: actions/checkout@v3
          with:
            fetch-depth: 0

        ########################
        # Compilation          #
        ########################
        - name: Compile
          uses: ./.github/actions/vulnerability_scanner/compile

        ########################
        # Content generation   #
        ########################
        - name: Generate vulnerability database
          uses: ./.github/actions/vulnerability_scanner/content_generation
          with:
            content_version: ${{ inputs.content_version }}

        ########################
        # Content upload       #
        ########################
        - name: Upload database to S3
          if: ${{ inputs.upload_to_s3 }}
          run: |
            root_folder=$(pwd)
            bucket="${{ secrets.FEED_AWS_BUCKET }}"
            file="vd_1.0.0_vd_${{ inputs.content_version }}.tar.xz"
            dest_dir="deps/vulnerability_model_database"
            aws s3 cp ${file} s3://${bucket}/${dest_dir}/${file} --acl public-read
          env:
            AWS_ACCESS_KEY_ID: ${{ secrets.FEED_AWS_KEY_ID }}
            AWS_SECRET_ACCESS_KEY: ${{ secrets.FEED_AWS_SECRET_ACCESS_KEY}}
            AWS_DEFAULT_REGION: 'us-west-1'
          shell: bash
