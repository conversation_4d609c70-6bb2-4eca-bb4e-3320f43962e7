---
test_name: GET /syscollector/{agent_id}/os

stages:

  - name: Get the OS of an agent
    request: &os_request_001
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/os"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - architecture: !anystr
              hostname: !anystr
              os:
                codename: !anystr
                major: !anystr
                minor: !anystr
                name: !anystr
                platform: !anystr
                version: !anystr
              release: !anystr
              scan:
                id: !anyint
                time: !anystr
              sysname: !anystr
              version: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        message: !anystr
      save:
        json:
          os.version: data.affected_items[0].os.version
          release: data.affected_items[0].release
          sysname: data.affected_items[0].sysname

  - name: Invalid selector
    request:
      verify: False
      <<: *os_request_001
      params:
        select: wrongParam
    response: &error_response_1724
      status_code: 400
      json:
        error: 1724

  - name: Try to get 012 agent's syscollector OS data (never connected)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/012/os"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        error: 2007

---
test_name: GET /syscollector/{agent_id}/os?{select}

marks:
  - parametrize:
      key: field
      vals:
        - architecture
        - hostname
        - os.codename
        - os.major
        - os.minor
        - os.name
        - os.platform
        - os.version
        - release
        - scan.id
        - scan.time
        - sysname
        - version

stages:

  - name: OS of an agent, select
    request:
      verify: False
      <<: *os_request_001
      params:
        select: "{field}"
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"

---
test_name: GET /syscollector/{agent_id}/hardware

stages:

  - name: Hardware of an agent
    request: &hardware_request_002
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/hardware"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - cpu:
                cores: !anything
                mhz: !anything
                name: !anything
              ram:
                free: !anything
                total: !anything
                usage: !anything
              scan:
                id: !anything
                time: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        message: !anystr
      save:
        json:
          cpu_name: data.affected_items[0].cpu.name
          ram_total: data.affected_items[0].ram.total

  - name: Not allowed selector
    request:
      verify: False
      <<: *hardware_request_002
      params:
        select: wrongParam
    response:
      <<: *error_response_1724

---
test_name: GET /syscollector/{agent_id}/hardware?{select}

marks:
  - parametrize:
      key: field
      vals:
        - cpu.cores
        - cpu.mhz
        - cpu.name
        - ram.free
        - ram.total
        - ram.usage
        - scan.id
        - scan.time

stages:

  - name: Hardware of an agent, select
    request:
      verify: False
      <<: *hardware_request_002
      params:
        select: "{field}"
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"

---
test_name: GET /syscollector/{agent_id}/packages

stages:

  - name: Packages of and agent
    request: &packages_request_003
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/003/packages"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &packages_response
            - architecture: !anystr
              description: !anystr
              format: !anystr
              name: !anystr
              priority: !anystr
              scan:
                id: !anyint
                time: !anystr
              section: !anystr
              size: !anyint
              vendor: !anystr
              version: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      save:
        json:
          expected_scan_id: data.affected_items[0].scan.id
          expected_description: data.affected_items[0].description
          expected_architecture: data.affected_items[0].architecture
          expected_version: data.affected_items[0].version

  - name: Multiple selectors with limit
    request:
      verify: False
      <<: *packages_request_003
      params:
        select: scan.id,description,architecture
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - architecture: "{expected_architecture}"
              description: "{expected_description}"
              scan:
                id: !int "{expected_scan_id}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Not allowed selector
    request:
      verify: False
      <<: *packages_request_003
      params:
        select: wrongParam
    response:
      <<: *error_response_1724

  - name: Filter by query
    request:
      verify: False
      <<: *packages_request_003
      params:
        q: scan.id={expected_scan_id};architecture={expected_architecture};description={expected_description}
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - architecture: "{expected_architecture}"
              description: "{expected_description}"
              scan:
                id: !int "{expected_scan_id}"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
        message: !anystr

  - name: Limit and offset
    request:
      verify: False
      <<: *packages_request_003
      params:
        offset: 0
        limit: 2
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response
            - <<: *packages_response
      save:
        json:
          offset_test_architecture: data.affected_items[1].architecture
          offset_test_description: data.affected_items[1].description
          offset_test_format: data.affected_items[1].format
          offset_test_name: data.affected_items[1].name
          offset_test_priority: data.affected_items[1].priority
          offset_test_scan_id: data.affected_items[1].scan.id
          offset_test_scan_time: data.affected_items[1].scan.time
          offset_test_section: data.affected_items[1].section
          offset_test_size: data.affected_items[1].size
          offset_test_vendor: data.affected_items[1].vendor
          offset_test_version: data.affected_items[1].version

  - name: Limit and offset test
    request:
      verify: False
      <<: *packages_request_003
      params:
        offset: 1
        limit: 2
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - architecture: "{offset_test_architecture}"
              description: "{offset_test_description}"
              format: "{offset_test_format}"
              name: "{offset_test_name}"
              priority: "{offset_test_priority}"
              scan:
                id: !int "{offset_test_scan_id}"
                time: "{offset_test_scan_time}"
              section: "{offset_test_section}"
              size: !int "{offset_test_size}"
              vendor: "{offset_test_vendor}"
              version: "{offset_test_version}"
            - <<: *packages_response
      save:
        json:
          expected_name: data.affected_items[0].name
          expected_vendor: data.affected_items[0].vendor

  - name: Wrong limit
    request:
      verify: False
      <<: *packages_request_003
      params:
        offset: 0
        limit: 1a
    response:
      status_code: 400

  - name: Invalid limit
    request:
      verify: False
      <<: *packages_request_003
      params:
        offset: 0
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *packages_request_003
      params:
        sort: -name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"
            reverse: true
      status_code: 200

  - name: Filter sort 2
    request:
      verify: False
      <<: *packages_request_003
      params:
        sort: +name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"
      status_code: 200

  - name: Wrong sort
    request:
      verify: False
      <<: *packages_request_003
      params:
        sort: wrongParam
    response: &error_response_1403
      status_code: 400
      json:
        error: 1403

  - name: Search an expected name
    request:
      verify: False
      <<: *packages_request_003
      params:
        search: "{expected_name}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response

  - name: Search an expected vendor
    request:
      verify: False
      <<: *packages_request_003
      params:
        search: ubuntu
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response

  - name: Filter by vendor
    request:
      verify: False
      <<: *packages_request_003
      params:
        vendor: "Ubuntu Core developers <<EMAIL>>"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response

  - name: Search an expected name
    request:
      verify: False
      <<: *packages_request_003
      params:
        name: "binutils"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response
      save:
        json:
          name: data.affected_items[0].name

  - name: Filter by name
    request:
      verify: False
      <<: *packages_request_003
      params:
        name: "{expected_name}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response
      save:
        json:
          architecture: data.affected_items[0].architecture

  - name: Filter by architecture
    request:
      verify: False
      <<: *packages_request_003
      params:
        architecture: "{expected_architecture}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response
      save:
        json:
          expected_format: data.affected_items[0].format
          expected_vendor: data.affected_items[0].vendor

  - name: Filter by format
    request:
      verify: False
      <<: *packages_request_003
      params:
        format: "{expected_format}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response

  - name: Filter by version
    request:
      verify: False
      <<: *packages_request_003
      params:
        version: "{expected_version}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response

  - name: Filter by format
    request:
      verify: False
      <<: *packages_request_003
      params:
        format: "{expected_format}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *packages_response

  - name: Get distinct packages info
    request:
      verify: False
      <<: *packages_request_003
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /syscollector/{agent_id}/packages?{select}

marks:
  - parametrize:
      key: field
      vals:
        - architecture
        - description
        - format
        - name
        - priority
        - scan.id
        - scan.time
        - section
        - size
        - vendor
        - version

stages:

  - name: Packages of an agent, select
    request:
      verify: False
      <<: *packages_request_003
      params:
        select: "{field:s}"
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"

---
test_name: GET /syscollector/{agent_id}/processes

stages:

  - name: Processes of an agent with limit = 1
    request: &processes_request_004
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/004/processes"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &processes_response_004
            - egroup: !anystr
              euser: !anystr
              fgroup: !anystr
              name: !anystr
              nice: !anyint
              nlwp: !anyint
              pgrp: !anyint
              pid: !anystr
              ppid: !anyint
              priority: !anyint
              processor: !anyint
              resident: !anyint
              rgroup: !anystr
              ruser: !anystr
              scan:
                id: !anyint
                time: !anystr
              session: !anyint
              sgroup: !anystr
              share: !anyint
              size: !anyint
              start_time: !anyint
              state: !anystr
              stime: !anyint
              suser: !anystr
              tgid: !anyint
              tty: !anyint
              utime: !anyint
              vm_size: !anyint
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      save:
        json:
          expected_tty: data.affected_items[0].tty
          expected_sgroup: data.affected_items[0].sgroup
          expected_share: data.affected_items[0].share
          expected_session: data.affected_items[0].session
          expected_scan_id: data.affected_items[0].scan.id
          expected_pid: data.affected_items[0].pid
          expected_ppid: data.affected_items[0].ppid
          expected_state: data.affected_items[0].state
          expected_egroup: data.affected_items[0].egroup
          expected_euser: data.affected_items[0].euser
          expected_ruser: data.affected_items[0].ruser
          expected_suser: data.affected_items[0].suser
          expected_fgroup: data.affected_items[0].fgroup
          expected_rgroup: data.affected_items[0].rgroup
          expected_name: data.affected_items[0].name
          expected_nlwp: data.affected_items[0].nlwp
          expected_pgrp: data.affected_items[0].pgrp
          expected_priority: data.affected_items[0].priority

  - name: Filter by pid
    request:
      verify: False
      <<: *processes_request_004
      params:
        pid: "{expected_pid}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - pid: "{expected_pid}"

  - name: Filter by ppid
    request:
      verify: False
      <<: *processes_request_004
      params:
        ppid: "{expected_ppid}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - ppid: !int "{expected_ppid}"

  - name: Filter by state
    request:
      verify: False
      <<: *processes_request_004
      params:
        state: "{expected_state}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - state: "{expected_state}"

  - name: Filter by egroup
    request:
      verify: False
      <<: *processes_request_004
      params:
        egroup: "{expected_egroup}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - egroup: "{expected_egroup}"

  - name: Filter by fgroup
    request:
      verify: False
      <<: *processes_request_004
      params:
        fgroup: "{expected_fgroup}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - fgroup: "{expected_fgroup}"

  - name: Filter by sgroup
    request:
      verify: False
      <<: *processes_request_004
      params:
        sgroup: "{expected_sgroup}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - sgroup: "{expected_sgroup}"

  - name: Filter by rgroup
    request:
      verify: False
      <<: *processes_request_004
      params:
        rgroup: "{expected_rgroup}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - rgroup: "{expected_rgroup}"

  - name: Filter by euser
    request:
      verify: False
      <<: *processes_request_004
      params:
        euser: "{expected_euser}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - euser: "{expected_euser}"

  - name: Filter by ruser
    request:
      verify: False
      <<: *processes_request_004
      params:
        ruser: "{expected_ruser}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - ruser: "{expected_ruser}"

  - name: Filter by suser
    request:
      verify: False
      <<: *processes_request_004
      params:
        suser: "{expected_suser}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - suser: "{expected_suser}"

  - name: Filter by nlwp
    request:
      verify: False
      <<: *processes_request_004
      params:
        nlwp: "{expected_nlwp}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - nlwp: !int "{expected_nlwp}"

  - name: Filter by pgrp
    request:
      verify: False
      <<: *processes_request_004
      params:
        pgrp: "{expected_pgrp}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - pgrp: !int "{expected_pgrp}"

  - name: Filter by priority
    request:
      verify: False
      <<: *processes_request_004
      params:
        priority: "{expected_priority}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - priority: !int "{expected_priority}"

  - name: Filter by name
    request:
      verify: False
      <<: *processes_request_004
      params:
        name: "{expected_name}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - name: "{expected_name}"

  - name: Select by
    request:
      verify: False
      <<: *processes_request_004
      params:
        select: tty,sgroup,share,session,scan.id
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - scan:
                id: !int "{expected_scan_id}"
              session: !int "{expected_session}"
              sgroup: "{expected_sgroup}"
              share: !int "{expected_share}"
              tty: !int "{expected_tty}"

  - name: Filter by query
    request:
      verify: False
      <<: *processes_request_004
      params:
        q: state={expected_state};pid={expected_pid};pgrp={expected_pgrp}
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - state: "{expected_state}"

  - name: Wrong param
    request:
      verify: False
      <<: *processes_request_004
      params:
        select: wrongParam
    response:
      <<: *error_response_1724

  - name: offset and limit
    request:
      verify: False
      <<: *processes_request_004
      params:
        offset: 0
        limit: 2
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *processes_response_004
            - <<: *processes_response_004
      save:
        json:
          offset_test_egroup: data.affected_items[1].egroup
          offset_test_euser: data.affected_items[1].euser
          offset_test_fgroup: data.affected_items[1].fgroup
          offset_test_name: data.affected_items[1].name
          offset_test_nice: data.affected_items[1].nice
          offset_test_nlwp: data.affected_items[1].nlwp
          offset_test_pgrp: data.affected_items[1].pgrp
          offset_test_pid: data.affected_items[1].pid
          offset_test_ppid: data.affected_items[1].ppid
          offset_test_priority: data.affected_items[1].priority
          offset_test_processor: data.affected_items[1].processor
          offset_test_resident: data.affected_items[1].resident
          offset_test_rgroup: data.affected_items[1].rgroup
          offset_test_ruser: data.affected_items[1].ruser
          offset_test_scan_id: data.affected_items[1].scan.id
          offset_test_scan_time: data.affected_items[1].scan.time
          offset_test_session: data.affected_items[1].session
          offset_test_sgroup: data.affected_items[1].sgroup
          offset_test_share: data.affected_items[1].share
          offset_test_size: data.affected_items[1].size
          offset_test_start_time: data.affected_items[1].start_time
          offset_test_state: data.affected_items[1].state
          offset_test_stime: data.affected_items[1].stime
          offset_test_suser: data.affected_items[1].suser
          offset_test_tgid: data.affected_items[1].tgid
          offset_test_tty: data.affected_items[1].tty
          offset_test_utime: data.affected_items[1].utime
          offset_test_vm_size: data.affected_items[1].vm_size

  - name: offset and limit check
    request:
      verify: False
      <<: *processes_request_004
      params:
        offset: 1
        limit: 1
    response:
      status_code: 200
      strict: false
      json:
        error: !anyint
        data:
          affected_items:
            - egroup: "{offset_test_egroup}"
              euser: "{offset_test_euser}"
              fgroup: "{offset_test_fgroup}"
              name: "{offset_test_name}"
              nice: !int "{offset_test_nice}"
              nlwp: !int "{offset_test_nlwp}"
              pgrp: !int "{offset_test_pgrp}"
              pid: "{offset_test_pid}"
              ppid: !int "{offset_test_ppid}"
              priority: !int "{offset_test_priority}"
              processor: !int "{offset_test_processor}"
              resident: !int "{offset_test_resident}"
              rgroup: "{offset_test_rgroup}"
              ruser: "{offset_test_ruser}"
              scan:
                id: !int "{offset_test_scan_id}"
                time: "{offset_test_scan_time}"
              session: !int "{offset_test_session}"
              sgroup: "{offset_test_sgroup}"
              share: !int "{offset_test_share}"
              size: !int "{offset_test_size}"
              start_time: !int "{offset_test_start_time}"
              state: "{offset_test_state}"
              stime: !int "{offset_test_stime}"
              suser: "{offset_test_suser}"
              tgid: !int "{offset_test_tgid}"
              tty: !int "{offset_test_tty}"
              utime: !int "{offset_test_utime}"
              vm_size: !int "{offset_test_vm_size}"

  - name: Wrong limit
    request:
      verify: False
      <<: *processes_request_004
      params:
        limit: 0
    response:
      status_code: 400

  - name: Get distinct processes info
    request:
      verify: False
      <<: *processes_request_004
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /syscollector/{agent_id}/processes?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - argvs
        - cmd
        - egroup
        - euser
        - fgroup
        - name
        - nice
        - nlwp
        - pgrp
        - pid
        - ppid
        - priority
        - processor
        - resident
        - rgroup
        - ruser
        - scan.id
        - session
        - sgroup
        - share
        - size
        - start_time
        - state
        - stime
        - suser
        - tgid
        - tty
        - utime
        - vm_size

stages:

  - name: Processes of an agent, sort
    request:
      verify: False
      <<: *processes_request_004
      params:
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

  - name: Processes of an agent, select
    request:
      verify: False
      <<: *processes_request_004
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"

---
test_name: GET /syscollector/{agent_id}/ports

stages:

  - name: Ports of an agent
    request: &port_request_005
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/005/ports"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

  - name: Ports of an agent, limit
    request:
      verify: False
      <<: *port_request_005
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &port_response
            - inode: !anyint
              local:
                ip: !anystr
                port: !anyint
              protocol: !anystr
              remote:
                ip: !anystr
                port: !anyint
              rx_queue: !anyint
              scan:
                id: !anyint
                time: !anystr
              state: !anystr
              tx_queue: !anyint
      save:
        json:
          expected_protocol: data.affected_items[0].protocol
          expected_local_ip: data.affected_items[0].local.ip
          expected_local_port: data.affected_items[0].local.port
          expected_remote_ip: data.affected_items[0].remote.ip
          expected_tx_queue: data.affected_items[0].tx_queue
          expected_state: data.affected_items[0].state

  - name: Filter protocol
    request:
      verify: False
      <<: *port_request_005
      params:
        protocol: "{expected_protocol}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *port_response

  - name: Filter local.ip
    request:
      verify: False
      <<: *port_request_005
      params:
        local.ip: "{expected_local_ip}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *port_response

  - name: Filter local.port
    request:
      verify: False
      <<: *port_request_005
      params:
        local.port: "{expected_local_port}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *port_response

  - name: Filter remote.ip
    request:
      verify: False
      <<: *port_request_005
      params:
        remote.ip: "{expected_remote_ip}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *port_response

  - name: Filter tx_queue
    request:
      verify: False
      <<: *port_request_005
      params:
        tx_queue: "{expected_tx_queue}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *port_response

  - name: Filter status
    request:
      verify: False
      <<: *port_request_005
      params:
        state: "{expected_state}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *port_response

  - name: Filter pid
    skip: True # This test cannot be performed as usual because the column adapter exists in the database but the field is always empty
    request:
      verify: False
      <<: *port_request_005
      params:
        pid: "???"
    response:
      status_code: 200

  - name: Filter by query
    request:
      verify: False
      <<: *port_request_005
      params:
        q: local.ip={expected_local_ip};state={expected_state};tx_queue={expected_tx_queue};remote.ip={expected_remote_ip}
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *port_response

  - name: Not allowed selector
    request:
      verify: False
      <<: *port_request_005
      params:
        select: wrong
    response:
      <<: *error_response_1724

  - name: Wrong limit
    request:
      verify: False
      <<: *port_request_005
      params:
        limit: 0
    response:
      status_code: 400

  - name: Wrong filter
    request:
      verify: False
      <<: *port_request_005
      params:
        wrongFilter: true
    response:
      status_code: 400

  - name: Invalid select
    request:
      verify: False
      <<: *port_request_005
      params:
        select: wrong
    response:
      <<: *error_response_1724

  - name: Get distinct ports info
    request:
      verify: False
      <<: *port_request_005
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /syscollector/{agent_id}/port?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - inode
        - local.ip
        - local.port
        - protocol
        - remote.ip
        - remote.port
        - rx_queue
        - scan.id
        - scan.time
        - state
        - tx_queue

stages:

  - name: Port of an agent, select
    request:
      verify: False
      <<: *port_request_005
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

  - name: Port of an agent, sort
    request:
      verify: False
      <<: *port_request_005
      params:
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

---
test_name: GET /syscollector/{agent_id}/netaddr

stages:

  - name: Netaddress of an agent
    request: &netaddr_request_002
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/netaddr"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &netaddr_response
            - address: !anystr
              broadcast: !anystr
              iface: !anystr
              netmask: !anystr
              proto: !anystr
              scan:
                id: !anyint
          total_affected_items: !anyint
      save:
        json:
          expected_broadcast: data.affected_items[0].broadcast
          expected_proto: data.affected_items[0].proto
          expected_address: data.affected_items[0].address
          expected_netmask: data.affected_items[0].netmask
          expected_iface: data.affected_items[0].iface

  - name: Filter by iface
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        iface: "{expected_iface}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by iface (incorrect)
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        iface: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by netmask
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        netmask: "{expected_netmask}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by netmask (incorrect)
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        netmask: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by address
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        address: "{expected_address}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by address (incorrect)
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        address: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by proto
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        proto: "{expected_proto}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by proto (incorrect)
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        proto: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by broadcast
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        broadcast: "{expected_broadcast}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by broadcast (incorrect)
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        broadcast: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Search broadcast
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        search: "{expected_broadcast}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - broadcast: "{expected_broadcast}"

  - name: Search protocol
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        search: "{expected_proto}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - proto: "{expected_proto}"

  - name: Search address
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        search: "{expected_address}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - address: "{expected_address}"

  - name: Search netmask
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        search: "{expected_netmask}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - netmask: "{expected_netmask}"

  - name: Search iface
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        search: "{expected_iface}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - iface: "{expected_iface}"

  - name: Select proto, netmask, broadcast
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        select: proto,broadcast,netmask
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - proto: !anystr
              netmask: !anystr
              broadcast: !anystr

  - name: Not allowed selector
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        select: wrongSelector
    response:
      <<: *error_response_1724

  - name: Pagination
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 1

  - name: Wrong limit
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        limit: 0
    response:
      status_code: 400

  - name: Wrong param
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        wrong: 0
    response:
      status_code: 400

  - name: Sort
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        sort: +proto
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "proto"
      status_code: 200

  - name: Invalid sort
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        sort: -wrong
    response:
      <<: *error_response_1403

  - name: Filter by query with broadcast and iface
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        q: broadcast={expected_broadcast};iface={expected_iface}
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Get distinct network address info
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /syscollector/{agent_id}/netaddr?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - address
        - broadcast
        - iface
        - netmask
        - proto
        - scan.id

stages:

  - name: Netaddress of an agent, select

    request:
      verify: False
      <<: *netaddr_request_002
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

  - name: Netaddress of an agent, sort
    request:
      verify: False
      <<: *netaddr_request_002
      params:
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

---
test_name: GET /syscollector/{agent_id}/netproto

stages:

  - name: Netprotocol of an agent
    request: &netproto_request_003
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/003/netproto"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &netproto_response_003
            - dhcp: !anystr
              gateway: !anystr
              iface: !anystr
              type: !anystr
              scan:
                id: !anyint
          total_affected_items: !anyint
      save:
        json:
          expected_dhcp: data.affected_items[0].dhcp
          expected_gateway: data.affected_items[0].gateway
          expected_type: data.affected_items[0].type
          expected_scan_id: data.affected_items[0].scan.id
          expected_iface: data.affected_items[0].iface

  - name: Filter by iface
    request:
      verify: False
      <<: *netproto_request_003
      params:
        iface: "{expected_iface}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response_003

  - name: Filter by iface (incorrect)
    request:
      verify: False
      <<: *netproto_request_003
      params:
        iface: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by type
    request:
      verify: False
      <<: *netproto_request_003
      params:
        type: "{expected_type}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response_003

  - name: Filter by type (incorrect)
    request:
      verify: False
      <<: *netproto_request_003
      params:
        type: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by gateway
    request:
      verify: False
      <<: *netproto_request_003
      params:
        gateway: "{expected_gateway}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response_003

  - name: Filter by gateway (incorrect)
    request:
      verify: False
      <<: *netproto_request_003
      params:
        gateway: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by dhcp
    request:
      verify: False
      <<: *netproto_request_003
      params:
        dhcp: "{expected_dhcp}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response_003

  - name: Filter by dhcp (incorrect)
    request:
      verify: False
      <<: *netproto_request_003
      params:
        dhcp: "invalid"
    response:
      status_code: 400

  - name: Select proto, netmask, broadcast
    request:
      verify: False
      <<: *netproto_request_003
      params:
        select: iface,gateway,dhcp
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - iface: !anystr
              gateway: !anystr
              dhcp: !anystr

  - name: Search dhcp
    request:
      verify: False
      <<: *netproto_request_003
      params:
        search: "{expected_dhcp}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - dhcp: "{expected_dhcp}"

  - name: Search gateway
    request:
      verify: False
      <<: *netproto_request_003
      params:
        search: "{expected_gateway}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - gateway: "{expected_gateway}"

  - name: Search type
    request:
      verify: False
      <<: *netproto_request_003
      params:
        search: "{expected_type}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - type: "{expected_type}"

  - name: Search scan.id
    request:
      verify: False
      <<: *netproto_request_003
      params:
        search: "{expected_scan_id}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - scan:
                id: !int "{expected_scan_id}"

  - name: Search iface
    request:
      verify: False
      <<: *netproto_request_003
      params:
        search: "{expected_iface}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - iface: "{expected_iface}"

  - name: Not allowed selector
    request:
      verify: False
      <<: *netproto_request_003
      params:
        select: wrongSelector
    response:
      <<: *error_response_1724

  - name: Wrong limit
    request:
      verify: False
      <<: *netproto_request_003
      params:
        limit: 0
    response:
      status_code: 400

  - name: Wrong param
    request:
      verify: False
      <<: *netproto_request_003
      params:
        wrong: 0
    response:
      status_code: 400

  - name: Sort
    request:
      verify: False
      <<: *netproto_request_003
      params:
        sort: +dhcp
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "dhcp"
      status_code: 200

  - name: Invalid sort
    request:
      verify: False
      <<: *netproto_request_003
      params:
        sort: -wrong
    response:
      <<: *error_response_1403

  - name: Get distinct network protocol info
    request:
      verify: False
      <<: *netproto_request_003
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /syscollector/{agent_id}/netproto?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - dhcp
        - gateway
        - iface
        - scan.id
        - type

stages:

  - name: Netprotocol of an agent, select
    request:
      verify: False
      <<: *netproto_request_003
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

  - name: Netprotocol of an agent, sort

    request:
      verify: False
      <<: *netproto_request_003
      params:
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

---
test_name: GET /syscollector/{agent_id}/netiface

stages:

  - name: Netiface of an agent
    request: &netiface_request_008
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/008/netiface"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &netiface_response_008
            - mac: !anystr
              mtu: !anyint
              name: !anystr
              rx:
                bytes: !anyint
                dropped: !anyint
                errors: !anyint
                packets: !anyint
              scan:
                id: !anyint
                time: !anystr
              state: !anystr
              tx:
                bytes: !anyint
                dropped: !anyint
                errors: !anyint
                packets: !anyint
              type: !anystr
          total_affected_items: !anyint
      save:
        json:
          expected_mac: data.affected_items[0].mac
          expected_rx_bytes: data.affected_items[0].rx.bytes
          expected_tx_bytes: data.affected_items[0].tx.bytes
          expected_tx_errors: data.affected_items[0].tx.errors
          expected_rx_errors: data.affected_items[0].rx.errors
          expected_rx_packets: data.affected_items[0].rx.packets
          expected_tx_dropped: data.affected_items[0].tx.dropped
          expected_rx_dropped: data.affected_items[0].rx.dropped
          expected_tx_packets: data.affected_items[0].tx.packets
          expected_type: data.affected_items[0].type
          expected_mtu: data.affected_items[0].mtu
          expected_name: data.affected_items[0].name
          expected_state: data.affected_items[0].state

  - name: Select type, rx.bytes, tx.errors, tx.dropped, mac
    request:
      verify: False
      <<: *netiface_request_008
      params:
        select: type,rx.bytes,tx.errors,tx.dropped,mac
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - type: !anystr
              rx:
                bytes: !anyint
              tx:
                errors: !anyint
                dropped: !anyint
              mac: !anystr

  - name: Search mac
    request:
      verify: False
      <<: *netiface_request_008
      params:
        search: "{expected_mac}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - mac: "{expected_mac}"

  - name: Search rx.bytes
    request:
      verify: False
      <<: *netiface_request_008
      params:
        search: "{expected_rx_bytes}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - rx:
                bytes: !int "{expected_rx_bytes}"

  - name: Search tx.errors
    request:
      verify: False
      <<: *netiface_request_008
      params:
        search: !int "{expected_tx_errors}"
        limit: 1

    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - tx:
                errors: !int "{expected_tx_errors}"

  - name: Search tx.dropped
    request:
      verify: False
      <<: *netiface_request_008
      params:
        search: "{expected_tx_dropped}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - tx:
                dropped: !int "{expected_tx_dropped}"

  - name: Not allowed selector
    request:
      verify: False
      <<: *netiface_request_008
      params:
        select: wrongSelector
    response:
      <<: *error_response_1724

  - name: Wrong limit
    request:
      verify: False
      <<: *netiface_request_008
      params:
        limit: 0
    response:
      status_code: 400

  - name: Wrong param
    request:
      verify: False
      <<: *netiface_request_008
      params:
        wrong: 0
    response:
      status_code: 400

  - name: Sort
    request:
      verify: False
      <<: *netiface_request_008
      params:
        sort: +type
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "type"
      status_code: 200

  - name: Invalid sort
    request:
      verify: False
      <<: *netiface_request_008
      params:
        sort: -wrong
    response:
      <<: *error_response_1403

  - name: Offset and limit
    request:
      verify: False
      <<: *netiface_request_008
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Offset = 1 and limit = 1
    request:
      verify: False
      <<: *netiface_request_008
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: !anyint

  - name: Filter mtu
    request:
      verify: False
      <<: *netiface_request_008
      params:
        mtu: "{expected_mtu}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter adapter
    skip: True # This test cannot be performed as usual because the column adapter exists in the database but the field is always empty
    request:
      verify: False
      <<: *netiface_request_008
      params:
        adapter: "???"
    response:
      status_code: 200

  - name: Filter name
    request:
      verify: False
      <<: *netiface_request_008
      params:
        name: "{expected_name}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter type
    request:
      verify: False
      <<: *netiface_request_008
      params:
        type: "{expected_type}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter state
    request:
      verify: False
      <<: *netiface_request_008
      params:
        state: "{expected_state}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter tx.packets
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.packets: "{expected_tx_packets}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter rx.packets
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.packets: "{expected_rx_packets}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter tx.dropped
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.dropped: "{expected_tx_dropped}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter rx.dropped
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.dropped: "{expected_rx_dropped}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter tx.bytes
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.bytes: "{expected_tx_bytes}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter rx.bytes
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.bytes: "{expected_rx_bytes}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter tx.errors
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.errors: "{expected_tx_errors}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter rx.errors
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.errors: "{expected_rx_errors}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response_008
          total_affected_items: !anyint

  - name: Filter by query
    request:
      verify: False
      <<: *netiface_request_008
      params:
        q: mac={expected_mac};state={expected_state}
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - mac: "{expected_mac}"
              state: "{expected_state}"

  - name: Filter invalid tx.packets
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.packets: -1
    response:
      status_code: 400

  - name: Filter invalid tx.packets
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.packets: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.packets
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.packets: -1
    response:
      status_code: 400

  - name: Filter invalid rx.packets
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.packets: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid tx.dropped
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.dropped: -1
    response:
      status_code: 400

  - name: Filter invalid tx.dropped
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.dropped: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.dropped
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.dropped: -1
    response:
      status_code: 400

  - name: Filter invalid rx.dropped
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.dropped: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid tx.bytes
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.bytes: -1
    response:
      status_code: 400

  - name: Filter invalid tx.bytes
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.bytes: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.bytes
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.bytes: -1
    response:
      status_code: 400

  - name: Filter invalid rx.bytes
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.bytes: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid tx.errors
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.errors: -1
    response:
      status_code: 400

  - name: Filter invalid tx.errors
    request:
      verify: False
      <<: *netiface_request_008
      params:
        tx.errors: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.errors
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.errors: -1
    response:
      status_code: 400

  - name: Filter invalid rx.errors
    request:
      verify: False
      <<: *netiface_request_008
      params:
        rx.errors: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Get distinct net interface info
    request:
      verify: False
      <<: *netiface_request_008
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /syscollector/{agent_id}/netiface?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - mac
        - mtu
        - name
        - rx.bytes
        - rx.dropped
        - rx.errors
        - rx.packets
        - scan.id
        - scan.time
        - state
        - tx.bytes
        - tx.dropped
        - tx.errors
        - tx.packets
        - type

stages:

  - name: Netiface of an agent, select
    request:
      verify: False
      <<: *netiface_request_008
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

  - name: Netiface of an agent, sort
    request:
      verify: False
      <<: *netiface_request_008
      params:
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

---
test_name: GET /syscollector/{agent_id}/os

stages:

  - name: Get the OS of an agent
    request: &os_request
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/os"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - architecture: !anystr
              hostname: !anystr
              os:
                codename: !anystr
                major: !anystr
                minor: !anystr
                name: !anystr
                platform: !anystr
                version: !anystr
              release: !anystr
              scan:
                id: !anyint
                time: !anystr
              sysname: !anystr
              version: !anystr
      save:
        json:
          os_version: data.affected_items[0].os.version
          release: data.affected_items[0].release
          sysname: data.affected_items[0].sysname

  - name: Invalid selector
    request:
      verify: False
      <<: *os_request
      params:
        select: wrongParam
    response:
       <<: *error_response_1724

---
test_name: GET /syscollector/{agent_id}/os?{select}

marks:
  - parametrize:
      key: field
      vals:
        - architecture
        - hostname
        - os.codename
        - os.major
        - os.minor
        - os.name
        - os.platform
        - os.version
        - release
        - scan.id
        - scan.time
        - sysname
        - version

stages:

  - name: OS of an agent, select

    request:
      verify: False
      <<: *os_request
      params:
        select: "{field}"

    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"

---
test_name: GET /syscollector/{agent_id}/hardware

stages:

  - name: Hardware of an agent
    request: &hardware_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/hardware"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - cpu:
                cores: !anything
                mhz: !anything
                name: !anything
              ram:
                free: !anything
                total: !anything
                usage: !anything
              scan:
                id: !anything
                time: !anything
      save:
        json:
          cpu_name: data.affected_items[0].cpu.name
          ram_total: data.affected_items[0].ram.total

  - name: Not allowed selector
    request:
      verify: False
      <<: *hardware_request
      params:
        select: wrongParam
    response:
      <<: *error_response_1724

---
test_name: GET /syscollector/{agent_id}/hardware?{select}

marks:
  - parametrize:
      key: field
      vals:
        - cpu.cores
        - cpu.mhz
        - cpu.name
        - ram.free
        - ram.total
        - ram.usage
        - scan.id
        - scan.time

stages:

  - name: Hardware of an agent, select
    request:
      verify: False
      <<: *hardware_request
      params:
        select: "{field}"
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"

---
test_name: GET /syscollector/{agent_id}/netaddr

stages:

  - name: Netaddress of an agent
    request: &netaddr_request
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/netaddr"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response
          total_affected_items: !anyint
      save:
        json:
          expected_broadcast: data.affected_items[0].broadcast
          expected_proto: data.affected_items[0].proto
          expected_address: data.affected_items[0].address
          expected_netmask: data.affected_items[0].netmask
          expected_iface: data.affected_items[0].iface

  - name: Filter by iface
    request:
      verify: False
      <<: *netaddr_request
      params:
        iface: "{expected_iface}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by iface (incorrect)
    request:
      verify: False
      <<: *netaddr_request
      params:
        iface: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by netmask
    request:
      verify: False
      <<: *netaddr_request
      params:
        netmask: "{expected_netmask}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by netmask (incorrect)
    request:
      verify: False
      <<: *netaddr_request
      params:
        netmask: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by address
    request:
      verify: False
      <<: *netaddr_request
      params:
        address: "{expected_address}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by address (incorrect)
    request:
      verify: False
      <<: *netaddr_request
      params:
        address: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by proto
    request:
      verify: False
      <<: *netaddr_request
      params:
        proto: "{expected_proto}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by proto (incorrect)
    request:
      verify: False
      <<: *netaddr_request
      params:
        proto: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by broadcast
    request:
      verify: False
      <<: *netaddr_request
      params:
        broadcast: "{expected_broadcast}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netaddr_response

  - name: Filter by broadcast (incorrect)
    request:
      verify: False
      <<: *netaddr_request
      params:
        broadcast: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Search broadcast
    request:
      verify: False
      <<: *netaddr_request
      params:
        search: "{expected_broadcast}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - broadcast: "{expected_broadcast}"

  - name: Search protocol
    request:
      verify: False
      <<: *netaddr_request
      params:
        search: "{expected_proto}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - proto: "{expected_proto}"

  - name: Search address
    request:
      verify: False
      <<: *netaddr_request
      params:
        search: "{expected_address}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - address: "{expected_address}"

  - name: Search netmask
    request:
      verify: False
      <<: *netaddr_request
      params:
        search: "{expected_netmask}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - netmask: "{expected_netmask}"

  - name: Search iface
    request:
      verify: False
      <<: *netaddr_request
      params:
        search: "{expected_iface}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - iface: "{expected_iface}"

  - name: Select proto, netmask, broadcast
    request:
      verify: False
      <<: *netaddr_request
      params:
        select: proto,broadcast,netmask
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - proto: !anystr
              netmask: !anystr
              broadcast: !anystr

  - name: Not allowed selector
    request:
      verify: False
      <<: *netaddr_request
      params:
        select: wrongSelector
    response:
      <<: *error_response_1724

  - name: Pagination
    request:
      verify: False
      <<: *netaddr_request
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 1

  - name: Wrong limit
    request:
      verify: False
      <<: *netaddr_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Wrong param
    request:
      verify: False
      <<: *netaddr_request
      params:
        wrong: 0
    response:
      status_code: 400

---
test_name: GET /syscollector/{agent_id}/netaddr?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - address
        - broadcast
        - iface
        - netmask
        - proto
        - scan.id

stages:

  - name: Netaddress of an agent, select
    request:
      verify: False
      <<: *netaddr_request
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

---
test_name: GET /syscollector/{agent_id}/netproto

stages:

  - name: Netprotocol of an agent
    request: &netproto_request
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/netproto"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &netproto_response
            - dhcp: !anystr
              gateway: !anystr
              iface: !anystr
              scan:
                id: !anyint
              type: !anystr
          total_affected_items: !anyint
      save:
        json:
          expected_dhcp: data.affected_items[0].dhcp
          expected_gateway: data.affected_items[0].gateway
          expected_type: data.affected_items[0].type
          expected_scan_id: data.affected_items[0].scan.id
          expected_iface: data.affected_items[0].iface

  - name: Filter by iface
    request:
      verify: False
      <<: *netproto_request
      params:
        iface: "{expected_iface}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response

  - name: Filter by iface (incorrect)
    request:
      verify: False
      <<: *netproto_request
      params:
        iface: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by type
    request:
      verify: False
      <<: *netproto_request
      params:
        type: "{expected_type}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response

  - name: Filter by type (incorrect)
    request:
      verify: False
      <<: *netproto_request
      params:
        type: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by gateway
    request:
      verify: False
      <<: *netproto_request
      params:
        gateway: "{expected_gateway}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response

  - name: Filter by gateway (incorrect)
    request:
      verify: False
      <<: *netproto_request
      params:
        gateway: "invalid"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          total_affected_items: 0

  - name: Filter by dhcp
    request:
      verify: False
      <<: *netproto_request
      params:
        dhcp: "{expected_dhcp}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netproto_response

  - name: Filter by dhcp (incorrect)
    request:
      verify: False
      <<: *netproto_request
      params:
        dhcp: "invalid"
    response:
      status_code: 400

  - name: Select proto, netmask, broadcast
    request:
      verify: False
      <<: *netproto_request
      params:
        select: iface,gateway,dhcp
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - iface: !anystr
              gateway: !anystr
              dhcp: !anystr

  - name: Search dhcp
    request:
      verify: False
      <<: *netproto_request
      params:
        search: "{expected_dhcp}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - dhcp: "{expected_dhcp}"

  - name: Search gateway
    request:
      verify: False
      <<: *netproto_request
      params:
        search: "{expected_gateway}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - gateway: "{expected_gateway}"

  - name: Search type
    request:
      verify: False
      <<: *netproto_request
      params:
        search: "{expected_type}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - type: "{expected_type}"

  - name: Search scan.id
    request:
      verify: False
      <<: *netproto_request
      params:
        search: "{expected_scan_id}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - scan:
                id: !int "{expected_scan_id}"

  - name: Search iface
    request:
      verify: False
      <<: *netproto_request
      params:
        search: "{expected_iface}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - iface: "{expected_iface}"

  - name: Filter by query
    request:
      verify: False
      <<: *netproto_request
      params:
        q: gateway={expected_gateway};iface={expected_iface}
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - gateway: "{expected_gateway}"
              iface: "{expected_iface}"

  - name: Not allowed selector
    request:
      verify: False
      <<: *netproto_request
      params:
        select: wrongSelector
    response:
      <<: *error_response_1724

  - name: Wrong limit
    request:
      verify: False
      <<: *netproto_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Wrong param
    request:
      verify: False
      <<: *netproto_request
      params:
        wrong: 0
    response:
      status_code: 400

  - name: Sort
    request:
      verify: False
      <<: *netproto_request
      params:
        sort: +dhcp
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "dhcp"
      status_code: 200

  - name: Invalid sort
    request:
      verify: False
      <<: *netproto_request
      params:
        sort: -wrong
    response:
      <<: *error_response_1403

---
test_name: GET /syscollector/{agent_id}/netproto?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - dhcp
        - gateway
        - iface
        - scan.id
        - type

stages:

  - name: Netprotocol of an agent, select
    request:
      verify: False
      <<: *netproto_request
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

  - name: Netprotocol of an agent, sort
    request:
      verify: False
      <<: *netproto_request
      params:
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

---
test_name: GET /syscollector/{agent_id}/netiface

stages:

  - name: Netiface of an agent
    request: &netiface_request
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/netiface"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &netiface_response
            - mac: !anystr
              mtu: !anyint
              name: !anystr
              rx:
                bytes: !anyint
                dropped: !anyint
                errors: !anyint
                packets: !anyint
              scan:
                id: !anyint
                time: !anystr
              state: !anystr
              tx:
                bytes: !anyint
                dropped: !anyint
                errors: !anyint
                packets: !anyint
              type: !anystr
          total_affected_items: !anyint
      save:
        json:
          expected_mac: data.affected_items[0].mac
          expected_rx_bytes: data.affected_items[0].rx.bytes
          expected_tx_bytes: data.affected_items[0].tx.bytes
          expected_tx_errors: data.affected_items[0].tx.errors
          expected_rx_errors: data.affected_items[0].rx.errors
          expected_rx_packets: data.affected_items[0].rx.packets
          expected_tx_dropped: data.affected_items[0].tx.dropped
          expected_rx_dropped: data.affected_items[0].rx.dropped
          expected_tx_packets: data.affected_items[0].tx.packets
          expected_type: data.affected_items[0].type
          expected_mtu: data.affected_items[0].mtu
          expected_name: data.affected_items[0].name
          expected_state: data.affected_items[0].state

  - name: Select type, rx.bytes, tx.errors, tx.dropped, mac
    request:
      verify: False
      <<: *netiface_request
      params:
        select: type,rx.bytes,tx.errors,tx.dropped,mac
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - type: !anystr
              rx:
                bytes: !anyint
              tx:
                errors: !anyint
                dropped: !anyint
              mac: !anystr

  - name: Search mac
    request:
      verify: False
      <<: *netiface_request
      params:
        search: "{expected_mac}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - mac: "{expected_mac}"

  - name: Search rx.bytes
    request:
      verify: False
      <<: *netiface_request
      params:
        search: "{expected_rx_bytes}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - rx:
                bytes: !int "{expected_rx_bytes}"

  - name: Search tx.errors
    request:
      verify: False
      <<: *netiface_request
      params:
        search: !int "{expected_tx_errors}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - tx:
                errors: !int "{expected_tx_errors}"

  - name: Search tx.dropped
    request:
      verify: False
      <<: *netiface_request
      params:
        search: "{expected_tx_dropped}"
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - tx:
                dropped: !int "{expected_tx_dropped}"

  - name: Not allowed selector
    request:
      verify: False
      <<: *netiface_request
      params:
        select: wrongSelector
    response:
      <<: *error_response_1724

  - name: Wrong limit
    request:
      verify: False
      <<: *netiface_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Wrong param
    request:
      verify: False
      <<: *netiface_request
      params:
        wrong: 0
    response:
      status_code: 400

  - name: Sort
    request:
      verify: False
      <<: *netiface_request
      params:
        sort: -type
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "type"
            reverse: true
      status_code: 200

  - name: Invalid sort
    request:
      verify: False
      <<: *netiface_request
      params:
        sort: -wrong
    response:
      <<: *error_response_1403

  - name: Offset and limit
    request:
      verify: False
      <<: *netiface_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Offset = 1 and limit = 1
    request:
      verify: False
      <<: *netiface_request
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: !anyint

  - name: Filter mtu
    request:
      verify: False
      <<: *netiface_request
      params:
        mtu: "{expected_mtu}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

#  - name: Filter adapter
#    skip: True # This test cannot be performed as usual because the column adapter exists in the database but the field is always empty
#    request:
#      verify: False
#      <<: *netiface_request
#      params:
#        adapter: "{expected_adapter}"
#      response:
#        status_code: 200

  - name: Filter name
    request:
      verify: False
      <<: *netiface_request
      params:
        name: "{expected_name}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter type
    request:
      verify: False
      <<: *netiface_request
      params:
        type: "{expected_type}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter state
    request:
      verify: False
      <<: *netiface_request
      params:
        state: "{expected_state}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter tx.packets
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.packets: "{expected_tx_packets}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter rx_packets
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.packets: "{expected_rx_packets}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter tx.dropped
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.dropped: "{expected_tx_dropped}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter rx.dropped
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.dropped: "{expected_rx_dropped}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter tx.bytes
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.bytes: "{expected_tx_bytes}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter rx.bytes
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.bytes: "{expected_rx_bytes}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter tx.errors
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.errors: "{expected_tx_errors}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter rx.errors
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.errors: "{expected_rx_errors}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *netiface_response
          total_affected_items: !anyint

  - name: Filter invalid tx.packets
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.packets: -1
    response:
      status_code: 400

  - name: Filter invalid tx.packets
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.packets: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.packets
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.packets: -1
    response:
      status_code: 400

  - name: Filter invalid rx.packets
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.packets: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid tx.dropped
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.dropped: -1
    response:
      status_code: 400

  - name: Filter invalid tx.dropped
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.dropped: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.dropped
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.dropped: -1
    response:
      status_code: 400

  - name: Filter invalid rx.dropped
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.dropped: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid tx.bytes
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.bytes: -1
    response:
      status_code: 400

  - name: Filter invalid tx.bytes
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.bytes: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.bytes
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.bytes: -1
    response:
      status_code: 400

  - name: Filter invalid rx.bytes
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.bytes: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid tx.errors
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.errors: -1
    response:
      status_code: 400

  - name: Filter invalid tx.errors
    request:
      verify: False
      <<: *netiface_request
      params:
        tx.errors: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Filter invalid rx.errors
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.errors: -1
    response:
      status_code: 400

  - name: Filter invalid rx.errors
    request:
      verify: False
      <<: *netiface_request
      params:
        rx.errors: 999999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

---
test_name: GET /syscollector/{agent_id}/netiface?{sort,select}

marks:
  - parametrize:
      key: field
      vals:
        - mac
        - mtu
        - name
        - rx.bytes
        - rx.dropped
        - rx.errors
        - rx.packets
        - scan.id
        - scan.time
        - state
        - tx.bytes
        - tx.dropped
        - tx.errors
        - tx.packets
        - type

stages:

  - name: Netiface of an agent, select
    request:
      verify: False
      <<: *netiface_request
      params:
        select: "{field}"
        limit: 1
    response:
      status_code: 200
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: "{field:s}"
      json:
        error: !anyint
        data:
          total_affected_items: !anyint

  - name: Netiface of an agent, sort
    request:
      verify: False
      <<: *netiface_request
      params:
        sort: "{field}"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "{field}"
      status_code: 200

---
test_name: GET /syscollector/{agent_id}/hotfixes

stages:

  - name: Get all hotfixes from agent
    request: &hotfix_request_001
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/hotfixes"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_validate_syscollector_hotfix
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter hotfix with non-existent query
    request:
      verify: False
      <<: *hotfix_request_001
      params:
        q: 'hotfix=0'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Get distinct hotfixes info
    request:
      verify: False
      <<: *hotfix_request_001
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key
