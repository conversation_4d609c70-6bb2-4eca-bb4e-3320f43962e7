---
test_name: GET DECODERS RBAC

stages:

  - name: Try to show the decoders of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 5
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - &full_item_decoders
              details: !anything
              filename: 0005-wazuh_decoders.xml
              name: !anystr
              relative_dirname: !anystr
              position: !anyint
              status: !anystr
            - <<: *full_item_decoders
            - <<: *full_item_decoders
            - <<: *full_item_decoders
            - <<: *full_item_decoders
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the decoders of the system (try q parameter to bypass a denied resource)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        q: 'filename=0006-json_decoders.xml'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the decoders of the system (list)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        decoder_names: wazuh,agent-buffer,netscaler,netinfo,agent-upgrade
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - <<: *full_item_decoders
            - <<: *full_item_decoders
            - <<: *full_item_decoders
            - <<: *full_item_decoders
            - <<: *full_item_decoders
            - <<: *full_item_decoders
            - <<: *full_item_decoders
              filename: 0160-netscaler_decoders.xml
          failed_items:
            - error:
                code: 1504
              id:
                - netinfo
          total_affected_items: !anyint
          total_failed_items: 1
        
---
test_name: GET DECODERS FILES RBAC

stages:

  - name: Try to show the decoders files of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - filename: 0005-wazuh_decoders.xml
              relative_dirname: ruleset/decoders
              status: enabled
            - filename: 0160-netscaler_decoders.xml
              relative_dirname: ruleset/decoders
              status: enabled
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the decoders files of the system (list)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        filename: 0005-wazuh_decoders.xml,0160-netscaler_decoders.xml
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - filename: 0005-wazuh_decoders.xml
              relative_dirname: ruleset/decoders
              status: enabled
            - filename: 0160-netscaler_decoders.xml
              relative_dirname: ruleset/decoders
              status: enabled
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the decoders files of the system (no permissions)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        filename: 0064-cisco-asa_decoders.xml,0100-fortigate_decoders.xml
    response: &permission_denied
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

---
test_name: GET DECODERS FILES RBAC (CONTENT)

stages:

  - name: Get one decoder file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/0005-wazuh_decoders.xml"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200

  - name: Try to get one decoder file (no permissions)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/0064-cisco-asa_decoders.xml"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: PUT DECODERS FILES RBAC

stages:

  - name: Update one decoder file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/new_decoder.xml"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      data: "{new_decoder:s}"
    response:
      status_code: 200


  - name: Try to update the same file without delete permissions (overwrite)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/new_decoder.xml"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        overwrite: True
      data: "{new_decoder:s}"
    response:
      json:
        data:
          affected_items: []
          total_affected_items: 0
          failed_items:
            - error:
                code: 4000
          total_failed_items: 1
        error: 1

---
test_name: DELETE DECODERS FILES RBAC

stages:

  - name: Delete one decoder file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/local_decoder.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

  - name: Try to delete one decoder file that does not exist
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/files/not_exist.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET DECODERS PARENTS RBAC

stages:

  - name: Try to show the groups of rules in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/decoders/parents"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
