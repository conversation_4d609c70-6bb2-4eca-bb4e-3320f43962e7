---
test_name: PUT /logtest

stages:

  - name: Run logtest without token (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/logtest"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        event: "Jun 24 11:54:19 Master systemd[2099]: Started VTE child process 20118 launched by terminator process 17756."
        log_format: "syslog"
        location: "master->/var/log/syslog"
    response:
      status_code: 200

---
test_name: DELETE /logtest/sessions/{token}

stages:

  - name: Run logtest without token in order to start session (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/logtest"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/json
      json:
        event: "Jun 24 11:54:19 Master systemd[2099]: Started VTE child process 20118 launched by terminator process 17756."
        log_format: "syslog"
        location: "master->/var/log/syslog"
    response:
      status_code: 200
      json:
        data:
          token: !anystr
      save:
        json:
          returned_token: data.token

  - name: End logtest session (allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/logtest/sessions/{returned_token:s}"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        data:
          codemsg: 0
