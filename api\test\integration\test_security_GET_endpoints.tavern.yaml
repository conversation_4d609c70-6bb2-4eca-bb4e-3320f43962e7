---
test_name: GET /security/roles

stages:

  # GET /security/roles
  - name: Try to show the roles of the system
    request: &all_roles_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: &roles
            - id: !anyint
              name: !anystr
              policies: !anything
              rules: !anything
              users: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  # GET /security/roles
  - name: Try to show the roles of the system, offset = 0
    request:
      <<: *all_roles_request
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *roles
            - <<: *roles
          total_affected_items: !anyint
      save:
        json:
          second_role_id: data.affected_items[1].id
          second_role_name: data.affected_items[1].name

  # GET /security/roles
  - name: Try to show the roles of the system, offset = 1
    request:
      <<: *all_roles_request
      params:
        limit: 2
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
          - id: !int "{second_role_id}"
            name: "{second_role_name}"
          - <<: *roles
          total_affected_items: !anyint

  # GET /security/roles
  - name: Try to show the roles of the system, without limit
    request:
      <<: *all_roles_request
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
          - <<: *roles

  # GET /security/roles
  - name: Try to get all roles with limit = 0
    request:
      <<: *all_roles_request
      params:
        limit: 0
    response:
      status_code: 400

  # GET /security/roles
  - name: Sort the roles
    request:
      <<: *all_roles_request
      params:
        sort: name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"
      status_code: 200

  # GET /security/roles
  - name: Sort without limit (reverse)
    request:
      <<: *all_roles_request
      params:
        sort: -name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"
            reverse: true
      status_code: 200

  # GET /security/roles
  - name: Select role name
    request:
      <<: *all_roles_request
      params:
        select: name
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'name' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/roles
  - name: Select role id
    request:
      <<: *all_roles_request
      params:
        select: id
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'id' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/roles
  - name: Select role rules
    request:
      <<: *all_roles_request
      params:
        select: rules
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'rules' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/roles
  - name: Select role policies
    request:
      <<: *all_roles_request
      params:
        select: policies
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'policies' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/roles
  - name: Select role users
    request:
      <<: *all_roles_request
      params:
        select: users
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'users' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/roles
  - name: Search in roles with select
    request:
      <<: *all_roles_request
      params:
        select: name
        search: readonly
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'name' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 2
              name: readonly
            - id: 4
              name: agents_readonly
            - id: 6
              name: cluster_readonly
          total_affected_items: 3

  # GET /security/roles
  - name: Search without limit
    request:
      <<: *all_roles_request
      params:
        search: wazuh
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *roles
            - <<: *roles
          total_affected_items: !anyint

  # GET /security/roles
  - name: Sort without limit, non existent sort parameter
    request:
      <<: *all_roles_request
      params:
        sort: +noexist
    response:
      status_code: 400

  # GET /security/roles
  - name: Search without limit, non existent role
    request:
      <<: *all_roles_request
      params:
        search: noexist
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: 0

  # GET /security/roles
  - name: Invalid parameter
    request:
      <<: *all_roles_request
      params:
        noexist: nothing
    response:
      status_code: 400

  # GET /security/roles
  - name: Invalid parameters - Extra fields
    request:
      <<: *all_roles_request
      params:
        search: wazuh
        noexist: True
    response:
      status_code: 400

  # GET /security/roles
  - name: Try to show the roles of the system with a non-existent search
    request:
      <<: *all_roles_request
      params:
        search: notexist
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: 0

  # GET /security/roles/{role_id}
  - name: Try to show a specified role in the system
    request:
      <<: *all_roles_request
      params:
        role_ids: 2,3,4
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *roles
            - <<: *roles
            - <<: *roles
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  # GET /security/roles/{non-existent role_id}
  - name: Try to show a non-existent role in the system
    request:
      <<: *all_roles_request
      params:
        role_ids: 999
    response:
      status_code: 200
      json: &response_error
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: !anyint
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1

  - name: Get security roles using query parameter
    request:
      verify: False
      <<: *all_roles_request
      params:
        q: id=2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 2
              name: readonly
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    
  - name: Try to get security roles using an invalid query
    request:
      verify: False
      <<: *all_roles_request
      params:
        q: id%=invalid
    response:
      status_code: 400
      json:
        error: 1407

  - name: Get distinct security roles
    request:
      verify: False
      <<: *all_roles_request
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /security/policies

stages:

  # GET /security/policies
  - name: Try to show the policies of the system
    request: &all_policies_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: &policies
            - id: !anyint
              name: !anystr
              policy: !anything
          total_affected_items: !anyint

  # GET /security/policies
  - name: Try to show the policies of the system, offset = 0
    request:
      <<: *all_policies_request
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *policies
            - <<: *policies
          total_affected_items: !anyint
      save:
        json:
          second_policy_id: data.affected_items[1].id
          second_policy_name: data.affected_items[1].name

  # GET /security/policies
  - name: Try to show the policies of the system, offset = 1
    request:
      <<: *all_policies_request
      params:
        limit: 2
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
          - id: !int "{second_policy_id}"
            name: "{second_policy_name}"
          - <<: *policies
          total_affected_items: !anyint

  # GET /security/policies
  - name: Try to show the policies of the system, without limit
    request:
      <<: *all_policies_request
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  # GET /security/policies
  - name: Try to get all policies with limit = 0
    request:
      <<: *all_policies_request
      params:
        limit: 0
    response:
      status_code: 400

  # GET /security/policies
  - name: Sort the policies
    request:
      <<: *all_policies_request
      params:
        sort: name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"

  # GET /security/policies
  - name: Sort the policies (reverse)
    request:
      <<: *all_policies_request
      params:
        sort: -name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"
            reverse: true

  # GET /security/policies
  - name: Select policy id
    request:
      <<: *all_policies_request
      params:
        select: id
        limit: 1
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'id' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/policies
  - name: Select policy name
    request:
      <<: *all_policies_request
      params:
        select: name
        limit: 1
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'name' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/policies
  - name: Select policy info
    request:
      <<: *all_policies_request
      params:
        select: policy
        limit: 1
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'policy' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/policies
  - name: Search in policies with select
    request:
      <<: *all_policies_request
      params:
        select: name
        search: wazuh
        limit: 2
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'name' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - name: wazuhPolicy
              id: 100
            - name: wazuh-wuiPolicy
              id: 101
          total_affected_items: 2
      save:
        json:
          policy_id: data.affected_items[0].id

  # GET /security/policies
  - name: Search without limit
    request:
      <<: *all_policies_request
      params:
        search: wazuh
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: 2

  # GET /security/policies
  - name: Search without limit, non existent policy
    request:
      <<: *all_policies_request
      params:
        search: noexist
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: 0

  # GET /security/policies
  - name: Invalid parameter
    request:
      <<: *all_policies_request
      params:
        noexist: nothing
    response:
      status_code: 400

  # GET /security/policies
  - name: Invalid parameters - Extra fields
    request:
      <<: *all_policies_request
      params:
        search: wazuh
        noexist: True
    response:
      status_code: 400

  # GET /security/policies
  - name: Try to show the policies of the system with a non-existent search
    request:
      <<: *all_policies_request
      params:
        search: notexist
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: 0

  # GET /security/policies/{policy_id}
  - name: Try to show a specified policy in the system
    request:
      <<: *all_policies_request
      params:
        policy_ids: 2,3,4
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *policies
            - <<: *policies
            - <<: *policies
          failed_items: [ ]
          total_affected_items: !anyint
          total_failed_items: 0

  # GET /security/policies/{non-existent policy_id}
  - name: Try to show a non-existent policy in the system
    request:
      <<: *all_policies_request
      params:
        policy_ids: 999
    response:
      status_code: 200
      json:
        <<: *response_error
    
  - name: Get security policies using query parameter
    request:
      verify: False
      <<: *all_policies_request
      params:
        q: id=103
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 103
              name: administratorPolicy
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    
  - name: Try to get security policies using an invalid query
    request:
      verify: False
      <<: *all_policies_request
      params:
        q: id%=invalid
    response:
      status_code: 400
      json:
        error: 1407

  - name: Get distinct security policies
    request:
      verify: False
      <<: *all_policies_request
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /security/rules

stages:

  # GET /security/rules
  - name: Try to show the rules of the system
    request: &all_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: &rules
            - id: !anyint
              name: !anystr
              rule: !anything
              roles: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  # GET /security/rules
  - name: Try to show the rules of the system, offset = 0
    request:
      <<: *all_rules_request
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules
            - <<: *rules
          total_affected_items: !anyint
      save:
        json:
          second_rule_id: data.affected_items[1].id
          second_rule_name: data.affected_items[1].name

  # GET /security/rules
  - name: Try to show the rules of the system, offset = 1
    request:
      <<: *all_rules_request
      params:
        limit: 2
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
          - id: !int "{second_rule_id}"
            name: "{second_rule_name}"
          - <<: *rules
          total_affected_items: !anyint

  # GET /security/rules
  - name: Try to show the rules of the system, without limit
    request:
      <<: *all_rules_request
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
          - <<: *rules

  # GET /security/rules
  - name: Try to get all rules with limit = 0
    request:
      <<: *all_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  # GET /security/rules
  - name: Sort the rules
    request:
      <<: *all_rules_request
      params:
        sort: name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"
      status_code: 200

  # GET /security/rules
  - name: Sort the rules (reverse)
    request:
      <<: *all_rules_request
      params:
        sort: -name
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "name"
            reverse: true
      status_code: 200

  # GET /security/rules
  - name: Search in rules
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
        search: administrator
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules
          total_affected_items: !anyint

  # GET /security/rules
  - name: Select rule id
    request:
      <<: *all_rules_request
      params:
        select: id
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'id' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/rules
  - name: Select rule name
    request:
      <<: *all_rules_request
      params:
        select: name
        limit: 1
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'name' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/rules
  - name: Select rule info
    request:
      <<: *all_rules_request
      params:
        select: rule
        limit: 1
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'rule' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/rules
  - name: Select rule roles
    request:
      <<: *all_rules_request
      params:
        select: roles
        limit: 1
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'roles' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: [ ]
          total_failed_items: 0

  # GET /security/rules
  - name: Search without limit
    request:
      <<: *all_rules_request
      params:
        search: administrator
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules
            - <<: *rules
            - <<: *rules
          total_affected_items: !anyint

  # GET /security/rules
  - name: Sort without limit, non existent sort parameter
    request:
      <<: *all_rules_request
      params:
        sort: +noexist
    response:
      status_code: 400

  # GET /security/rules
  - name: Search without limit, non existent rules
    request:
      <<: *all_rules_request
      params:
        search: noexist
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: 0

  # GET /security/rules
  - name: Invalid parameter
    request:
      <<: *all_rules_request
      params:
        noexist: nothing
    response:
      status_code: 400

  # GET /security/rules
  - name: Invalid parameters - Extra fields
    request:
      <<: *all_rules_request
      params:
        search: wazuh
        noexist: True
    response:
      status_code: 400

  # GET /security/rules
  - name: Try to show the rules of the system with a non-existent search
    request:
      <<: *all_rules_request
      params:
        search: notexist
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: 0

  # GET /security/rules
  - name: Try to show a list of rules
    request:
      <<: *all_rules_request
      params:
        rule_ids: 1,2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules
          total_affected_items: 2

  # GET /security/rules
  - name: Try to show a list of unexistent rules
    request:
      <<: *all_rules_request
      params:
        rule_ids: 1,999
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - <<: *rules
          failed_items:
            - error:
                code:
                  4022
              id:
                - '999'
          total_affected_items: 1
          total_failed_items: 1

  - name: Get security rules using query parameter
    request:
      verify: False
      <<: *all_rules_request
      params:
        q: id=101
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 101
              name: rule2
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    
  - name: Try to get security rules using an invalid query
    request:
      verify: False
      <<: *all_rules_request
      params:
        q: id%=invalid
    response:
      status_code: 400
      json:
        error: 1407

  - name: Get distinct security rules
    request:
      verify: False
      <<: *all_rules_request
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /security/user/authenticate

stages:

  - name: Get an API token in raw format
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/user/authenticate"
      headers:
        Authorization: "Basic {basic_auth:s}"
      params:
        raw: True
      method: GET
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_token_raw_format
      save:
        $ext:
          function: tavern_utils:test_save_token_raw_format

  - name: Test generated token
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/"
      headers:
        Authorization: "Bearer {login_token}"
      method: GET
    response:
      status_code: 200

---
test_name: GET /security/users

marks:
  - parametrize:
      key: field
      vals:
        - wazuh-wui
        - normal

stages:

  - name: Get all users in the system
    request: &all_users_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 1
              username: wazuh
              allow_run_as: true
              roles:
                - 1
            - id: 2
              username: wazuh-wui
              allow_run_as: true
              roles:
                - 1
            - id: 99
              username: testing
              allow_run_as: false
              roles:
                - 1
            - id: 100
              username: administrator
              allow_run_as: false
              roles:
                - 100
                - 101
            - id: 101
              username: normal
              allow_run_as: false
              roles:
                - 104
                - 105
                - 103
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
            - id: 103
              username: python
              allow_run_as: false
              roles:
                - 101
            - id: 104
              username: rbac
              allow_run_as: false
              roles:
                - 104
                - 102
                - 103
            - id: 105
              username: guest
              allow_run_as: false
              roles: []
          total_affected_items: !anyint
          total_failed_items: 0
          failed_items: []

  - name: Get users in the system using filters
    request:
      <<: *all_users_request
      params:
        select: id
        limit: 2
        offset: 0
        sort: -id
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        - function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: 'id' # required_fields={'id'}
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "id"
            reverse: true
      status_code: 200

  - name: Get users in the system using search and select filters
    request:
      <<: *all_users_request
      params:
        limit: 1
        select: username
        search: wazuh-wui
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'username' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 2
              username: wazuh-wui
          total_affected_items: !anyint
          total_failed_items: 0
          failed_items: [ ]

  - name: Get all users in the system using select filter
    request:
      <<: *all_users_request
      params:
        select: allow_run_as,roles
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'allow_run_as,roles' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: !anyint
              allow_run_as: !anybool
              roles: !anylist
          total_affected_items: !anyint
          total_failed_items: 0
          failed_items: [ ]

  - name: Get a specified user by its id
    request:
      <<: *all_users_request
      params:
        user_ids: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: &user
            - id: !anyint
              username: !anystr
          total_affected_items: 1

  - name: Get a specified user by its id
    request:
      <<: *all_users_request
      params:
        user_ids: 1,2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *user
            - <<: *user
          total_affected_items: 2

  - name: Get a user by its id (invalid id)
    request:
      <<: *all_users_request
      params:
        user_ids: "!!!"
    response:
      status_code: 400

  - name: Get a user by its id (invalid id)
    request:
      <<: *all_users_request
      params:
        user_ids: "####@@@|#|"
    response:
      status_code: 400

  - name: Get an non-existent user by its id
    request:
      <<: *all_users_request
      params:
        user_ids: 10000
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: [ ]
          failed_items:
            - error:
                code: 5001
              id:
                - 10000
          total_affected_items: 0
          total_failed_items: 1

  - name: Get two non-existent users by their ids
    request:
      <<: *all_users_request
      params:
        user_ids: 10000,10001
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: [ ]
          failed_items:
            - error:
                code: 5001
              id:
                - 10000
                - 10001
          total_affected_items: 0
          total_failed_items: 2

  - name: Get two non-existent users and an existing one by their ids
    request:
      <<: *all_users_request
      params:
        user_ids: 10000,10001,1
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - id: 1
              username: wazuh
              allow_run_as: true
          failed_items:
            - error:
                code: 5001
              id:
                - 10000
                - 10001
          total_affected_items: 1
          total_failed_items: 2

  - name: Get two non-existent users and two existent by their ids
    request:
      <<: *all_users_request
      params:
        user_ids: 10000,10001,2,1
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - id: 1
              username: wazuh
            - id: 2
              username: wazuh-wui
          failed_items:
            - error:
                code: 5001
              id:
                - 10000
                - 10001
          total_affected_items: 2
          total_failed_items: 2

  - name: Testing search
    request:
      <<: *all_users_request
      params:
        search: "{field}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - username: "{field}"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Testing complementary search
    request:
      <<: *all_users_request
      params:
        search: "-wazuh"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 99
              username: testing
              allow_run_as: false
              roles:
                - 1
            - id: 100
              username: administrator
              allow_run_as: false
              roles:
                - 100
                - 101
            - id: 101
              username: normal
              allow_run_as: false
              roles:
                - 104
                - 105
                - 103
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
            - id: 103
              username: python
              allow_run_as: false
              roles:
                - 101
            - id: 104
              username: rbac
              allow_run_as: false
              roles:
                - 104
                - 102
                - 103
            - id: 105
              username: guest
              roles: []
          total_affected_items: 7
          total_failed_items: 0
          failed_items: []

  - name: Testing inverse sort
    request:
      <<: *all_users_request
      params:
        sort: "-username"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "username"
            reverse: true
      status_code: 200

  - name: Testing offset
    request:
      <<: *all_users_request
      params:
        offset: 3
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 100
              username: administrator
              allow_run_as: false
              roles:
                - 100
                - 101
            - id: 101
              username: normal
              allow_run_as: false
              roles:
                - 104
                - 105
                - 103
            - id: 102
              username: ossec
              allow_run_as: false
              roles:
                - 101
                - 104
            - id: 103
              username: python
              allow_run_as: false
              roles:
                - 101
            - id: 104
              username: rbac
              allow_run_as: false
              roles:
                - 104
                - 102
                - 103
            - id: 105
              username: guest
              allow_run_as: false
              roles: []
          total_affected_items: 9
          total_failed_items: 0

  - name: Testing limit
    request:
      <<: *all_users_request
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 1
              username: wazuh
              allow_run_as: true
              roles:
                - 1
          total_affected_items: 9
          total_failed_items: 0
          failed_items: []

  - name: Get security users using query parameter
    request:
      verify: False
      <<: *all_users_request
      params:
        q: id=1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 1
              username: wazuh
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    
  - name: Try to get security users using an invalid query
    request:
      verify: False
      <<: *all_users_request
      params:
        q: id%=invalid
    response:
      status_code: 400
      json:
        error: 1407

  - name: Get distinct security users
    request:
      verify: False
      <<: *all_users_request
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /security/users/me

stages:

  - name: Get information about the current user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/me"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: 99
              username: testing
              allow_run_as: false
              roles:
                - id: 1
                  name: "administrator"
                  rules: !anything
                  policies: !anything
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []

---
test_name: GET /security/users/me/policies

stages:

  - name: Get information about the current user processed policies
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/me/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data: !anything
        message: !anystr

---
test_name: GET /security/actions

stages:
  - name: Testing RBAC's actions catalog
    request: &rbac_actions_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/actions"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        endpoint: "PUT /active-response"
    response:
      status_code: 200
      json:
        error: 0
        data:
          active-response:command:
            description: !anystr
            resources:
              - agent:id
            example:
              actions:
                - active-response:command
              resources:
                - agent:id:001
              effect: allow
            related_endpoints:
              - PUT /active-response

  - name: Testing RBAC's actions catalog
    request:
      <<: *rbac_actions_request
      params:
        endpoint: "GET /rules/files"
    response:
      status_code: 200
      json:
        error: 0
        data:
          rules:read:
            description: !anystr
            resources:
              - rule:file
            example:
              actions:
                - rules:read
              resources:
                - rule:file:0610-win-ms_logs_rules.xml
              effect: allow
            related_endpoints:
              - GET /rules/files

---
test_name: GET /security/resources

stages:
  - name: Testing RBAC's actions catalog
    request: &rbac_resources_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/resources"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          "*:*":
            description: 'Resource applied in functions acting on resources that do not yet exist in the system.
            We call these functions, resourceless functions'
          agent:group:
            description: Reference agents via group name (i.e. agent:group:web)
          agent:id:
            description: Reference agents via agent ID (i.e. agent:id:001)
          group:id:
            description: Reference agent groups via group ID (i.e. group:id:default)
          node:id:
            description: Reference cluster nodes via node ID (i.e. node:id:worker1)
          decoder:file:
            description: Reference decoder files via its path (i.e. decoder:file:0005-wazuh_decoders.xml)
          list:file:
            description: Reference list files via its filename (i.e. list:file:audit-keys)
          rule:file:
            description: Reference rule files via its path (i.e. rule:file:0610-win-ms_logs_rules.xml)
          policy:id:
            description: Reference security policies via its id (i.e. policy:id:1)
          role:id:
            description: Reference security roles via its id (i.e. role:id:1)
          user:id:
            description: Reference security users via its id (i.e. user:id:1)

  - name: Testing RBAC's actions catalog
    request:
      <<: *rbac_resources_request
      params:
        resource: "agent:id"
    response:
      status_code: 200
      json:
        error: 0
        data:
          agent:id:
            description: Reference agents via agent ID (i.e. agent:id:001)

---
test_name: GET /security/config

stages:

  - name: Get current security configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: 0
        data:
          auth_token_exp_timeout: !anyint
          rbac_mode: !anystr
