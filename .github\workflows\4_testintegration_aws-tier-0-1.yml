name: Integration tests for AWS - Tier 0 and 1

on:
  workflow_dispatch:
    inputs:
      base_branch:
        description: 'Base branch'
        required: true
        default: 'main'
      base_qa_it_fw_branch:
        description: 'Base qa-integration-framework branch'
        required: true
        default: 'main'

jobs:
  build:
    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
      BRANCH_BASE: ${{ github.base_ref || inputs.base_branch }}
      QA_IT_FW_BRANCH: ${{ github.base_ref || inputs.base_qa_it_fw_branch }}
      AWS_ACCESS_KEY_ID: ${{ secrets.IT_AWS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.IT_AWS_SECRET_ACCESS_KEY }}
      AWS_DEFAULT_REGION: 'us-east-1'
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version-file: ".github/workflows/.python-version-it"
          architecture: x64
      # Download and install integration tests framework.
      - name: Download and install integration tests framework
        run: |
          if [ "X`git ls-remote https://github.com/wazuh/qa-integration-framework.git ${BRANCH_BASE}`" != "X" ]; then
              QA_BRANCH=${BRANCH_BASE}
          elif [ "X`git ls-remote https://github.com/wazuh/qa-integration-framework.git ${QA_IT_FW_BRANCH}`" != "X" ]; then
              QA_BRANCH=${QA_IT_FW_BRANCH}
          else
              QA_BRANCH="main"
          fi
          git clone -b ${QA_BRANCH} --single-branch https://github.com/wazuh/qa-integration-framework.git
          sudo pip install qa-integration-framework/
          sudo rm -rf qa-integration-framework/
      - name: Set AWS credentials file
        run: |
          sudo aws configure set aws_access_key_id ${{ secrets.IT_AWS_KEY_ID }} --profile default
          sudo aws configure set aws_secret_access_key ${{ secrets.IT_AWS_SECRET_ACCESS_KEY }} --profile default
          sudo aws configure set default.region ${AWS_DEFAULT_REGION} --profile default
      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake
      # Build wazuh server for linux.
      - name: Build wazuh server for linux
        run: |
          make deps -C src TARGET=server -j2
          make -C src TARGET=server -j2
      # Install wazuh server for linux.
      - name: Install wazuh server for linux
        run: |
          echo 'USER_LANGUAGE="en"' > ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_NO_STOP="y"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_INSTALL_TYPE="server"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo "USER_DIR=/var/ossec" >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_EMAIL="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SYSCHECK="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_ROOTCHECK="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SYSCOLLECTOR="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SCA="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_WHITE_LIST="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_SYSLOG="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_AUTHD="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_ENABLE_UPDATE_CHECK="n"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          echo 'USER_AUTO_START="y"' >> ./etc/preloaded-vars.conf
          echo "" >> ./etc/preloaded-vars.conf
          sudo sh install.sh
          rm ./etc/preloaded-vars.conf
      # Run AWS integration tests.
      - name: Run Parser related tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/aws_s3.py') ||
            contains(steps.get_modified_files.outputs.files, 'wodles/aws/aws_tools.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 test_aws/test_parser.py
      - name: Run every test due to base WazuhIntegration class change or manual dispatch
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/wazuh_integration.py') ||
            ${{ github.event_name == 'workflow_dispatch' }}
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 test_aws/
      # Bucket tests
      - name: Run Custom Buckets tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k kms test_aws/
          sudo python3 -m pytest --tier 0 --tier 1 -k macie test_aws/
          sudo python3 -m pytest --tier 0 --tier 1 -k trusted_advisor test_aws/
      - name: Run Config tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/config.py') ||
            contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k config test_aws/
      - name: Run GuardDuty tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/guardduty.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k guardduty test_aws/
      - name: Run CloudTrail tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/cloudtrail.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k cloudtrail test_aws/
      - name: Run Load Balancers tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/load_balancers.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k alb test_aws/
          sudo python3 -m pytest --tier 0 --tier 1 -k clb test_aws/
          sudo python3 -m pytest --tier 0 --tier 1 -k nlb test_aws/
      - name: Run Server Access tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/server_access.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k server_access test_aws/
      - name: Run Umbrella tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/umbrella.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k cisco test_aws/
      - name: Run VPC Flow tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/vpcflow.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k vpc test_aws/
      - name: Run WAF tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/waf.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/buckets_s3/aws_bucket.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k waf test_aws/
      # Services tests
      - name: Run CloudWatch tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/services/cloudwatchlogs.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/services/aws_service.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k cloudwatch test_aws/
      - name: Run Inspector tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/services/inspector.py') ||
          contains(steps.get_modified_files.outputs.files, 'wodles/aws/services/aws_service.py')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 -k inspector test_aws/
      # Custom Logs Buckets tests
      - name: Run Inspector tests
        if: contains(steps.get_modified_files.outputs.files, 'wodles/aws/subscribers/**')
        run: |
          cd tests/integration
          sudo python3 -m pytest --tier 0 --tier 1 test_aws/test_custom_bucket.py
