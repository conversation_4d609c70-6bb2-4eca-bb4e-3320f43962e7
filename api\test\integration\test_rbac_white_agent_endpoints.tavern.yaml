---
test_name: GET /agents

stages:

  - name: Get all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: '000'
            - id: '002'
            - id: '004'
            - id: '006'
            - id: '008'
            - id: '010'
            - id: '012'
          failed_items: []
          total_affected_items: 7
          total_failed_items: 0

  - name: Get a list of agents (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,003'
    response: &permission_denied
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

  - name: Get a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,003,004,005,006'
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - id: '004'
            - id: '006'
          failed_items:
            - error:
                code: 4000
              id:
                - '001'
                - '003'
                - '005'
          total_affected_items: 2
          total_failed_items: 3

  - name: Get a list of agents (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '002,006,010,012'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: '002'
            - id: '006'
            - id: '010'
            - id: '012'
          total_affected_items: 4
          total_failed_items: 0

---
test_name: GET /agents?agents_list=agent_id

stages:

  - name: Try to get agent 009 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '009'
    response:
      <<: *permission_denied

  - name: Get agent 010 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '010'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: '010'
          total_affected_items: 1
          total_failed_items: 0

---
test_name: GET /agents/{agent_id}/config/{component}/{configuration}

stages:

  - name: Try to get client configuration from agent component for agent 003 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/003/config/agent/client"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Get client configuration from agent component for agent 002 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/002/config/agent/client"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

---
test_name: GET /agents/{agent_id}/group/is_sync

stages:

  - name: Try to get sync of agent 011 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/011/group/is_sync"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Get agent sync (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/008/group/is_sync"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: '008'
              synced: !anybool
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: GET /agents/{agent_id}/key

stages:

 - name: Try get key agent 005 (Denied)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/005/key"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
      <<: *permission_denied

 - name: Get agent key for 006 (Allowed)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/006/key"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - id: '006'
         failed_items: []
         total_affected_items: 1
         total_failed_items: 0

---
test_name: GET /agents/{agent_id}/daemons/stats

stages:

  - name: Try to get daemon stats from agent 001 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Get all daemons' stats from agent 002 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/002/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - timestamp: !anystr
              name: wazuh-remoted
              agents:
                - id: 2
                  uptime: !anystr
                  metrics: !anything
            - timestamp: !anystr
              name: wazuh-analysisd
              agents:
                - id: 2
                  uptime: !anystr
                  metrics: !anything
          failed_items: [ ]
          total_affected_items: 2
          total_failed_items: 0

---
test_name: GET /agents/{agent_id}/stats/{component}

stages:

 - name: Try to get logcollector stats from agent 002 (Allowed)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/002/stats/logcollector"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200

 - name: Try to get logcollector stats from agent 003 (Denied)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/003/stats/logcollector"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
      <<: *permission_denied

---
test_name: GET /groups

stages:

 - name: Get all groups (Partially allowed, user agnostic)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - name: 'default'
           - name: 'group1'
           - name: 'group2'
         failed_items: []
         total_affected_items: 3
         total_failed_items: 0

 - name: Try to read group3 (Denied)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
     params:
       groups_list: 'group3'
   response:
      <<: *permission_denied

 - name: Get a list of groups (Partially allowed, user aware)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
     params:
       groups_list: 'default,group2,group3'
   response:
     status_code: 200
     json:
       error: 2
       data:
         affected_items:
           - name: 'default'
           - name: 'group2'
         failed_items:
           - error:
               code: 4000
             id:
               - 'group3'
         total_affected_items: 2
         total_failed_items: 1

 - name: Get a list of groups (Allowed)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
     params:
       groups_list: 'group1,group2'
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - name: 'group1'
           - name: 'group2'
         failed_items: []
         total_affected_items: 2
         total_failed_items: 0

---
test_name: GET /groups/{group_id}/agents

stages:

 - name: Try get all agents in one group (Partially allowed, user agnostic)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups/group2/agents"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - id: '002'
           - id: '006'
           - id: '008'
           - id: '010'
         failed_items: []
         total_affected_items: 4
         total_failed_items: 0

---
test_name: GET /groups/{group_id}/configuration

stages:

 - name: Try to get the configuration of a group (Denied)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups/group3/configuration"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
      <<: *permission_denied

 - name: Try get the configuration of a group (Allowed)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups/group1/configuration"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200

---
test_name: GET /groups/{group_id}/files

stages:

 - name: Try get the files of a group (Denied)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups/group3/files"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
      <<: *permission_denied

 - name: Try get the files of a group (Allow)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups/group2/files"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
     params:
       limit: 2
       offset: 0
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - filename: agent.conf
             hash: !anystr
           - filename: ar.conf
             hash: !anystr
         total_affected_items: 3
         total_failed_items: 0
         failed_items: []

---
test_name: GET /groups/{group_id}/files/{filename}

stages:

 - name: Try get one file of a group (Denied)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups/group3/files/agent.conf"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
      <<: *permission_denied

 - name: Try get one file of a group (Allowed)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/groups/default/files/agent.conf"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200

---
test_name: GET /agents?name=agent_name

stages:

 - name: Basic response agents name (Denied)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
     params:
       name: 'wazuh-agent1'
   response:
     status_code: 200
     json:
       error: 0
       data:
         total_affected_items: 0

 - name: Basic response agents name (Allowed)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
     params:
       name: 'wazuh-agent2'
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - id: '002'
             name: 'wazuh-agent2'
         failed_items: []
         total_affected_items: 1
         total_failed_items: 0

---
test_name: GET /agents/no_group

stages:

 - name: Get the agents without group (Partially allowed, user agnostic)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/no_group"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - id: '012'
         failed_items: []
         total_affected_items: 1
         total_failed_items: 0

---
test_name: GET /agents/outdated

stages:

 - name: Get the outdated agents (Partially allowed, user agnostic)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/outdated"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - id: '006'
           - id: '008'
           - id: '010'
         failed_items: []
         total_affected_items: !anyint
         total_failed_items: 0

---
test_name: GET /agents/stats/distinct

stages:

 - name: Get the different combinations for os.platform (Partially allowed, user agnostic)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/stats/distinct"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
     params:
       limit: 2
       fields: os.platform
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - count: 6
             os:
               platform: !anystr
         failed_items: []
         total_affected_items: !anyint
         total_failed_items: 0

---
test_name: GET /agents/summary/status

stages:

 - name: Get the agents status summary (Partially allowed, user agnostic)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/summary/status"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200
     json:
       error: 0
       data:
         connection:
           active: 4
           disconnected: 1
           never_connected: 1
           pending: 0
           total: 6
         configuration:
           synced: 4
           not_synced: 2
           total: 6

---
test_name: GET /agents/summary/os

stages:

 - name: Get the summary/os of all agents (Partially allowed, user agnostic)
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/agents/summary/os"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     status_code: 200
     json:
       error: 0
       data:
         affected_items:
           - ubuntu
         total_affected_items: 1
         total_failed_items: 0
         failed_items: []

---
test_name: DELETE /agents/{agent_id}/group/{group_id}

stages:

  - name: Try to remove agent 001 from group1 (Denied group)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/group/group1"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to remove agent 004 from group3 (Denied agent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/004/group/group3"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to remove agent 999 from default (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/999/group/default"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &resource_not_found
      status_code: 404
      json:
        error: 1701

  - name: Try to remove agent 999 from group1 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/999/group/group1"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to remove agent 002 from group3 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/002/group/group3"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        error: 1734

  - name: Remove agent 002 from group2 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/002/group/group2"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

---
test_name: DELETE /agents/{agent_id}/group

stages:

  - name: Try to remove agent 009 from all groups (Denied agent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/009/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to remove agent 001 from group1 (Denied group)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        groups_list: 'group1'
    response:
      <<: *permission_denied

  - name: Remove agent 006 from all groups (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/006/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - 'group2'
            - 'group3'
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Try to remove agent 005 from all groups (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/005/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - 'default'
            - 'group2'
          total_affected_items: 2
          total_failed_items: 0

  - name: Try to remove agent 007 from a list of groups (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/007/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        groups_list: 'group1,group3,group2'
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["007"]
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - 'group3'
          failed_items:
            - error:
                code: 1734
              id:
                - 'group2'
            - error:
                code: 4000
              id:
                - 'group1'
          total_affected_items: 1
          total_failed_items: 2

---
test_name: DELETE /agents/group?group_id={group_id}

stages:

  - name: Try to remove all agents from a non existing group
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        group_id: 'wrong_group'
        agents_list: 'all'
    response: &resource_not_found_group
      status_code: 404
      json:
        error: 1710

  - name: Try to remove all agents from group1 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        group_id: 'group1'
        agents_list: 'all'
    response:
      <<: *permission_denied

  - name: Remove all agents from group2 (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        group_id: 'group2'
        agents_list: 'all'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '008'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Remove a list of agents from group3 (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '004,007,008,998,999'
        group_id: 'group3'
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '008'
          failed_items:
            - error:
                code: 1701
              id:
                - '999'
            - error:
                code: 1734
              id:
                - '007'
            - error:
                code: 4000
              id:
                - '004'
                - '998'
          total_affected_items: 1
          total_failed_items: 4

  - name: Remove a list of agents from group default (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,008'
        group_id: 'default'
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["001", "008"]
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '001'
            - '008'
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

---
test_name: DELETE /groups?groups_list={group_id}

stages:

  - name: Try to delete group2 (Group denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        groups_list: 'group2'
    response:
      <<: *permission_denied

  - name: Delete group3
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        groups_list: 'group3'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - group3
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    delay_after: !float "{global_db_delay}"

---
test_name: DELETE /groups

stages:

  - name: Try to delete all groups (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        groups_list: 'all'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - group1
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to delete a list of groups (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        groups_list: 'default,group1,group2,pepito,antonio'
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1710
              id:
                - group1
                - pepito
            - error:
                code: 1712
              id:
                - default
            - error:
                code: 4000
              id:
                - antonio
                - group2
          total_affected_items: 0
          total_failed_items: 5

---
test_name: DELETE /agents?agents_list=agent_id&status=all

stages:

  - name: Try to delete agent 002 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '002'
        status: 'all'
    response:
      <<: *permission_denied

  - name: Delete agent 001 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001'
        older_than: '0s'
        status: 'all'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '001'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    delay_after: !float "{global_db_delay}"

---
test_name: DELETE /agents

stages:

  - name: Try to delete a list of agents (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '004,010'
        status: 'all'
    response:
      <<: *permission_denied

  - name: Try to delete a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '000,007,999'
        older_than: '0s'
        status: 'all'
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '007'
          failed_items:
            - error:
                code: 4000
              id:
                - '000'
                - '999'
          total_affected_items: 1
          total_failed_items: 2
    delay_after: !float "{global_db_delay}"

  - name: Try to delete all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        older_than: '0s'
        agents_list: 'all'
        status: 'all'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '003'
            - '005'
            - '009'
            - '011'
          total_affected_items: 4
          total_failed_items: 0
    delay_after: !float "{global_db_delay}"

---
test_name: POST /agents

stages:

  - name: Try to create a new agent (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: POST
      headers:
        Authorization: "Bearer {test_login_token}"
      json:
        name: "NewAgentPost"
        ip: "any"
    response:
      <<: *permission_denied

---
test_name: POST /agents/insert

stages:

  - name: Try to create a new agent specifying id, ip, key and name (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/insert"
      method: POST
      headers:
        Authorization: "Bearer {test_login_token}"
      json:
        name: "NewAgentPostInsert"
        ip: "any"
        id: "750"
        key: "1abcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzabcdefghi64"
    response:
      <<: *permission_denied

---
test_name: POST /agents/insert/quick

stages:

  - name: Try to create new agent specifying its name (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/insert/quick"
      method: POST
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agent_name: 'test_agent'
    response:
      <<: *permission_denied

---
test_name: POST /groups

stages:

  - name: Try to create a group called group4 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups"
      method: POST
      headers:
        Authorization: "Bearer {test_login_token}"
      json:
        group_id: "group4"
    response:
      <<: *permission_denied

---
test_name: PUT /agents/group

stages:

  - name: Try to assign all agents to a non existing group (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        group_id: 'wrong_group'
    response:
      <<: *resource_not_found_group

  - name: Try to assign all agents to group1 (Group denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        group_id: 'group1'
    response:
      <<: *permission_denied

  - name: Try to assign a list of agents to group2 (Agents denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '004,009,010'
        group_id: 'group2'
    response:
      <<: *permission_denied

  - name: Try to assign a list of agents to group2 (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,002,005'
        group_id: 'group2'
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["002"]
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '002'
          failed_items:
            - error:
                code: 1701
              id:
                - '001'
                - '005'
          total_affected_items: 1
          total_failed_items: 2

  - name: Try to assign all agents to group2 (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        group_id: 'group2'
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["006", "008"]
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '006'
            - '008'
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

---
test_name: PUT /groups/{group_id}/configuration

stages:

  - name: Update group2 configuration (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups/group2/configuration"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: "application/xml"
      data:
        "{file_xml:s}"
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["002", "006", "008"]
      status_code: 200

  - name: Try to update group1 configuration (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/groups/group1/configuration"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: "application/xml"
      data:
        "{file_xml:s}"
    response:
      <<: *permission_denied

---
test_name: PUT /agents/group/{group_id}/restart

stages:

    # PUT /agents/group/default/restart
  - name: Restart all agents from group default (Partially allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group/default/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["004"]
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - "004"
          total_affected_items: 1
          failed_items:
            - error:
                code: 4000
              id:
                - "002"
                - "006"
                - "008"
                - "010"
          total_failed_items: 4
        message: !anystr

---
test_name: PUT /agents/reconnect

stages:

  - name: Try to reconnect non existing agent (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/reconnect"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "997"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1701
              id:
                - '997'
          total_affected_items: 0
          total_failed_items: 1

  - name: Try to reconnect agent 010 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/reconnect"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "010"
    response:
      <<: *permission_denied

  - name: Try to reconnect a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/reconnect"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "000,001,002,004"
        wait_for_complete: true  # Prevent error code 500
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '004'
          failed_items:
            - error:
                code: 1701
              id:
                - '001'
            - error:
                code: 1703
              id:
                - '000'
            - error:
                code: 4000
              id:
                - '002'
          total_affected_items: 1
          total_failed_items: 3
    delay_after: !float "{reconnect_delay}"

  - name: Try to reconnect all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/reconnect"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '004'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    delay_after: !float "{reconnect_delay}"

---
test_name: PUT /agents/restart

stages:

  - name: Try to restart non existing agent (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "997"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1701
              id:
                - '997'
          total_affected_items: 0
          total_failed_items: 1

  - name: Try to restart agent 010 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "010"
    response:
      <<: *permission_denied

  - name: Try to restart a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "000,001,002,004"
        wait_for_complete: true  # Prevent error code 500
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["004"]
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - '004'
          failed_items:
            - error:
                code: 1701
              id:
                - '001'
            - error:
                code: 1703
              id:
                - '000'
            - error:
                code: 4000
              id:
                - '002'
          total_affected_items: 1
          total_failed_items: 3

  - name: Try to restart all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["004"]
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '004'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: PUT /agents/{agent_id}/group/{group_id}

stages:

  - name: Try to assign agent 004 to group2 (Agent denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/004/group/group2"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to assign agent 008 to group1 (Group denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/008/group/group1"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to assign agent 008 to group3 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/008/group/group3"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *resource_not_found_group

  - name: Try to assign agent 001 to group2 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/group/group2"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1701
              id:
                - '001'
          total_affected_items: 0
          total_failed_items: 1

  # Agent assignment below requires the agent not to be in the group to succeed
  - name: Remove agent 008 from group default (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/group"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '008'
        group_id: 'default'
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["008"]
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '008'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Assign agent 008 to default (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/008/group/default"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["008"]
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '008'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: PUT /agents/{agent_id}/restart

stages:

  - name: Try to restart agent 001 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/001/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1701
              id:
                - '001'
          total_affected_items: 0
          total_failed_items: 1

  - name: Try to restart agent 010 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/010/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Restart agent 004 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/004/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      verify_response_with:
        function: tavern_utils:healthcheck_agent_restart
        extra_kwargs:
          agents_list: ["004"]
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - '004'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: PUT /agents/upgrade

stages:

  - name: Upgrade agents (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        version: 4.0.0
        agents_list: '005,006'
    response:
      status_code: 200

  - name: Try to upgrade agents 003 and 004 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "003,004"
    response:
      <<: *permission_denied

---
test_name: PUT /agents/upgrade_custom

stages:

  - name: Customly upgrade agents 007 and 008 using default installer (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_custom"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "007,008"
        file_path: "{custom_wpk_path:s}"
    response:
      status_code: 200

  - name: Try to customly upgrade agents 003 and 004 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_custom"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "003,004"
        file_path: "{custom_wpk_path:s}"
    response:
      <<: *permission_denied

---
test_name: GET /agents/upgrade_result

stages:

  - name: Get upgrade result from agents list (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_result"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "005,006,007,008"
    response:
      status_code: 200

  - name: Try get upgrade result agents 003 and 004 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade_result"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "003,004"
    response:
      <<: *permission_denied

---
test_name: GET /agents (deny cluster:read)

stages:

  - name: Get unknown-node on failed response
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001'
    response:
      <<: *permission_denied

---
test_name: PUT /agents/node/{node_id}/restart

stages:

  - name: Restart all agents belonging to master-node (Deny node_id)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/node/master-node/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /agents/uninstall

stages:

  - name: Check user's permission to uninstall agents (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/uninstall"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        message: "User has permission to uninstall agents"
        error: 0
