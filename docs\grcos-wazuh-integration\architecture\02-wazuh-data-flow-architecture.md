# Wazuh Data Flow Architecture

## Overview

This document details the comprehensive data flow architecture for Wazuh SIEM/XDR integration within the GRCOS platform. The architecture ensures real-time security event processing, intelligent threat correlation, and seamless integration with GRCOS's blockchain-secured compliance framework.

## Data Flow Patterns

### Primary Data Flows

```mermaid
graph TB
    subgraph "Data Sources"
        A[IT Infrastructure]
        B[OT Networks]
        C[IoT Devices]
        D[Cloud Services]
        E[Network Equipment]
    end
    
    subgraph "Wazuh Agent Layer"
        F[Wazuh Agents]
        G[Agentless Monitoring]
        H[API Integrations]
    end
    
    subgraph "Wazuh Manager Cluster"
        I[Event Collection]
        J[Rule Processing]
        K[Alert Generation]
        L[Data Enrichment]
    end
    
    subgraph "GRCOS Integration Layer"
        M[Event Stream Processor]
        N[Data Synchronizer]
        O[API Gateway]
        P[Message Queue]
    end
    
    subgraph "GRCOS Monitor Module"
        Q[Event Classifier]
        R[Threat Detector]
        S[Incident Manager]
        T[Compliance Monitor]
    end
    
    subgraph "GRCOS Storage Layer"
        U[Event Database]
        V[Incident Database]
        W[Compliance Database]
        X[Blockchain Evidence]
    end
    
    A --> F
    B --> F
    C --> F
    D --> G
    E --> H
    
    F --> I
    G --> I
    H --> I
    
    I --> J
    J --> K
    K --> L
    
    L --> M
    L --> N
    L --> O
    
    M --> P
    N --> P
    O --> P
    
    P --> Q
    Q --> R
    R --> S
    S --> T
    
    Q --> U
    R --> V
    S --> V
    T --> W
    
    U --> X
    V --> X
    W --> X
```

### Real-time Event Processing Flow

#### 1. Event Collection and Ingestion
```yaml
Flow: Source → Wazuh Agent → Wazuh Manager → GRCOS
Latency: < 5 seconds end-to-end
Volume: 10,000+ events/second
Format: JSON over HTTPS/WebSocket
Reliability: At-least-once delivery with deduplication
```

#### 2. Event Processing Pipeline
```yaml
Stage 1: Raw Event Ingestion
  - Event validation and parsing
  - Duplicate detection and filtering
  - Initial classification and routing

Stage 2: Rule-based Processing
  - Wazuh rule engine evaluation
  - Alert generation and severity assignment
  - Compliance framework mapping

Stage 3: AI-enhanced Analysis
  - Threat intelligence correlation
  - Behavioral analysis and anomaly detection
  - Risk scoring and prioritization

Stage 4: Response Orchestration
  - Policy-based response determination
  - Automated containment actions
  - Incident workflow initiation
```

## Event Data Models

### Wazuh Security Event Schema

```json
{
  "event_metadata": {
    "timestamp": "2024-01-15T15:45:30.123Z",
    "event_id": "**********.123456",
    "source": "wazuh-manager",
    "version": "4.7.0"
  },
  "agent_info": {
    "id": "001",
    "name": "web-server-01",
    "ip": "*************",
    "os": {
      "platform": "ubuntu",
      "version": "20.04.3 LTS",
      "architecture": "x86_64"
    },
    "groups": ["default", "web-servers"],
    "status": "active",
    "last_keepalive": "2024-01-15T15:45:25.000Z"
  },
  "rule_info": {
    "id": 5712,
    "level": 10,
    "description": "Multiple authentication failures",
    "groups": ["authentication_failed", "authentication_failures"],
    "firedtimes": 1,
    "frequency": 8,
    "timeframe": 240,
    "compliance": {
      "pci_dss": ["10.2.4", "10.2.5"],
      "gdpr": ["IV_35.7.d", "IV_32.2"],
      "hipaa": ["164.312.b"],
      "nist_800_53": ["AU.14", "AC.7"],
      "tsc": ["CC6.1", "CC6.8", "CC7.2", "CC7.3"]
    },
    "mitre": {
      "tactic": ["Credential Access"],
      "technique": ["T1110"],
      "subtechnique": ["T1110.001"]
    }
  },
  "log_data": {
    "full_log": "Jan 15 15:45:30 web-server-01 sshd[12345]: Failed password for invalid user admin from ************* port 22 ssh2",
    "location": "/var/log/auth.log",
    "decoder": {
      "name": "sshd",
      "parent": "sshd"
    },
    "extracted_data": {
      "srcip": "*************",
      "srcport": "22",
      "dstuser": "admin",
      "protocol": "ssh",
      "action": "failed_login"
    }
  },
  "enrichment_data": {
    "geo_location": {
      "country": "Unknown",
      "city": "Unknown",
      "coordinates": null
    },
    "threat_intelligence": {
      "reputation": "unknown",
      "categories": [],
      "confidence": 0.0
    },
    "asset_context": {
      "criticality": "high",
      "business_function": "web_services",
      "data_classification": "confidential"
    }
  }
}
```

### GRCOS Enhanced Event Schema

```json
{
  "grcos_metadata": {
    "processing_timestamp": "2024-01-15T15:45:30.456Z",
    "correlation_id": "grcos-corr-123456",
    "processing_node": "grcos-monitor-01",
    "version": "2.0.0"
  },
  "source_event": {
    "wazuh_event": "... (complete Wazuh event)"
  },
  "ai_analysis": {
    "threat_score": 0.85,
    "confidence": 0.92,
    "threat_categories": ["brute_force", "credential_access"],
    "behavioral_analysis": {
      "anomaly_score": 0.78,
      "baseline_deviation": 2.3,
      "pattern_match": true
    },
    "correlation_results": {
      "related_events": ["event-123", "event-124"],
      "attack_chain_position": "initial_access",
      "campaign_indicators": []
    }
  },
  "policy_evaluation": {
    "opa_decision": {
      "allow": false,
      "escalate": true,
      "contain": true,
      "notify": ["security_team", "soc_manager"]
    },
    "response_actions": [
      {
        "action": "block_source_ip",
        "parameters": {"ip": "*************", "duration": 3600},
        "priority": "immediate"
      },
      {
        "action": "increase_monitoring",
        "parameters": {"agent_id": "001", "duration": 7200},
        "priority": "high"
      }
    ]
  },
  "oscal_mapping": {
    "controls": [
      {
        "control_id": "ac-7",
        "implementation_status": "non_compliant",
        "finding_type": "violation",
        "evidence_id": "evidence-123456"
      }
    ],
    "assessment_result_uuid": "ar-uuid-123456"
  },
  "blockchain_evidence": {
    "evidence_hash": "sha256:abc123...",
    "blockchain_tx_id": "tx-123456",
    "timestamp": "2024-01-15T15:45:30.789Z",
    "verification_status": "pending"
  }
}
```

## Data Transformation Pipelines

### Event Enrichment Pipeline

```python
class EventEnrichmentPipeline:
    def __init__(self, threat_intel_client, asset_db, geo_service):
        self.threat_intel = threat_intel_client
        self.asset_db = asset_db
        self.geo_service = geo_service
    
    async def enrich_event(self, wazuh_event):
        """Enrich Wazuh event with additional context"""
        enriched_event = wazuh_event.copy()
        
        # Threat intelligence enrichment
        if 'srcip' in wazuh_event.get('data', {}):
            threat_info = await self.threat_intel.lookup_ip(
                wazuh_event['data']['srcip']
            )
            enriched_event['threat_intelligence'] = threat_info
        
        # Asset context enrichment
        agent_id = wazuh_event['agent']['id']
        asset_info = await self.asset_db.get_asset_context(agent_id)
        enriched_event['asset_context'] = asset_info
        
        # Geolocation enrichment
        if 'srcip' in wazuh_event.get('data', {}):
            geo_info = await self.geo_service.lookup_location(
                wazuh_event['data']['srcip']
            )
            enriched_event['geo_location'] = geo_info
        
        # Business context enrichment
        business_context = await self._get_business_context(wazuh_event)
        enriched_event['business_context'] = business_context
        
        return enriched_event
    
    async def _get_business_context(self, event):
        """Get business context for the event"""
        agent_id = event['agent']['id']
        
        # Get asset business function
        asset_info = await self.asset_db.get_asset(agent_id)
        
        # Get business impact assessment
        business_impact = await self._assess_business_impact(asset_info)
        
        return {
            "business_function": asset_info.get('business_function'),
            "criticality": asset_info.get('criticality', 'medium'),
            "impact_assessment": business_impact,
            "stakeholders": asset_info.get('stakeholders', []),
            "compliance_requirements": asset_info.get('compliance_requirements', [])
        }
```

### Correlation Engine

```python
class EventCorrelationEngine:
    def __init__(self, event_store, ml_model):
        self.event_store = event_store
        self.ml_model = ml_model
        self.correlation_rules = self._load_correlation_rules()
    
    async def correlate_events(self, new_event):
        """Correlate new event with historical events"""
        correlation_results = {
            "related_events": [],
            "attack_patterns": [],
            "campaign_indicators": [],
            "correlation_score": 0.0
        }
        
        # Time-based correlation (last 24 hours)
        time_window = datetime.utcnow() - timedelta(hours=24)
        recent_events = await self.event_store.get_events_since(time_window)
        
        # Rule-based correlation
        rule_correlations = await self._apply_correlation_rules(
            new_event, recent_events
        )
        correlation_results["related_events"].extend(rule_correlations)
        
        # ML-based correlation
        ml_correlations = await self._ml_correlation_analysis(
            new_event, recent_events
        )
        correlation_results["attack_patterns"].extend(ml_correlations)
        
        # Campaign detection
        campaign_indicators = await self._detect_campaign_indicators(
            new_event, recent_events
        )
        correlation_results["campaign_indicators"] = campaign_indicators
        
        # Calculate overall correlation score
        correlation_results["correlation_score"] = self._calculate_correlation_score(
            correlation_results
        )
        
        return correlation_results
    
    async def _apply_correlation_rules(self, new_event, recent_events):
        """Apply rule-based correlation logic"""
        correlations = []
        
        for rule in self.correlation_rules:
            if rule["enabled"]:
                matches = await self._evaluate_correlation_rule(
                    rule, new_event, recent_events
                )
                correlations.extend(matches)
        
        return correlations
    
    async def _ml_correlation_analysis(self, new_event, recent_events):
        """Use ML model for advanced correlation"""
        # Feature extraction
        features = self._extract_correlation_features(new_event, recent_events)
        
        # ML prediction
        predictions = await self.ml_model.predict_correlations(features)
        
        # Convert predictions to correlation results
        correlations = self._convert_predictions_to_correlations(predictions)
        
        return correlations
```

## Data Storage Architecture

### Multi-tier Storage Strategy

```yaml
Tier 1: Hot Storage (Real-time Access)
  Technology: Redis Cluster
  Retention: 24 hours
  Use Cases:
    - Real-time event processing
    - Active incident tracking
    - Correlation analysis
  Performance: < 1ms latency

Tier 2: Warm Storage (Operational Access)
  Technology: MongoDB Sharded Cluster
  Retention: 90 days
  Use Cases:
    - Historical analysis
    - Trend analysis
    - Compliance reporting
  Performance: < 100ms latency

Tier 3: Cold Storage (Archival)
  Technology: Blockchain + IPFS
  Retention: 7+ years
  Use Cases:
    - Long-term compliance
    - Legal evidence
    - Audit trails
  Performance: < 5 seconds latency

Tier 4: Analytics Storage
  Technology: ClickHouse
  Retention: 2 years
  Use Cases:
    - Business intelligence
    - Performance analytics
    - Capacity planning
  Performance: < 1 second for complex queries
```

### Data Partitioning Strategy

```python
class DataPartitioningManager:
    def __init__(self, storage_clients):
        self.redis_client = storage_clients['redis']
        self.mongodb_client = storage_clients['mongodb']
        self.blockchain_client = storage_clients['blockchain']
        self.clickhouse_client = storage_clients['clickhouse']
    
    async def store_event(self, enriched_event):
        """Store event across multiple tiers based on data lifecycle"""
        event_id = enriched_event['grcos_metadata']['correlation_id']
        timestamp = enriched_event['grcos_metadata']['processing_timestamp']
        
        # Tier 1: Hot storage for real-time access
        await self.redis_client.store_event(
            key=f"event:{event_id}",
            data=enriched_event,
            ttl=86400  # 24 hours
        )
        
        # Tier 2: Warm storage for operational access
        await self.mongodb_client.events.insert_one({
            "_id": event_id,
            "timestamp": timestamp,
            "data": enriched_event,
            "indexed_fields": self._extract_indexed_fields(enriched_event)
        })
        
        # Tier 3: Cold storage for compliance (if required)
        if self._requires_long_term_storage(enriched_event):
            evidence_hash = await self.blockchain_client.store_evidence(
                enriched_event
            )
            enriched_event['blockchain_evidence']['evidence_hash'] = evidence_hash
        
        # Tier 4: Analytics storage for BI
        analytics_record = self._transform_for_analytics(enriched_event)
        await self.clickhouse_client.insert_event(analytics_record)
    
    def _requires_long_term_storage(self, event):
        """Determine if event requires long-term blockchain storage"""
        # High severity events
        if event['source_event']['rule_info']['level'] >= 10:
            return True
        
        # Compliance-related events
        compliance_frameworks = event['source_event']['rule_info'].get('compliance', {})
        if any(compliance_frameworks.values()):
            return True
        
        # Incident-related events
        if event['policy_evaluation']['opa_decision']['escalate']:
            return True
        
        return False
```

## Performance Optimization

### Stream Processing Optimization

```python
class StreamProcessingOptimizer:
    def __init__(self, config):
        self.config = config
        self.metrics = {}
        self.adaptive_batching = AdaptiveBatchingManager()
    
    async def optimize_processing_pipeline(self):
        """Continuously optimize processing pipeline performance"""
        while True:
            current_metrics = await self._collect_performance_metrics()
            
            # Optimize batch sizes
            await self.adaptive_batching.adjust_batch_size(current_metrics)
            
            # Optimize worker allocation
            await self._optimize_worker_allocation(current_metrics)
            
            # Optimize memory usage
            await self._optimize_memory_usage(current_metrics)
            
            # Sleep before next optimization cycle
            await asyncio.sleep(self.config['optimization_interval'])
    
    async def _optimize_worker_allocation(self, metrics):
        """Dynamically adjust worker allocation based on load"""
        queue_depth = metrics['event_queue_depth']
        processing_latency = metrics['avg_processing_latency']
        
        if queue_depth > 5000 and processing_latency > 100:
            # Scale up workers
            await self._scale_up_workers()
        elif queue_depth < 1000 and processing_latency < 50:
            # Scale down workers
            await self._scale_down_workers()
```

### Caching Strategy

```python
class IntelligentCachingManager:
    def __init__(self, redis_client, cache_config):
        self.redis = redis_client
        self.config = cache_config
        self.cache_stats = {}
    
    async def get_cached_data(self, cache_key, fetch_function, ttl=None):
        """Intelligent caching with adaptive TTL"""
        # Try to get from cache
        cached_data = await self.redis.get(cache_key)
        if cached_data:
            await self._update_cache_stats(cache_key, 'hit')
            return json.loads(cached_data)
        
        # Cache miss - fetch data
        data = await fetch_function()
        
        # Determine optimal TTL based on data characteristics
        optimal_ttl = self._calculate_optimal_ttl(cache_key, data, ttl)
        
        # Store in cache
        await self.redis.setex(
            cache_key, 
            optimal_ttl, 
            json.dumps(data, default=str)
        )
        
        await self._update_cache_stats(cache_key, 'miss')
        return data
    
    def _calculate_optimal_ttl(self, cache_key, data, default_ttl):
        """Calculate optimal TTL based on data characteristics and access patterns"""
        # Base TTL
        ttl = default_ttl or self.config['default_ttl']
        
        # Adjust based on data volatility
        if 'timestamp' in str(data):
            # Time-sensitive data gets shorter TTL
            ttl = min(ttl, 300)  # 5 minutes max
        
        # Adjust based on access patterns
        access_frequency = self.cache_stats.get(cache_key, {}).get('frequency', 0)
        if access_frequency > 100:  # Frequently accessed
            ttl *= 2  # Longer TTL for hot data
        
        return ttl
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Data Architecture Team
