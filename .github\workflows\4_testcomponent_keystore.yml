name: Wazuh Keystore

on:
  workflow_dispatch:

jobs:
  wazuh-keystore-qa:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          submodules: recursive

      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake

      - name: Compile keystore
        run: |
          make deps TARGET=server -j2 -C src/
          make libwazuhext.so TARGET=server -j2 -C src/
          mkdir -p src/shared_modules/keystore/build
          cmake -S src/shared_modules/keystore -B  src/shared_modules/keystore/build -DSRC_FOLDER=$(pwd)/src
          cmake --build src/shared_modules/keystore/build -j2
          find . -name "wazuh-keystore-testtool" -exec mv {} src/ \;

      - name: Install dependencies
        run: |
          pip install -r src/shared_modules/keystore/qa/requirements.txt

      - name: Run tests
        run: |
          python -m pytest -vv src/shared_modules/keystore/qa/ --log-cli-level=DEBUG
