name: Package - Build multiple Wazuh Manager Packages - DEB|RPM - amd64|x86_64
on:
  workflow_dispatch:
    inputs:
      build-amd64-deb:
        description: 'Build .deb wazuh-manager on amd64 arch'
        type: boolean
        required: false
      build-x86_64-rpm:
        description: 'Build .rpm wazuh-manager on x86_64 arch'
        type: boolean
        required: false
      revision:
        description: |
          Set the value to "1" for packages in release format.
          You can also add other values, such as issue numbers.
          By default, it is set to "0" for development.
        default: "0"
        type: string
        required: false
      is_stage:
          description: |
            Build package with release format.
            By default: false
          type: boolean
          required: false
      debug:
        description: |
          Build the binaries as debug (without optimizations).
          By default: false
        type: boolean
        required: false

jobs:
   Build-manager-amd64-deb:
    if: ${{ inputs.build-amd64-deb == true }}
    uses: ./.github/workflows/4_builderpackage_manager.yml 
    with:
        docker_image_tag: auto
        architecture: amd64
        system: deb
        revision: ${{ inputs.revision}}
        is_stage: ${{ inputs.is_stage }}
        debug: ${{ inputs.debug }}
    secrets: inherit

   Build-manager-x86_64-rpm:
    if: ${{ inputs.build-x86_64-rpm == true }}
    uses: ./.github/workflows/4_builderpackage_manager.yml 
    with:
        docker_image_tag: auto
        architecture: x86_64
        system: rpm
        revision: ${{ inputs.revision}}
        is_stage: ${{ inputs.is_stage }}
        debug: ${{ inputs.debug }}
    secrets: inherit
