name: Running RTR. Module syscollector and its dependencies for agent/winagent targets

on:
  workflow_dispatch:

jobs:
  rtr:
    strategy:
          fail-fast: false
          matrix:
              module: [wazuh_modules/syscollector, shared_modules/dbsync, shared_modules/rsync, data_provider]
              target: [server, agent, winagent]
    # We don't use ubuntu-latest because the install_build_deps action adds a fixed repository for Wine
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
        with:
          submodules: recursive
      - name: "Install dependencies"
        uses: ./.github/actions/install_build_deps
        with:
          target: ${{ matrix.target }}
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version-file: ".github/workflows/.python-version"
          architecture: x64
      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake
      - name: Run RTR for module ${{ matrix.module }} and target ${{ matrix.target }}
        run: |
          cd src/
          python build.py --target ${{ matrix.target }} --readytoreview ${{ matrix.module }}
      - name: Creating a valid artifact name
        if: always()
        run: |
          name="${{ matrix.module }}"
          name=$(echo $name | sed -e 's*/*-*g')
          echo "ARTIFACT_NAME=$name" >> $GITHUB_ENV
      - name: Uploading coverage report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: coverage_report ${{ env.ARTIFACT_NAME }} ${{ matrix.target }}
          path: ./**/coverage_report/**
