# GRCOS Portals Module OSCAL Integration

## Overview

The Portals Module provides stakeholder-specific trust portals and interfaces using OSCAL data to enable AI-powered chatbots, self-service compliance questionnaires, and secure stakeholder communication. This module creates customized views of OSCAL compliance data for different stakeholder types including auditors, customers, regulators, and business partners.

## OSCAL Model Integration

### Stakeholder Portal Configuration

#### Portal Access Control Based on OSCAL Responsible Parties
```json
{
  "portal-configuration": {
    "portal_uuid": "portal-auditor-fedramp",
    "portal_type": "auditor",
    "title": "FedRAMP Auditor Portal",
    "description": "Secure portal for FedRAMP assessors and auditors",
    "access_control": {
      "responsible_party_roles": ["assessor", "auditor", "fedramp-pmo"],
      "required_clearance": "moderate",
      "authentication_method": "pki_certificate"
    },
    "oscal_data_access": {
      "allowed_document_types": [
        "system-security-plan",
        "assessment-plan", 
        "assessment-results",
        "plan-of-action-and-milestones"
      ],
      "data_filters": {
        "system_environment": ["production"],
        "assessment_status": ["completed", "in-progress"],
        "finding_severity": ["critical", "high", "medium", "low"]
      },
      "restricted_fields": [
        "system-implementation.users.internal-details",
        "back-matter.resources.sensitive-configurations"
      ]
    },
    "available_features": [
      "compliance_dashboard",
      "assessment_results_viewer", 
      "evidence_repository",
      "finding_tracker",
      "ai_chatbot",
      "report_generator"
    ]
  }
}
```

### AI-Powered Portal Chatbots

#### OSCAL-Aware Chatbot Engine
```python
class OSCALPortalChatbot:
    """
    AI-powered chatbot for stakeholder portals with OSCAL knowledge
    """
    
    def __init__(self, oscal_repository, nlp_engine, portal_config):
        self.oscal_repository = oscal_repository
        self.nlp_engine = nlp_engine
        self.portal_config = portal_config
        self.conversation_context = {}
        self.oscal_knowledge_base = self._build_oscal_knowledge_base()
    
    def process_stakeholder_query(self, user_context, query, conversation_id=None):
        """
        Process stakeholder query using OSCAL data and AI
        """
        # Initialize or retrieve conversation context
        if conversation_id:
            context = self.conversation_context.get(conversation_id, {})
        else:
            conversation_id = str(uuid.uuid4())
            context = {"conversation_id": conversation_id, "user_context": user_context}
        
        # Analyze query intent and extract OSCAL-related entities
        query_analysis = self._analyze_query(query, context)
        
        # Determine response strategy
        response_strategy = self._determine_response_strategy(query_analysis, user_context)
        
        # Generate response based on strategy
        if response_strategy["type"] == "oscal_data_query":
            response = self._handle_oscal_data_query(query_analysis, user_context)
        elif response_strategy["type"] == "compliance_status":
            response = self._handle_compliance_status_query(query_analysis, user_context)
        elif response_strategy["type"] == "finding_inquiry":
            response = self._handle_finding_inquiry(query_analysis, user_context)
        elif response_strategy["type"] == "evidence_request":
            response = self._handle_evidence_request(query_analysis, user_context)
        else:
            response = self._handle_general_query(query_analysis, user_context)
        
        # Update conversation context
        context["last_query"] = query
        context["last_response"] = response
        context["query_count"] = context.get("query_count", 0) + 1
        self.conversation_context[conversation_id] = context
        
        return {
            "conversation_id": conversation_id,
            "response": response,
            "query_analysis": query_analysis,
            "suggested_actions": self._generate_suggested_actions(query_analysis, user_context)
        }
    
    def _analyze_query(self, query, context):
        """
        Analyze user query to extract intent and OSCAL entities
        """
        # Use NLP to extract entities and intent
        nlp_result = self.nlp_engine.analyze(query)
        
        # Extract OSCAL-specific entities
        oscal_entities = {
            "control_ids": self._extract_control_ids(query, nlp_result),
            "system_names": self._extract_system_names(query, nlp_result),
            "finding_types": self._extract_finding_types(query, nlp_result),
            "assessment_periods": self._extract_time_periods(query, nlp_result),
            "compliance_frameworks": self._extract_frameworks(query, nlp_result)
        }
        
        # Determine query intent
        intent = self._classify_intent(query, nlp_result, oscal_entities)
        
        return {
            "original_query": query,
            "intent": intent,
            "entities": oscal_entities,
            "confidence": nlp_result.get("confidence", 0.0),
            "context": context
        }
    
    def _handle_compliance_status_query(self, query_analysis, user_context):
        """
        Handle queries about compliance status
        """
        entities = query_analysis["entities"]
        
        # Determine scope of compliance query
        if entities["system_names"]:
            systems = entities["system_names"]
        else:
            # Get all systems user has access to
            systems = self._get_accessible_systems(user_context)
        
        compliance_data = []
        for system in systems:
            try:
                # Get latest assessment results
                latest_assessment = self.oscal_repository.get_latest_assessment(system)
                ssp = self.oscal_repository.get_ssp(system)
                
                if latest_assessment and ssp:
                    compliance_summary = self._generate_compliance_summary(
                        system, ssp, latest_assessment
                    )
                    compliance_data.append(compliance_summary)
                    
            except Exception as e:
                self._log_query_error(f"Error getting compliance data for {system}: {str(e)}")
        
        # Generate natural language response
        response = self._generate_compliance_response(compliance_data, query_analysis)
        
        return response
    
    def _handle_finding_inquiry(self, query_analysis, user_context):
        """
        Handle queries about specific findings
        """
        entities = query_analysis["entities"]
        
        # Build finding query criteria
        finding_criteria = {
            "control_ids": entities.get("control_ids", []),
            "severity": self._extract_severity_from_query(query_analysis["original_query"]),
            "status": self._extract_status_from_query(query_analysis["original_query"]),
            "time_period": entities.get("assessment_periods", [])
        }
        
        # Get accessible systems
        accessible_systems = self._get_accessible_systems(user_context)
        
        # Query findings
        findings = []
        for system_uuid in accessible_systems:
            system_findings = self.oscal_repository.get_findings_by_criteria(
                system_uuid, finding_criteria
            )
            findings.extend(system_findings)
        
        # Generate response with finding details
        response = self._generate_finding_response(findings, query_analysis)
        
        return response
    
    def _generate_compliance_response(self, compliance_data, query_analysis):
        """
        Generate natural language compliance status response
        """
        if not compliance_data:
            return {
                "text": "I don't have access to compliance data for the requested systems. Please check your permissions or contact your administrator.",
                "type": "error",
                "data": None
            }
        
        # Calculate overall metrics
        total_systems = len(compliance_data)
        avg_compliance_score = sum(data["compliance_score"] for data in compliance_data) / total_systems
        total_critical_findings = sum(data["critical_findings"] for data in compliance_data)
        
        # Generate response text
        response_text = f"Based on the latest assessments of {total_systems} system(s):\n\n"
        response_text += f"• Overall compliance score: {avg_compliance_score:.1f}%\n"
        response_text += f"• Critical findings: {total_critical_findings}\n\n"
        
        if avg_compliance_score >= 90:
            response_text += "✅ Compliance posture is strong across assessed systems."
        elif avg_compliance_score >= 75:
            response_text += "⚠️ Compliance posture is moderate. Some areas need attention."
        else:
            response_text += "🚨 Compliance posture needs immediate attention."
        
        # Add system-specific details
        if total_systems <= 3:
            response_text += "\n\nSystem Details:\n"
            for data in compliance_data:
                response_text += f"• {data['system_name']}: {data['compliance_score']:.1f}% compliant\n"
        
        return {
            "text": response_text,
            "type": "compliance_status",
            "data": {
                "summary": {
                    "total_systems": total_systems,
                    "average_compliance_score": avg_compliance_score,
                    "total_critical_findings": total_critical_findings
                },
                "systems": compliance_data
            }
        }
```

## Self-Service Compliance Questionnaires

### Automated Questionnaire Generation

#### OSCAL-Based Questionnaire Engine
```python
class OSCALQuestionnaireEngine:
    """
    Generate compliance questionnaires from OSCAL control requirements
    """
    
    def __init__(self, oscal_repository, questionnaire_templates):
        self.oscal_repository = oscal_repository
        self.questionnaire_templates = questionnaire_templates
        self.question_generators = self._load_question_generators()
    
    def generate_compliance_questionnaire(self, questionnaire_request):
        """
        Generate compliance questionnaire based on OSCAL controls
        """
        # Load relevant OSCAL documents
        target_profile = self.oscal_repository.get_profile(questionnaire_request["profile_uuid"])
        
        # Resolve profile to get control baseline
        resolved_controls = self._resolve_profile_controls(target_profile)
        
        # Generate questions for each control
        questionnaire = {
            "questionnaire_uuid": str(uuid.uuid4()),
            "title": questionnaire_request.get("title", "Compliance Assessment Questionnaire"),
            "description": questionnaire_request.get("description", ""),
            "metadata": {
                "generated_from_profile": questionnaire_request["profile_uuid"],
                "generation_timestamp": datetime.utcnow().isoformat(),
                "target_audience": questionnaire_request.get("audience", "vendor"),
                "estimated_completion_time": "45 minutes"
            },
            "sections": []
        }
        
        # Group controls by family for better organization
        control_families = self._group_controls_by_family(resolved_controls)
        
        for family_id, family_controls in control_families.items():
            section = self._generate_questionnaire_section(family_id, family_controls)
            questionnaire["sections"].append(section)
        
        return questionnaire
    
    def _generate_questionnaire_section(self, family_id, controls):
        """
        Generate questionnaire section for control family
        """
        family_info = self._get_family_info(family_id)
        
        section = {
            "section_id": family_id,
            "title": family_info["title"],
            "description": family_info["description"],
            "questions": []
        }
        
        for control in controls:
            questions = self._generate_control_questions(control)
            section["questions"].extend(questions)
        
        return section
    
    def _generate_control_questions(self, control):
        """
        Generate questions for specific OSCAL control
        """
        questions = []
        
        # Generate implementation questions
        impl_question = {
            "question_id": f"{control['id']}_implementation",
            "control_id": control["id"],
            "question_type": "multiple_choice",
            "question_text": f"How is control {control['id']} ({control['title']}) implemented in your organization?",
            "options": [
                {"value": "fully_implemented", "text": "Fully implemented and operational"},
                {"value": "partially_implemented", "text": "Partially implemented"},
                {"value": "planned", "text": "Planned for implementation"},
                {"value": "not_applicable", "text": "Not applicable to our environment"},
                {"value": "not_implemented", "text": "Not implemented"}
            ],
            "required": True,
            "help_text": control.get("guidance", "")
        }
        questions.append(impl_question)
        
        # Generate evidence questions
        evidence_question = {
            "question_id": f"{control['id']}_evidence",
            "control_id": control["id"],
            "question_type": "file_upload",
            "question_text": f"Please provide evidence of {control['id']} implementation (optional)",
            "accepted_file_types": [".pdf", ".docx", ".xlsx", ".png", ".jpg"],
            "max_file_size": "10MB",
            "required": False
        }
        questions.append(evidence_question)
        
        # Generate testing questions for high-risk controls
        if self._is_high_risk_control(control):
            testing_question = {
                "question_id": f"{control['id']}_testing",
                "control_id": control["id"],
                "question_type": "text_area",
                "question_text": f"Describe how {control['id']} effectiveness is tested and validated",
                "placeholder": "Describe testing procedures, frequency, and results...",
                "required": True,
                "max_length": 1000
            }
            questions.append(testing_question)
        
        return questions
```

### Stakeholder Communication Hub

#### Secure Communication Platform
```python
class StakeholderCommunicationHub:
    """
    Secure communication platform for compliance stakeholders
    """
    
    def __init__(self, encryption_service, notification_service, oscal_repository):
        self.encryption_service = encryption_service
        self.notification_service = notification_service
        self.oscal_repository = oscal_repository
        self.communication_channels = {}
    
    def create_compliance_discussion(self, discussion_request):
        """
        Create secure discussion thread for compliance topics
        """
        discussion = {
            "discussion_uuid": str(uuid.uuid4()),
            "title": discussion_request["title"],
            "description": discussion_request.get("description", ""),
            "topic_type": discussion_request["topic_type"],  # finding, control, assessment, etc.
            "related_oscal_objects": discussion_request.get("related_oscal_objects", []),
            "participants": discussion_request["participants"],
            "security_classification": discussion_request.get("classification", "internal"),
            "created_by": discussion_request["created_by"],
            "created_at": datetime.utcnow(),
            "status": "active",
            "messages": []
        }
        
        # Set up secure communication channel
        channel_config = self._setup_secure_channel(discussion)
        discussion["channel_config"] = channel_config
        
        # Notify participants
        self._notify_discussion_participants(discussion, "created")
        
        # Store discussion
        self._store_discussion(discussion)
        
        return discussion
    
    def add_discussion_message(self, discussion_uuid, message_request):
        """
        Add message to compliance discussion
        """
        discussion = self._get_discussion(discussion_uuid)
        
        if not discussion:
            raise ValueError("Discussion not found")
        
        # Verify sender permissions
        if not self._verify_participant_permissions(discussion, message_request["sender"]):
            raise PermissionError("Sender not authorized for this discussion")
        
        # Create message
        message = {
            "message_uuid": str(uuid.uuid4()),
            "sender": message_request["sender"],
            "content": message_request["content"],
            "message_type": message_request.get("type", "text"),
            "timestamp": datetime.utcnow(),
            "attachments": message_request.get("attachments", []),
            "references": message_request.get("oscal_references", [])
        }
        
        # Encrypt sensitive content
        if discussion["security_classification"] in ["confidential", "restricted"]:
            message["content"] = self.encryption_service.encrypt(message["content"])
            message["encrypted"] = True
        
        # Add to discussion
        discussion["messages"].append(message)
        discussion["last_activity"] = datetime.utcnow()
        
        # Update discussion
        self._update_discussion(discussion)
        
        # Notify other participants
        self._notify_discussion_participants(discussion, "new_message", message)
        
        return message
    
    def generate_compliance_summary_for_stakeholder(self, stakeholder_context, summary_request):
        """
        Generate customized compliance summary for specific stakeholder
        """
        stakeholder_type = stakeholder_context.get("type", "external")
        access_level = stakeholder_context.get("access_level", "basic")
        
        # Get accessible systems and data
        accessible_systems = self._get_stakeholder_accessible_systems(stakeholder_context)
        
        summary = {
            "summary_uuid": str(uuid.uuid4()),
            "generated_for": stakeholder_context["stakeholder_id"],
            "generation_timestamp": datetime.utcnow(),
            "summary_type": summary_request.get("type", "compliance_overview"),
            "time_period": summary_request.get("time_period", "current"),
            "sections": []
        }
        
        # Generate stakeholder-appropriate sections
        if stakeholder_type == "auditor":
            summary["sections"] = self._generate_auditor_summary_sections(
                accessible_systems, summary_request
            )
        elif stakeholder_type == "customer":
            summary["sections"] = self._generate_customer_summary_sections(
                accessible_systems, summary_request
            )
        elif stakeholder_type == "regulator":
            summary["sections"] = self._generate_regulator_summary_sections(
                accessible_systems, summary_request
            )
        elif stakeholder_type == "vendor":
            summary["sections"] = self._generate_vendor_summary_sections(
                accessible_systems, summary_request
            )
        
        # Apply data filtering based on access level
        summary = self._apply_stakeholder_data_filtering(summary, stakeholder_context)
        
        return summary
```

## Portal Analytics and Insights

### Stakeholder Engagement Analytics

#### Portal Usage Analytics
```python
class PortalAnalyticsEngine:
    """
    Analytics engine for portal usage and stakeholder engagement
    """
    
    def __init__(self, analytics_repository, oscal_repository):
        self.analytics_repository = analytics_repository
        self.oscal_repository = oscal_repository
        self.engagement_metrics = {}
    
    def track_stakeholder_engagement(self, portal_session):
        """
        Track stakeholder engagement with OSCAL data
        """
        engagement_data = {
            "session_uuid": portal_session["session_id"],
            "stakeholder_id": portal_session["user_context"]["stakeholder_id"],
            "stakeholder_type": portal_session["user_context"]["type"],
            "portal_type": portal_session["portal_type"],
            "session_start": portal_session["start_time"],
            "session_duration": portal_session.get("duration", 0),
            "pages_viewed": portal_session.get("pages_viewed", []),
            "oscal_documents_accessed": portal_session.get("documents_accessed", []),
            "queries_submitted": portal_session.get("chatbot_queries", []),
            "downloads_requested": portal_session.get("downloads", []),
            "engagement_score": self._calculate_engagement_score(portal_session)
        }
        
        # Store engagement data
        self.analytics_repository.store_engagement_data(engagement_data)
        
        # Update stakeholder engagement metrics
        self._update_engagement_metrics(engagement_data)
        
        return engagement_data
    
    def generate_stakeholder_insights(self, time_period="30d"):
        """
        Generate insights about stakeholder engagement patterns
        """
        engagement_data = self.analytics_repository.get_engagement_data(time_period)
        
        insights = {
            "analysis_period": time_period,
            "total_sessions": len(engagement_data),
            "unique_stakeholders": len(set(data["stakeholder_id"] for data in engagement_data)),
            "stakeholder_type_breakdown": self._analyze_stakeholder_types(engagement_data),
            "most_accessed_documents": self._analyze_document_access(engagement_data),
            "common_queries": self._analyze_chatbot_queries(engagement_data),
            "engagement_trends": self._analyze_engagement_trends(engagement_data),
            "recommendations": self._generate_engagement_recommendations(engagement_data)
        }
        
        return insights
    
    def _calculate_engagement_score(self, portal_session):
        """
        Calculate engagement score based on session activities
        """
        score = 0
        
        # Base score for session duration
        duration_minutes = portal_session.get("duration", 0) / 60
        score += min(duration_minutes * 2, 20)  # Max 20 points for duration
        
        # Points for document access
        documents_accessed = len(portal_session.get("documents_accessed", []))
        score += documents_accessed * 5  # 5 points per document
        
        # Points for chatbot interaction
        queries_submitted = len(portal_session.get("chatbot_queries", []))
        score += queries_submitted * 3  # 3 points per query
        
        # Points for downloads
        downloads = len(portal_session.get("downloads", []))
        score += downloads * 10  # 10 points per download
        
        # Bonus for completing questionnaires
        if portal_session.get("questionnaire_completed"):
            score += 25
        
        return min(score, 100)  # Cap at 100
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Portals Module Team
