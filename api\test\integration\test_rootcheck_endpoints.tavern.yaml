---
test_name: GET /rootcheck/001/last_scan

stages:

  # GET /rootcheck/001/last_scan
  - name: Get when the last scan for agent 001 started and ended
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001/last_scan"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - start: !anystr
              end: !anystr
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: GET /rootcheck/001

stages:

  # GET /rootcheck/001
  - name: Get rootcheck scan results for agent 001
    request: &get_rootcheck_agent
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        # We get totalItems number of arrays in items, using !anything to check items key is in the response
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        message: !anystr

  # GET /rootcheck/001?limit=1
  - name: Get rootcheck scan results for agent 001 with a set limit of 1 answer
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          # We define this items answer as a common one array full answer
          affected_items: &full_items_array
            - status: !anystr
              log: !anystr
              date_first: !anystr
              date_last: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        message: !anystr

  # GET /rootcheck/001?limit=2&offset=0
  - name: Get rootcheck scan results for agent 001 using limit 2 and offset 0
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *full_items_array
            - <<: *full_items_array
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      # Save second item to check offset in next stage
      save:
        json:
          offset_item: data.affected_items[1]

  # GET /rootcheck/001?limit=1&offset=1
  - name: Get rootcheck scan results for agent 001 using limit 1 and offset 1
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - status: "{offset_item.status}"
              date_last: "{offset_item.date_last}"
              date_first: "{offset_item.date_first}"
              log: "{offset_item.log}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  # GET /rootcheck/001?q=log=5.2 (perfect match)
  - name: Filters by composed query with a perfect match, no items returned
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        q: log=5.2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  # GET /rootcheck/001?q=log~5.2 (LIKE ~)
  - name: Filters by composed query with a LIKE operator
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        q: log~5.2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - status: "outstanding"
              log: "System Audit: CIS - Debian Linux - 5.2 - Network parameters - IP Forwarding enabled {{CIS: 5.2
              Debian Linux}}. File: /proc/sys/net/ipv4/ip_forward. Reference: https://benchmarks.cisecurity.org/tools2/linux/CIS_Debian_Benchmark_v1.0.pdf ."
              date_first: !anystr
              date_last: !anystr
              cis: "5.2 Debian Linux"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
      # Save some data for future use in the test
      save:
        json:
          returned_status: data.affected_items[0].status
          returned_log: data.affected_items[0].log
          returned_date_first: data.affected_items[0].date_first
          returned_date_last: data.affected_items[0].date_last
          returned_cis: data.affected_items[0].cis
          returned_total: data.total_affected_items

  # GET /rootcheck/001?q=log~5.2;cis=5.2 Debian Linux
  - name: Filters by composed query with LIKE and perfect match
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        q: log~5.2;cis=5.2 Debian Linux
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - status: "{returned_status}"
              log: "{returned_log}"
              date_first: "{returned_date_first}"
              date_last: "{returned_date_last}"
              cis: "{returned_cis}"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  # GET /rootcheck/001?q=(log=test,status=outstanding);cis=5.2 Debian Linux,pci_dss=2
  - name: Filters by composed query
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        q: (log=test,status=outstanding);cis=5.2 Debian Linux,pci_dss=2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - status: "{returned_status}"
              log: "{returned_log}"
              date_first: "{returned_date_first}"
              date_last: "{returned_date_last}"
              cis: "{returned_cis}"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  # GET /rootcheck/001?limit=1&search=/tmp
  - name: Get limited rootcheck scan results for agent 001 using search parameter
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        limit: 1
        search: /tmp
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *full_items_array
              log: "System Audit: CIS - Debian Linux - 1.4 - Robust partition scheme - /tmp is not on its own partition
               {{CIS: 1.4 Debian Linux}}. File: /etc/fstab. Reference: https://benchmarks.cisecurity.org/tools2/linux/CIS_Debian_Benchmark_v1.0.pdf ."
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  # GET /rootcheck/001?limit=1&search=random
  - name: Get limited rootcheck scan results for agent 001 using search parameter, no items returned
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        limit: 1
        search: random
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  # GET /rootcheck/001?cis=1.4 Debian Linux
  - name: Filter by CIS parameter
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        cis: 1.4 Debian Linux
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "cis"
            expected_values: "1.4 Debian Linux"

  # GET /rootcheck/001?cis=2.0 Debian Linux (does not exist)
  - name: Filter by CIS parameter, no items returned
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        cis: 2.0 Debian Linux
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  # GET /rootcheck/001?pci_dss=2 (does not exist)
  - name: Filter by PCI_DSS parameter, no items returned
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        pci_dss: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  # GET /rootcheck/001?status=outstanding
  - name: Filter by status parameter
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        status: outstanding
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "status"
            expected_values: "outstanding"

  # GET /rootcheck/001?status=all
  - name: Filter by status parameter using the all keyword
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        status: all
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "status"
            expected_values: "outstanding"

  # GET /rootcheck/001?status=solved (does not exist)
  - name: Filter by status parameter, no items returned
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        status: solved
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  # GET /rootcheck/001?select=log
  - name: Filter by select parameter
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        select: log
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'log'
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: []
          total_failed_items: 0

# GET /rootcheck/001?select=log,status,date_last
  - name: Filter by select parameter with more than a field
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        select: log,status,date_last
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'log,status,date_last'
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: []
          total_failed_items: 0

  # GET /rootcheck/001?select=cis&sort=+cis&distinct=true
  - name: Filter by select parameter and distinct, selecting cis and sorting by cis
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        select: cis
        sort: +cis
        distinct: true
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - cis: !anystr
            - cis: !anystr
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "cis"

  # GET /rootcheck/001?select=status,distinct=true
  - name: Filter by select parameter and distinct, selecting status
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        select: status
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: "status"
      json:
        error: 0
        data:
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  # GET /rootcheck/001?sort=+log
  - name: Sort response (ascending)
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        sort: +log
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "log"
      status_code: 200

  # GET /rootcheck/001?sort=-log
  - name: Sort response (descending)
    request:
      verify: False
      <<: *get_rootcheck_agent
      params:
        sort: -log
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "log"
            reverse: True

  # GET /rootcheck/012
  - name: Try to get 012 agent's rootcheck data (never connected)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/012"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        error: 2007

---
test_name: PUT /rootcheck

stages:

  # PUT /rootcheck?agents_list=002
  - name: Run rootcheck scan in agent 002
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "002"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - "002"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  # PUT /rootcheck
  - name: Run rootcheck scan in all agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - "000"
            - "001"
            - "002"
            - "003"
            - "004"
            - "005"
            - "006"
            - "007"
            - "008"
          failed_items: []
          total_affected_items: 9
          total_failed_items: 0

  # PUT /rootcheck
  - name: Try to run a rootcheck scan for non active agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '010,012'
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1707
              id:
                - "010"
                - "012"
          total_affected_items: 0
          total_failed_items: 2

  # PUT /rootcheck
  - name: Try to run rootcheck scan for non existing agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '040,050'
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1701
              id:
                - "040"
                - "050"
          total_affected_items: 0
          total_failed_items: 2
    delay_after: !float "{global_db_delay}"

---
test_name: DELETE /rootcheck/{agent_id}

stages:

  # GET /rootcheck/001
  - name: Check that agent 001 has data
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 5
          total_failed_items: 0
        message: !anystr

  # DELETE /rootcheck/001
  - name: Delete rootcheck scans in agent 001
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - "001"
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    delay_after: !float "{global_db_delay}"

  # GET /rootcheck/001
  - name: Check if data has been deleted in agent 001
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/rootcheck/001"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0
        message: !anystr
