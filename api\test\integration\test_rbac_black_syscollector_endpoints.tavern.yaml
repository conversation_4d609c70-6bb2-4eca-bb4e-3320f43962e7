---
test_name: GET OS SYSCOLLECTOR RBAC

stages:

  - name: Get os from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/os"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - architecture: !anystr
              hostname: !anystr
              os:
                codename: !anystr
                major: !anystr
                minor: !anystr
                name: !anystr
                platform: !anystr
                version: !anystr
              release: !anystr
              scan:
                id: !anyint
                time: !anystr
              sysname: !anystr
              version: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get os from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/os"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

---
test_name: GET HARDWARE SYSCOLLECTOR RBAC

stages:

  - name: Get hardware from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/hardware"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - cpu:
                cores: !anything
                mhz: !anything
                name: !anything
              ram:
                free: !anything
                total: !anything
                usage: !anything
              scan:
                id: !anything
                time: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get hardware from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/hardware"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: GET NETADDR SYSCOLLECTOR RBAC

stages:

  - name: Get netaddr from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/netaddr"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - address: !anystr
              broadcast: !anystr
              iface: !anystr
              netmask: !anystr
              proto: !anystr
              scan:
                id: !anyint
          total_affected_items: !anyint

  - name: Get netaddr from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/netaddr"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: GET NETIFACE SYSCOLLECTOR RBAC

stages:

  - name: Get netiface from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/netiface"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - mac: !anystr
              mtu: !anyint
              name: !anystr
              rx:
                bytes: !anyint
                dropped: !anyint
                errors: !anyint
                packets: !anyint
              scan:
                id: !anyint
                time: !anystr
              state: !anystr
              tx:
                bytes: !anyint
                dropped: !anyint
                errors: !anyint
                packets: !anyint
              type: !anystr
          total_affected_items: !anyint

  - name: Get netiface from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/netiface"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: GET NETPROTO SYSCOLLECTOR RBAC

stages:

  - name: Get netiface from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/004/netproto"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - dhcp: !anystr
              gateway: !anystr
              iface: !anystr
              scan:
                id: !anyint
              type: !anystr
          total_affected_items: !anyint

  - name: Get netiface from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/005/netproto"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: GET PACKAGES SYSCOLLECTOR RBAC

stages:

  - name: Get netiface from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/004/packages"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - architecture: !anystr
              description: !anystr
              format: !anystr
              name: !anystr
              priority: !anystr
              scan:
                id: !anyint
                time: !anystr
              section: !anystr
              size: !anyint
              vendor: !anystr
              version: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get netiface from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/005/packages"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: GET PORTS SYSCOLLECTOR RBAC

stages:

  - name: Get netiface from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/ports"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - inode: !anyint
              local:
                ip: !anystr
                port: !anyint
              protocol: !anystr
              remote:
                ip: !anystr
                port: !anyint
              rx_queue: !anyint
              scan:
                id: !anyint
                time: !anystr
              state: !anystr
              tx_queue: !anyint
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get netiface from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/006/ports"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: GET PROCESSES SYSCOLLECTOR RBAC

stages:

  - name: Get netiface from an agent (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/002/processes"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - egroup: !anystr
              euser: !anystr
              fgroup: !anystr
              name: !anystr
              nice: !anyint
              nlwp: !anyint
              pgrp: !anyint
              pid: !anystr
              ppid: !anyint
              priority: !anyint
              processor: !anyint
              resident: !anyint
              rgroup: !anystr
              ruser: !anystr
              scan:
                id: !anyint
                time: !anystr
              session: !anyint
              sgroup: !anystr
              share: !anyint
              size: !anyint
              start_time: !anyint
              state: !anystr
              stime: !anyint
              suser: !anystr
              tgid: !anyint
              tty: !anyint
              utime: !anyint
              vm_size: !anyint
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get netiface from an agent (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/006/processes"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

---
test_name: GET HOTFIXES SYSCOLLECTOR RBAC

stages:

  - name: Get all hotfixes from an agent (Deny)
    request: &hotfix_request_deny
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/syscollector/001/hotfixes"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

  - name: Filter hotfix (Deny)
    request:
      verify: False
      <<: *hotfix_request_deny
    response:
      <<: *permission_denied
