name: Windows upgrade test

on:
  pull_request:
    paths:
      - "src/**"
      - ".github/workflows/4_testcomponent_windows-installer-upgrade.yml"

jobs:
  build:
    runs-on: ubuntu-24.04
    steps:
      - name: Install mingw
        run: sudo apt install gcc-mingw-w64 g++-mingw-w64-i686 g++-mingw-w64-x86-64 nsis -y
      - uses: actions/checkout@v3
      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake
      - name: make deps
        run: make -C src deps TARGET=winagent -j2
      - name: make
        run: make -C src TARGET=winagent -j2
      - name: Avoid signing files and pause
        run: |
          sed -i 's+signtool+::signtool+g' src/win32/wazuh-installer-build-msi.bat
          sed -i 's+pause+::pause+g' src/win32/wazuh-installer-build-msi.bat
      - name: Removing not required files
        run: rm -rf src/external/* .git/
      - name: Compress folder
        run: 7z a win-agent-base.zip ./*
      - name: Upload base branch folder
        uses: actions/upload-artifact@v4
        with:
          name: win-agent-base.zip
          path: win-agent-base.zip

  check_upgrade_result:
    strategy:
          matrix:
              wazuh_version: [4.5.2-1]
    runs-on: windows-latest
    needs: build
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version-file: ".github/workflows/.python-version"
          architecture: x64
      - name: Add WiX toolkit to PATH
        shell: bash
        run: echo "${WIX}bin" >> $GITHUB_PATH
      - name: Install dependencies
        run: |
          pip install -r src/win32/qa/requirements.txt
      - name: Download released .msi
        run: |
          mkdir C:\win-agent-released
          python -m wget https://packages.wazuh.com/4.x/windows/wazuh-agent-${{ matrix.wazuh_version }}.msi -o C:\win-agent-released
      - name: Download base folder
        uses: actions/download-artifact@v4
        with:
          name: win-agent-base.zip
          path: C:\win-agent-base
      - name: Unzip base folder
        run: 7z x C:\win-agent-base\win-agent-base.zip -oC:\win-agent-base
      - name: Create .msi package
        run: |
          cd C:\win-agent-base\src\win32
          $VERSION_FILE = "..\..\VERSION.json"
          if (Test-Path $VERSION_FILE) {
              $json = Get-Content $VERSION_FILE | ConvertFrom-Json
              $VERSION = $json.version
          } else {
              Write-Host "Error: VERSION.json not found."
              exit 1
          }
          .\wazuh-installer-build-msi.bat $VERSION 0
          cp *.msi C:\win-agent-base\
      - name: Run tests
        run: |
          cd src/win32/qa
          python -m pytest -vv ./test_win_upgrade.py
      - name: Upload release installation log
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: release_log
          path: C:\win-agent-released\win-agent-released.log
      - name: Upload base installation log
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: base_log
          path: C:\win-agent-base\win-agent-base.log
