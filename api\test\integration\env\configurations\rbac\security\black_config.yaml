---
- actions:
  - security:read
  resources:
  - user:id:100 # administrator
  - user:id:101 # normal
  - user:id:103 # python
  - user:id:105 # guest
  - role:id:*
  - rule:id:103
  effect: deny

- actions:
  - security:read
  resources:
  - policy:id:*
  effect: allow

- actions:
  - security:update
  resources:
  - user:id:*
  effect: allow

- actions:
  - security:update
  resources:
  - role:id:103
  - role:id:102
  - role:id:105
  - policy:id:107
  - policy:id:102
  - policy:id:104
  - rule:id:104
  effect: deny

- actions:
  - security:update
  resources:
  - policy:id:107
  - policy:id:102
  effect: allow

- actions:
  - security:delete
  resources:
  - user:id:105 # guest
  - user:id:101 # normal
  - user:id:2 # wazuh-wui
  - user:id:99 # testing
  - policy:id:*
  - rule:id:105
  effect: deny

- actions:
  - security:delete
  resources:
  - policy:id:*
  - role:id:*
  effect: allow

- actions:
  - security:create_user
  resources:
  - "*:*:*"
  effect: allow

- actions:
  - security:create
  - security:read_config
  - security:update_config
  - security:edit_run_as
  resources:
  - "*:*:*"
  effect: deny
