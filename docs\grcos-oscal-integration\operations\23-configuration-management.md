# GRCOS OSCAL Configuration Management

## Overview

This document defines comprehensive configuration management procedures for OSCAL integration within the GRCOS platform. It covers environment-specific configurations, automated deployment pipelines, configuration validation, and change management processes for maintaining consistent OSCAL implementations across development, staging, and production environments.

## Configuration Architecture

### Environment-Specific Configuration

#### Configuration Hierarchy
```
Global Configuration
├── Environment-Specific (dev/staging/prod)
│   ├── OSCAL Service Configuration
│   ├── Database Configuration  
│   ├── Blockchain Network Configuration
│   ├── AI Agent Configuration
│   └── Security Configuration
├── Module-Specific Configuration
│   ├── Assets Module Config
│   ├── Frameworks Module Config
│   ├── Controls Module Config
│   └── Assessments Module Config
└── Integration Configuration
    ├── External System Integrations
    ├── API Gateway Configuration
    └── Monitoring Configuration
```

#### Configuration File Structure
```yaml
# config/environments/production.yaml
environment:
  name: "production"
  classification: "production"
  
oscal:
  version: "1.1.3"
  validation:
    enabled: true
    strict_mode: true
    schema_path: "/etc/oscal/schemas"
  
  storage:
    primary_database:
      type: "mongodb"
      connection_string: "${MONGODB_PROD_URI}"
      database: "grcos_oscal_prod"
      ssl_enabled: true
      auth_source: "admin"
    
    cache:
      type: "redis"
      connection_string: "${REDIS_PROD_URI}"
      database: 0
      ssl_enabled: true
      key_prefix: "grcos:oscal:prod:"
      ttl_default: 3600
  
  blockchain:
    network: "grcos-production-network"
    channel: "compliance-channel"
    chaincode: "oscal-compliance"
    peer_endpoints:
      - "peer0.grcos.org:7051"
      - "peer1.grcos.org:7051" 
      - "peer2.grcos.org:7051"
    orderer_endpoints:
      - "orderer0.grcos.org:7050"
    tls:
      enabled: true
      cert_path: "/etc/hyperledger/tls/peer.crt"
      key_path: "/etc/hyperledger/tls/peer.key"
      ca_cert_path: "/etc/hyperledger/tls/ca.crt"

agents:
  system_agent:
    enabled: true
    log_level: "INFO"
    max_concurrent_tasks: 10
    task_timeout: "30m"
    
  compliance_agent:
    enabled: true
    framework_analysis:
      enabled: true
      cache_results: true
      analysis_timeout: "15m"
    gap_analysis:
      enabled: true
      confidence_threshold: 0.8
      
  assessment_agent:
    enabled: true
    automated_testing:
      enabled: true
      max_parallel_tests: 5
      test_timeout: "1h"
    vulnerability_scanning:
      enabled: true
      scanner_endpoints:
        - "https://openvas.internal:9392"
      scan_schedules:
        - cron: "0 2 * * 0"  # Weekly Sunday 2 AM
          
security:
  encryption:
    algorithm: "AES-256-GCM"
    key_rotation_interval: "30d"
    key_management_service: "vault"
    
  authentication:
    jwt:
      issuer: "grcos-auth-service"
      audience: "grcos-api"
      expiration: "24h"
      algorithm: "RS256"
      
  authorization:
    rbac:
      enabled: true
      policy_engine: "opa"
      policy_refresh_interval: "5m"

monitoring:
  metrics:
    enabled: true
    prometheus_endpoint: "http://prometheus:9090"
    collection_interval: "30s"
    
  logging:
    level: "INFO"
    format: "json"
    output: "stdout"
    audit_enabled: true
    
  alerting:
    enabled: true
    alert_manager_endpoint: "http://alertmanager:9093"
    notification_channels:
      - type: "slack"
        webhook_url: "${SLACK_WEBHOOK_URL}"
      - type: "email"
        smtp_server: "${SMTP_SERVER}"
```

### Configuration Management Tools

#### Configuration Validation Framework
```python
class OSCALConfigurationValidator:
    """
    Validate OSCAL configuration across environments
    """
    
    def __init__(self, config_schema_path):
        self.config_schema = self._load_config_schema(config_schema_path)
        self.validation_rules = self._load_validation_rules()
    
    def validate_configuration(self, config_file_path, environment):
        """
        Validate configuration file against schema and business rules
        """
        validation_result = {
            "config_file": config_file_path,
            "environment": environment,
            "validation_timestamp": datetime.utcnow(),
            "schema_validation": {"passed": False, "errors": []},
            "business_rule_validation": {"passed": False, "errors": []},
            "security_validation": {"passed": False, "errors": []},
            "overall_status": "failed"
        }
        
        try:
            # Load configuration
            config = self._load_configuration(config_file_path)
            
            # Schema validation
            schema_result = self._validate_against_schema(config)
            validation_result["schema_validation"] = schema_result
            
            # Business rule validation
            business_result = self._validate_business_rules(config, environment)
            validation_result["business_rule_validation"] = business_result
            
            # Security validation
            security_result = self._validate_security_config(config, environment)
            validation_result["security_validation"] = security_result
            
            # Overall status
            all_passed = (
                schema_result["passed"] and 
                business_result["passed"] and 
                security_result["passed"]
            )
            validation_result["overall_status"] = "passed" if all_passed else "failed"
            
        except Exception as e:
            validation_result["validation_error"] = str(e)
        
        return validation_result
    
    def _validate_business_rules(self, config, environment):
        """
        Validate business-specific configuration rules
        """
        errors = []
        
        # Production environment specific rules
        if environment == "production":
            # SSL must be enabled in production
            if not config.get("oscal", {}).get("storage", {}).get("primary_database", {}).get("ssl_enabled"):
                errors.append("SSL must be enabled for database connections in production")
            
            # Blockchain TLS must be enabled
            if not config.get("oscal", {}).get("blockchain", {}).get("tls", {}).get("enabled"):
                errors.append("TLS must be enabled for blockchain connections in production")
            
            # Audit logging must be enabled
            if not config.get("monitoring", {}).get("logging", {}).get("audit_enabled"):
                errors.append("Audit logging must be enabled in production")
        
        # OSCAL version compatibility
        oscal_version = config.get("oscal", {}).get("version")
        if oscal_version != "1.1.3":
            errors.append(f"OSCAL version {oscal_version} not supported. Use 1.1.3")
        
        # Agent configuration validation
        agents_config = config.get("agents", {})
        if agents_config.get("system_agent", {}).get("enabled") and not agents_config.get("compliance_agent", {}).get("enabled"):
            errors.append("Compliance agent must be enabled when system agent is enabled")
        
        return {
            "passed": len(errors) == 0,
            "errors": errors
        }
    
    def _validate_security_config(self, config, environment):
        """
        Validate security-related configuration
        """
        errors = []
        
        # Encryption configuration
        encryption_config = config.get("security", {}).get("encryption", {})
        if encryption_config.get("algorithm") not in ["AES-256-GCM", "AES-256-CBC"]:
            errors.append("Invalid encryption algorithm. Use AES-256-GCM or AES-256-CBC")
        
        # JWT configuration
        jwt_config = config.get("security", {}).get("authentication", {}).get("jwt", {})
        if jwt_config.get("algorithm") not in ["RS256", "ES256"]:
            errors.append("JWT algorithm must be RS256 or ES256 for production security")
        
        # Key rotation interval
        rotation_interval = encryption_config.get("key_rotation_interval", "")
        if not self._is_valid_duration(rotation_interval) or self._duration_to_days(rotation_interval) > 90:
            errors.append("Key rotation interval must be 90 days or less")
        
        return {
            "passed": len(errors) == 0,
            "errors": errors
        }
```

## Automated Configuration Deployment

### GitOps Configuration Pipeline

#### Configuration Deployment Workflow
```yaml
# .github/workflows/config-deployment.yml
name: GRCOS Configuration Deployment

on:
  push:
    branches: [main, staging, development]
    paths: ['config/**']
  pull_request:
    paths: ['config/**']

jobs:
  validate-configuration:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Install dependencies
        run: |
          pip install -r requirements-config.txt
          
      - name: Validate configuration files
        run: |
          python scripts/validate-config.py --environment ${{ github.ref_name }}
          
      - name: Security scan configuration
        run: |
          python scripts/security-scan-config.py --config-dir config/
          
      - name: Generate configuration diff
        if: github.event_name == 'pull_request'
        run: |
          python scripts/config-diff.py --base main --head ${{ github.head_ref }}

  deploy-configuration:
    needs: validate-configuration
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging' || github.ref == 'refs/heads/development'
    runs-on: ubuntu-latest
    environment: ${{ github.ref_name }}
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        
      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBECONFIG }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig
          
      - name: Deploy configuration
        run: |
          ./scripts/deploy-config.sh ${{ github.ref_name }}
          
      - name: Verify deployment
        run: |
          ./scripts/verify-config-deployment.sh ${{ github.ref_name }}
          
      - name: Run configuration tests
        run: |
          python scripts/test-config-deployment.py --environment ${{ github.ref_name }}
```

#### Configuration Deployment Script
```bash
#!/bin/bash
# scripts/deploy-config.sh

set -euo pipefail

ENVIRONMENT=$1
CONFIG_DIR="config/environments"
NAMESPACE="grcos-${ENVIRONMENT}"

echo "Deploying GRCOS configuration for environment: ${ENVIRONMENT}"

# Validate environment
if [[ ! -f "${CONFIG_DIR}/${ENVIRONMENT}.yaml" ]]; then
    echo "Error: Configuration file for environment ${ENVIRONMENT} not found"
    exit 1
fi

# Create namespace if it doesn't exist
kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -

# Create configuration ConfigMap
kubectl create configmap grcos-config \
    --from-file=${CONFIG_DIR}/${ENVIRONMENT}.yaml \
    --namespace=${NAMESPACE} \
    --dry-run=client -o yaml | kubectl apply -f -

# Create secrets from environment variables
kubectl create secret generic grcos-secrets \
    --from-literal=mongodb-uri="${MONGODB_URI}" \
    --from-literal=redis-uri="${REDIS_URI}" \
    --from-literal=jwt-secret="${JWT_SECRET}" \
    --from-literal=encryption-key="${ENCRYPTION_KEY}" \
    --namespace=${NAMESPACE} \
    --dry-run=client -o yaml | kubectl apply -f -

# Deploy blockchain certificates
kubectl create secret generic blockchain-certs \
    --from-file=peer.crt="${BLOCKCHAIN_PEER_CERT}" \
    --from-file=peer.key="${BLOCKCHAIN_PEER_KEY}" \
    --from-file=ca.crt="${BLOCKCHAIN_CA_CERT}" \
    --namespace=${NAMESPACE} \
    --dry-run=client -o yaml | kubectl apply -f -

# Apply OSCAL service configuration
envsubst < k8s/oscal-service-${ENVIRONMENT}.yaml | kubectl apply -f -

# Apply agent configurations
for agent in system compliance assessment workflow remediation reporting; do
    envsubst < k8s/agents/${agent}-agent-${ENVIRONMENT}.yaml | kubectl apply -f -
done

# Wait for deployments to be ready
kubectl wait --for=condition=available --timeout=300s deployment/grcos-oscal-service -n ${NAMESPACE}
kubectl wait --for=condition=available --timeout=300s deployment/grcos-ai-agents -n ${NAMESPACE}

echo "Configuration deployment completed successfully"
```

### Configuration Drift Detection

#### Configuration Monitoring Service
```python
class ConfigurationDriftDetector:
    """
    Detect configuration drift in GRCOS OSCAL deployments
    """
    
    def __init__(self, kubernetes_client, config_repository):
        self.k8s_client = kubernetes_client
        self.config_repo = config_repository
        self.drift_alerts = []
    
    def detect_configuration_drift(self, environment):
        """
        Detect drift between expected and actual configuration
        """
        drift_report = {
            "environment": environment,
            "scan_timestamp": datetime.utcnow(),
            "drift_detected": False,
            "drift_items": [],
            "recommendations": []
        }
        
        # Get expected configuration
        expected_config = self.config_repo.get_environment_config(environment)
        
        # Get actual configuration from Kubernetes
        actual_config = self._get_actual_configuration(environment)
        
        # Compare configurations
        drift_items = self._compare_configurations(expected_config, actual_config)
        
        if drift_items:
            drift_report["drift_detected"] = True
            drift_report["drift_items"] = drift_items
            drift_report["recommendations"] = self._generate_drift_recommendations(drift_items)
            
            # Send alerts
            self._send_drift_alerts(drift_report)
        
        return drift_report
    
    def _compare_configurations(self, expected, actual):
        """
        Compare expected vs actual configuration
        """
        drift_items = []
        
        # Check ConfigMaps
        expected_configmaps = expected.get("configmaps", {})
        actual_configmaps = actual.get("configmaps", {})
        
        for name, expected_data in expected_configmaps.items():
            if name not in actual_configmaps:
                drift_items.append({
                    "type": "missing_configmap",
                    "resource": name,
                    "description": f"ConfigMap {name} is missing"
                })
            else:
                actual_data = actual_configmaps[name]
                data_diff = self._compare_data(expected_data, actual_data)
                if data_diff:
                    drift_items.append({
                        "type": "configmap_drift",
                        "resource": name,
                        "differences": data_diff
                    })
        
        # Check Secrets
        expected_secrets = expected.get("secrets", {})
        actual_secrets = actual.get("secrets", {})
        
        for name, expected_keys in expected_secrets.items():
            if name not in actual_secrets:
                drift_items.append({
                    "type": "missing_secret",
                    "resource": name,
                    "description": f"Secret {name} is missing"
                })
            else:
                actual_keys = actual_secrets[name]
                missing_keys = set(expected_keys) - set(actual_keys)
                if missing_keys:
                    drift_items.append({
                        "type": "secret_drift",
                        "resource": name,
                        "missing_keys": list(missing_keys)
                    })
        
        # Check Deployments
        deployment_drift = self._check_deployment_drift(expected, actual)
        drift_items.extend(deployment_drift)
        
        return drift_items
    
    def _check_deployment_drift(self, expected, actual):
        """
        Check for deployment configuration drift
        """
        drift_items = []
        
        expected_deployments = expected.get("deployments", {})
        actual_deployments = actual.get("deployments", {})
        
        for name, expected_spec in expected_deployments.items():
            if name not in actual_deployments:
                drift_items.append({
                    "type": "missing_deployment",
                    "resource": name,
                    "description": f"Deployment {name} is missing"
                })
                continue
            
            actual_spec = actual_deployments[name]
            
            # Check replica count
            expected_replicas = expected_spec.get("replicas", 1)
            actual_replicas = actual_spec.get("replicas", 1)
            if expected_replicas != actual_replicas:
                drift_items.append({
                    "type": "replica_drift",
                    "resource": name,
                    "expected": expected_replicas,
                    "actual": actual_replicas
                })
            
            # Check image versions
            expected_image = expected_spec.get("image")
            actual_image = actual_spec.get("image")
            if expected_image != actual_image:
                drift_items.append({
                    "type": "image_drift",
                    "resource": name,
                    "expected": expected_image,
                    "actual": actual_image
                })
            
            # Check resource limits
            expected_resources = expected_spec.get("resources", {})
            actual_resources = actual_spec.get("resources", {})
            if expected_resources != actual_resources:
                drift_items.append({
                    "type": "resource_drift",
                    "resource": name,
                    "expected": expected_resources,
                    "actual": actual_resources
                })
        
        return drift_items
```

## Configuration Change Management

### Change Approval Workflow

#### Configuration Change Request Process
```python
class ConfigurationChangeManager:
    """
    Manage configuration change requests and approvals
    """
    
    def __init__(self, workflow_engine, approval_service):
        self.workflow_engine = workflow_engine
        self.approval_service = approval_service
        self.change_templates = self._load_change_templates()
    
    def submit_configuration_change_request(self, change_request):
        """
        Submit configuration change request for approval
        """
        # Validate change request
        validation_result = self._validate_change_request(change_request)
        if not validation_result["valid"]:
            raise ValueError(f"Invalid change request: {validation_result['errors']}")
        
        # Assess change impact
        impact_assessment = self._assess_change_impact(change_request)
        
        # Determine approval workflow
        approval_workflow = self._determine_approval_workflow(change_request, impact_assessment)
        
        # Create change record
        change_record = {
            "change_id": str(uuid.uuid4()),
            "submitted_by": change_request["submitted_by"],
            "submission_time": datetime.utcnow(),
            "change_type": change_request["change_type"],
            "target_environment": change_request["target_environment"],
            "description": change_request["description"],
            "configuration_changes": change_request["configuration_changes"],
            "impact_assessment": impact_assessment,
            "approval_workflow": approval_workflow,
            "status": "pending_approval",
            "approvals": []
        }
        
        # Start approval workflow
        workflow_instance = self.workflow_engine.start_workflow(
            workflow_id=approval_workflow["workflow_id"],
            variables={
                "change_record": change_record,
                "required_approvers": approval_workflow["required_approvers"]
            }
        )
        
        change_record["workflow_instance_id"] = workflow_instance["id"]
        
        # Store change record
        self._store_change_record(change_record)
        
        return change_record
    
    def _assess_change_impact(self, change_request):
        """
        Assess the impact of proposed configuration changes
        """
        impact_assessment = {
            "risk_level": "low",
            "affected_services": [],
            "downtime_required": False,
            "rollback_complexity": "simple",
            "testing_requirements": [],
            "approval_requirements": []
        }
        
        target_env = change_request["target_environment"]
        changes = change_request["configuration_changes"]
        
        # Assess risk level
        if target_env == "production":
            impact_assessment["risk_level"] = "high"
            impact_assessment["approval_requirements"].append("security_team")
            impact_assessment["approval_requirements"].append("operations_team")
        
        # Check for high-impact changes
        high_impact_patterns = [
            "blockchain.network",
            "security.encryption",
            "database.connection_string",
            "agents.*.enabled"
        ]
        
        for pattern in high_impact_patterns:
            if self._change_matches_pattern(changes, pattern):
                impact_assessment["risk_level"] = "high"
                impact_assessment["testing_requirements"].append("integration_testing")
                break
        
        # Assess affected services
        service_impact_map = {
            "oscal.storage": ["grcos-oscal-service", "grcos-ai-agents"],
            "blockchain": ["grcos-oscal-service", "blockchain-peers"],
            "agents": ["grcos-ai-agents"],
            "security": ["all-services"]
        }
        
        for change_path in changes.keys():
            for service_pattern, affected_services in service_impact_map.items():
                if change_path.startswith(service_pattern):
                    impact_assessment["affected_services"].extend(affected_services)
        
        # Remove duplicates
        impact_assessment["affected_services"] = list(set(impact_assessment["affected_services"]))
        
        return impact_assessment
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Configuration Management Team
