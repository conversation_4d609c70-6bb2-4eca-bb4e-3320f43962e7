---
test_name: GET /ciscat/agent_id/results

stages:

  - name: Get ciscat result for agent 001 (allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/ciscat/001/results"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: !anything
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get ciscat result for agent 002 (allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/ciscat/002/results"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: !anything
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get ciscat result for agent 003 (denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/ciscat/003/results"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr


