---
test_name: GET RULES RBAC

stages:

  - name: Try to show the rules of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 5
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - &full_item_rules
              description: !anystr
              details: !anything
              filename: 0010-rules_config.xml
              gdpr: !anything
              gpg13: !anything
              groups: !anything
              hipaa: !anything
              id: !anyint
              level: !anyint
              mitre: !anything
              nist_800_53: !anything
              tsc: !anything
              relative_dirname: !anystr
              pci_dss: !anything
              status: !anystr
            - <<: *full_item_rules
            - <<: *full_item_rules
            - <<: *full_item_rules
            - <<: *full_item_rules
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the rules of the system (try q parameter to bypass a denied resource)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        q: 'filename=0020-syslog_rules.xml'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the rules of the system (list)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        rule_ids: 1,2,200,500,700,702
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *full_item_rules
              id: 1
            - <<: *full_item_rules
              id: 2
            - <<: *full_item_rules
              filename: 0015-ossec_rules.xml
              id: 500
            - <<: *full_item_rules
              filename: 0015-ossec_rules.xml
              id: 700
          failed_items:
            - error:
                code: 1208
              id:
                - 200
                - 702
          total_affected_items: 4
          total_failed_items: 2
        
---
test_name: GET RULES FILES RBAC

stages:

  - name: Try to show the rules files of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - &full_item_rules_files
              filename: 0010-rules_config.xml
              relative_dirname: ruleset/rules
              status: enabled
            - <<: *full_item_rules_files
              filename: 0015-ossec_rules.xml
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the rules files of the system (list)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        filename: 0015-ossec_rules.xml,0010-rules_config.xml
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *full_item_rules_files
              filename: 0010-rules_config.xml
            - <<: *full_item_rules_files
              filename: 0015-ossec_rules.xml
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
  - name: Try to show the rules files of the system (no permissions)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        filename: 0011-ossec_rules.xml,0011-rules_config.xml
    response: &permission_denied
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

---
test_name: GET RULES FILES RBAC (CONTENT)

stages:

  - name: Get one rule file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/0015-ossec_rules.xml"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        raw: True
    response:
      status_code: 200
      headers:
        content-type: 'application/xml; charset=utf-8'

  - name: Try to get one rule file (no permissions)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/0011-ossec_rules.xml"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: PUT RULES FILES RBAC

stages:

  - name: Update one rule file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      data: "{new_rules:s}"
    response:
      status_code: 200


  - name: Try to update the same file without delete permissions (overwrite)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        overwrite: True
      data: "{new_rules:s}"
    response:
      json:
        data:
          affected_items: []
          total_affected_items: 0
          failed_items:
            - error:
                code: 4000
          total_failed_items: 1
        error: 1

---
test_name: DELETE RULES FILES RBAC

stages:

  - name: Delete one rule file
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/local_rules.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

  - name: Try to delete one rule file that does not exist
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/not_exist.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET RULES GROUPS RBAC

stages:

  - name: Try to show the groups of rules in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/groups"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
---
test_name: GET pci_dss REQUIREMENT RBAC

stages:

  - name: Try to show the groups of rules in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/pci_dss"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
        
---
test_name: GET MITRE TECHNIQUE IDS RBAC

stages:

  - name: Try to show the groups of rules in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/mitre"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
