# Controls Module Wazuh Integration

## Overview

The Controls Module integration with Wazuh SIEM/XDR enables real-time monitoring and validation of security control effectiveness. This integration transforms static control frameworks into dynamic, continuously monitored security controls with automated evidence collection and compliance validation.

## Integration Architecture

### Security Control Monitoring Flow

```mermaid
graph TB
    subgraph "Wazuh Security Events"
        A[Authentication Events]
        B[Access Control Events]
        C[System Events]
        D[Network Events]
        E[File Integrity Events]
    end
    
    subgraph "GRCOS Controls Module"
        F[Control Mapper]
        G[Evidence Collector]
        H[Effectiveness Monitor]
        I[Compliance Validator]
    end
    
    subgraph "OSCAL Framework"
        J[Control Catalog]
        K[Control Implementation]
        L[Assessment Results]
        M[POA&M Items]
    end
    
    subgraph "Control Database"
        N[Control Registry]
        O[Evidence Store]
        P[Assessment History]
        Q[Compliance Status]
    end
    
    A --> F
    B --> F
    C --> F
    D --> F
    E --> F
    
    F --> G
    G --> H
    H --> I
    
    F --> J
    G --> K
    H --> L
    I --> M
    
    G --> N
    H --> O
    I --> P
    I --> Q
```

### Core Integration Components

#### 1. Real-time Control Monitoring
- **Event-to-Control Mapping**: Automatic mapping of security events to OSCAL controls
- **Continuous Assessment**: Real-time evaluation of control effectiveness
- **Evidence Collection**: Automated gathering of control implementation evidence
- **Compliance Validation**: Continuous validation against compliance frameworks

#### 2. Control Effectiveness Analysis
- **Performance Metrics**: Real-time calculation of control effectiveness metrics
- **Trend Analysis**: Historical analysis of control performance
- **Gap Identification**: Automated identification of control gaps and weaknesses
- **Risk Assessment**: Dynamic risk assessment based on control status

#### 3. Automated Compliance Reporting
- **OSCAL Assessment Results**: Automatic generation of OSCAL-compliant assessment results
- **Compliance Dashboards**: Real-time compliance status visualization
- **Audit Evidence**: Automated collection and organization of audit evidence
- **Regulatory Reporting**: Automated generation of regulatory compliance reports

## Control Mapping Implementation

### OSCAL Control Mapper

```python
import asyncio
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from datetime import datetime
import json

@dataclass
class ControlMapping:
    control_id: str
    control_title: str
    wazuh_rules: List[str]
    event_types: List[str]
    compliance_frameworks: List[str]
    effectiveness_metrics: Dict[str, Any]
    evidence_requirements: List[str]

class OSCALControlMapper:
    def __init__(self, oscal_client, wazuh_client):
        self.oscal_client = oscal_client
        self.wazuh_client = wazuh_client
        self.control_mappings = {}
        self.mapping_cache = {}
        self.load_control_mappings()
    
    def load_control_mappings(self):
        """Load OSCAL control to Wazuh rule mappings"""
        # NIST 800-53 Control Mappings
        self.control_mappings = {
            'ac-2': ControlMapping(
                control_id='ac-2',
                control_title='Account Management',
                wazuh_rules=['5501', '5502', '5503', '5551', '5552'],
                event_types=['user_account', 'authentication'],
                compliance_frameworks=['NIST_800_53', 'PCI_DSS', 'SOX'],
                effectiveness_metrics={
                    'account_creation_monitoring': 'percentage',
                    'account_modification_detection': 'percentage',
                    'unauthorized_account_detection': 'count'
                },
                evidence_requirements=[
                    'account_creation_logs',
                    'account_modification_logs',
                    'access_review_records'
                ]
            ),
            'ac-3': ControlMapping(
                control_id='ac-3',
                control_title='Access Enforcement',
                wazuh_rules=['5710', '5711', '5712', '5720'],
                event_types=['access_control', 'authorization'],
                compliance_frameworks=['NIST_800_53', 'PCI_DSS', 'HIPAA'],
                effectiveness_metrics={
                    'unauthorized_access_attempts': 'count',
                    'access_denial_rate': 'percentage',
                    'privilege_escalation_detection': 'count'
                },
                evidence_requirements=[
                    'access_attempt_logs',
                    'authorization_decisions',
                    'privilege_usage_logs'
                ]
            ),
            'ac-7': ControlMapping(
                control_id='ac-7',
                control_title='Unsuccessful Logon Attempts',
                wazuh_rules=['5712', '5713', '5714', '5715'],
                event_types=['authentication_failure', 'brute_force'],
                compliance_frameworks=['NIST_800_53', 'PCI_DSS'],
                effectiveness_metrics={
                    'failed_login_detection': 'percentage',
                    'account_lockout_effectiveness': 'percentage',
                    'brute_force_detection': 'count'
                },
                evidence_requirements=[
                    'failed_authentication_logs',
                    'account_lockout_records',
                    'brute_force_alerts'
                ]
            ),
            'au-2': ControlMapping(
                control_id='au-2',
                control_title='Audit Events',
                wazuh_rules=['1002', '1003', '2501', '2502'],
                event_types=['audit', 'logging'],
                compliance_frameworks=['NIST_800_53', 'PCI_DSS', 'SOX'],
                effectiveness_metrics={
                    'audit_coverage': 'percentage',
                    'log_completeness': 'percentage',
                    'audit_event_detection': 'count'
                },
                evidence_requirements=[
                    'audit_configuration',
                    'log_generation_records',
                    'audit_event_samples'
                ]
            ),
            'si-4': ControlMapping(
                control_id='si-4',
                control_title='Information System Monitoring',
                wazuh_rules=['1001', '1002', '40101', '40102'],
                event_types=['system_monitoring', 'intrusion_detection'],
                compliance_frameworks=['NIST_800_53', 'PCI_DSS'],
                effectiveness_metrics={
                    'monitoring_coverage': 'percentage',
                    'threat_detection_rate': 'percentage',
                    'false_positive_rate': 'percentage'
                },
                evidence_requirements=[
                    'monitoring_configuration',
                    'detection_alerts',
                    'system_activity_logs'
                ]
            )
        }
    
    async def map_event_to_controls(self, security_event: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Map Wazuh security event to OSCAL controls"""
        mapped_controls = []
        
        rule_id = str(security_event.get('rule', {}).get('id', ''))
        event_groups = security_event.get('rule', {}).get('groups', [])
        
        # Direct rule mapping
        for control_id, mapping in self.control_mappings.items():
            if rule_id in mapping.wazuh_rules:
                control_assessment = await self._assess_control_from_event(
                    mapping, security_event
                )
                mapped_controls.append(control_assessment)
        
        # Group-based mapping
        for control_id, mapping in self.control_mappings.items():
            if any(event_type in event_groups for event_type in mapping.event_types):
                control_assessment = await self._assess_control_from_event(
                    mapping, security_event
                )
                if control_assessment not in mapped_controls:
                    mapped_controls.append(control_assessment)
        
        return mapped_controls
    
    async def _assess_control_from_event(self, mapping: ControlMapping, 
                                       event: Dict[str, Any]) -> Dict[str, Any]:
        """Assess control effectiveness based on security event"""
        assessment = {
            'control_id': mapping.control_id,
            'control_title': mapping.control_title,
            'assessment_timestamp': datetime.utcnow().isoformat(),
            'event_id': event.get('id', ''),
            'assessment_method': 'automated_monitoring',
            'implementation_status': 'implemented',
            'effectiveness_status': 'effective',
            'findings': [],
            'evidence': [],
            'metrics': {}
        }
        
        # Analyze event for control effectiveness
        rule_level = event.get('rule', {}).get('level', 0)
        
        if rule_level >= 10:  # High severity event
            assessment['findings'].append({
                'finding_id': f"finding-{event.get('id', '')}",
                'finding_type': 'non_compliance',
                'severity': 'high',
                'description': f"High severity security event detected: {event.get('rule', {}).get('description', '')}",
                'remediation_required': True
            })
            assessment['effectiveness_status'] = 'partially_effective'
        
        elif rule_level >= 7:  # Medium severity event
            assessment['findings'].append({
                'finding_id': f"finding-{event.get('id', '')}",
                'finding_type': 'weakness',
                'severity': 'medium',
                'description': f"Security event requiring attention: {event.get('rule', {}).get('description', '')}",
                'remediation_required': False
            })
        
        # Collect evidence
        assessment['evidence'] = [
            {
                'evidence_type': 'security_event_log',
                'evidence_id': event.get('id', ''),
                'timestamp': event.get('timestamp', ''),
                'source': 'wazuh_siem',
                'description': 'Security event captured by Wazuh SIEM',
                'content': json.dumps(event, indent=2)
            }
        ]
        
        return assessment
```

### Control Effectiveness Monitor

```python
class ControlEffectivenessMonitor:
    def __init__(self, control_mapper, metrics_calculator, evidence_collector):
        self.control_mapper = control_mapper
        self.metrics_calculator = metrics_calculator
        self.evidence_collector = evidence_collector
        self.effectiveness_cache = {}
        self.monitoring_rules = self._load_monitoring_rules()
    
    async def monitor_control_effectiveness(self, control_id: str, 
                                          time_period: str = '24h') -> Dict[str, Any]:
        """Monitor effectiveness of a specific control over time period"""
        effectiveness_report = {
            'control_id': control_id,
            'monitoring_period': time_period,
            'assessment_timestamp': datetime.utcnow().isoformat(),
            'overall_effectiveness': 'unknown',
            'effectiveness_score': 0.0,
            'metrics': {},
            'trends': {},
            'findings': [],
            'recommendations': []
        }
        
        # Get control mapping
        control_mapping = self.control_mapper.control_mappings.get(control_id)
        if not control_mapping:
            effectiveness_report['findings'].append({
                'type': 'configuration_error',
                'message': f'No mapping found for control {control_id}'
            })
            return effectiveness_report
        
        # Calculate effectiveness metrics
        metrics = await self._calculate_effectiveness_metrics(
            control_mapping, time_period
        )
        effectiveness_report['metrics'] = metrics
        
        # Analyze trends
        trends = await self._analyze_effectiveness_trends(
            control_id, time_period
        )
        effectiveness_report['trends'] = trends
        
        # Calculate overall effectiveness score
        effectiveness_score = self._calculate_overall_effectiveness_score(metrics)
        effectiveness_report['effectiveness_score'] = effectiveness_score
        
        # Determine effectiveness status
        if effectiveness_score >= 0.9:
            effectiveness_report['overall_effectiveness'] = 'highly_effective'
        elif effectiveness_score >= 0.7:
            effectiveness_report['overall_effectiveness'] = 'effective'
        elif effectiveness_score >= 0.5:
            effectiveness_report['overall_effectiveness'] = 'partially_effective'
        else:
            effectiveness_report['overall_effectiveness'] = 'ineffective'
        
        # Generate findings and recommendations
        findings = await self._generate_effectiveness_findings(
            control_mapping, metrics, trends
        )
        effectiveness_report['findings'] = findings
        
        recommendations = await self._generate_recommendations(
            control_mapping, effectiveness_report
        )
        effectiveness_report['recommendations'] = recommendations
        
        return effectiveness_report
    
    async def _calculate_effectiveness_metrics(self, mapping: ControlMapping, 
                                             time_period: str) -> Dict[str, Any]:
        """Calculate effectiveness metrics for a control"""
        metrics = {}
        
        # Get events related to this control
        control_events = await self._get_control_events(mapping, time_period)
        
        for metric_name, metric_type in mapping.effectiveness_metrics.items():
            if metric_type == 'percentage':
                metrics[metric_name] = await self._calculate_percentage_metric(
                    metric_name, control_events, mapping
                )
            elif metric_type == 'count':
                metrics[metric_name] = await self._calculate_count_metric(
                    metric_name, control_events, mapping
                )
            elif metric_type == 'rate':
                metrics[metric_name] = await self._calculate_rate_metric(
                    metric_name, control_events, mapping, time_period
                )
        
        return metrics
    
    async def _get_control_events(self, mapping: ControlMapping, 
                                time_period: str) -> List[Dict[str, Any]]:
        """Get all events related to a specific control"""
        # Convert time period to timestamp range
        end_time = datetime.utcnow()
        if time_period == '1h':
            start_time = end_time - timedelta(hours=1)
        elif time_period == '24h':
            start_time = end_time - timedelta(hours=24)
        elif time_period == '7d':
            start_time = end_time - timedelta(days=7)
        elif time_period == '30d':
            start_time = end_time - timedelta(days=30)
        else:
            start_time = end_time - timedelta(hours=24)  # Default to 24h
        
        # Query Wazuh for events matching control rules
        events = []
        for rule_id in mapping.wazuh_rules:
            rule_events = await self.wazuh_client.get_security_events(
                rule_id=rule_id,
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat()
            )
            events.extend(rule_events.get('data', {}).get('affected_items', []))
        
        return events
    
    def _calculate_overall_effectiveness_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall effectiveness score from individual metrics"""
        if not metrics:
            return 0.0
        
        # Weight different types of metrics
        weights = {
            'detection_rate': 0.3,
            'coverage': 0.25,
            'accuracy': 0.2,
            'response_time': 0.15,
            'false_positive_rate': 0.1
        }
        
        weighted_scores = []
        total_weight = 0
        
        for metric_name, value in metrics.items():
            # Normalize metric values to 0-1 scale
            normalized_value = self._normalize_metric_value(metric_name, value)
            
            # Apply weight if available
            weight = weights.get(metric_name, 0.1)  # Default weight
            weighted_scores.append(normalized_value * weight)
            total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        return sum(weighted_scores) / total_weight
```

### Evidence Collection Engine

```python
class ControlEvidenceCollector:
    def __init__(self, wazuh_client, blockchain_client, storage_client):
        self.wazuh_client = wazuh_client
        self.blockchain_client = blockchain_client
        self.storage_client = storage_client
        self.evidence_templates = self._load_evidence_templates()
    
    async def collect_control_evidence(self, control_id: str, 
                                     assessment_period: str) -> Dict[str, Any]:
        """Collect comprehensive evidence for control implementation"""
        evidence_package = {
            'control_id': control_id,
            'collection_timestamp': datetime.utcnow().isoformat(),
            'assessment_period': assessment_period,
            'evidence_items': [],
            'evidence_summary': {},
            'blockchain_hash': None,
            'verification_status': 'pending'
        }
        
        # Get control mapping
        control_mapping = self.control_mapper.control_mappings.get(control_id)
        if not control_mapping:
            return evidence_package
        
        # Collect different types of evidence
        for evidence_type in control_mapping.evidence_requirements:
            evidence_items = await self._collect_evidence_type(
                evidence_type, control_mapping, assessment_period
            )
            evidence_package['evidence_items'].extend(evidence_items)
        
        # Generate evidence summary
        evidence_package['evidence_summary'] = self._generate_evidence_summary(
            evidence_package['evidence_items']
        )
        
        # Store evidence in blockchain for tamper-proof verification
        blockchain_hash = await self._store_evidence_blockchain(evidence_package)
        evidence_package['blockchain_hash'] = blockchain_hash
        evidence_package['verification_status'] = 'verified'
        
        return evidence_package
    
    async def _collect_evidence_type(self, evidence_type: str, 
                                   mapping: ControlMapping, 
                                   period: str) -> List[Dict[str, Any]]:
        """Collect specific type of evidence"""
        evidence_items = []
        
        if evidence_type == 'security_event_logs':
            # Collect security event logs
            events = await self._get_control_events(mapping, period)
            for event in events:
                evidence_items.append({
                    'evidence_type': 'security_event_log',
                    'evidence_id': f"event-{event.get('id', '')}",
                    'timestamp': event.get('timestamp', ''),
                    'source': 'wazuh_siem',
                    'content': json.dumps(event, indent=2),
                    'hash': self._calculate_content_hash(event)
                })
        
        elif evidence_type == 'configuration_records':
            # Collect configuration evidence
            config_data = await self._collect_configuration_evidence(mapping)
            evidence_items.extend(config_data)
        
        elif evidence_type == 'audit_logs':
            # Collect audit logs
            audit_data = await self._collect_audit_evidence(mapping, period)
            evidence_items.extend(audit_data)
        
        elif evidence_type == 'compliance_reports':
            # Collect compliance reports
            compliance_data = await self._collect_compliance_evidence(mapping, period)
            evidence_items.extend(compliance_data)
        
        return evidence_items
    
    async def _store_evidence_blockchain(self, evidence_package: Dict[str, Any]) -> str:
        """Store evidence package in blockchain for tamper-proof verification"""
        # Create evidence hash
        evidence_content = json.dumps(evidence_package, sort_keys=True)
        evidence_hash = hashlib.sha256(evidence_content.encode()).hexdigest()
        
        # Store in blockchain
        blockchain_tx = await self.blockchain_client.store_evidence({
            'evidence_type': 'control_assessment_evidence',
            'control_id': evidence_package['control_id'],
            'evidence_hash': evidence_hash,
            'timestamp': evidence_package['collection_timestamp'],
            'evidence_summary': evidence_package['evidence_summary']
        })
        
        return blockchain_tx['transaction_id']
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Controls Module Team
