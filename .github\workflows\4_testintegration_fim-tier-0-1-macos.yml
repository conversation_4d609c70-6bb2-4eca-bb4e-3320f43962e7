name: Integration tests for FIM on MacOS - Tier 0 and 1

on:
  workflow_dispatch:
    inputs:
      base_branch:
        description: 'Base branch'
        required: true
        default: 'main'
  pull_request:
    paths:
        - ".github/workflows/4_testintegration_fim-tier-0-1-macos.yml"
        - "src/config/syscheck-config.c"
        - "src/config/syscheck-config.h"
        - "src/syscheckd/**"
        - "src/Makefile"
        - "tests/integration/conftest.py"
        - "tests/integration/test_fim/**"

jobs:
  build:
    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
      BRANCH_BASE: ${{ github.base_ref || inputs.base_branch }}
    runs-on: macos-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version-file: ".github/workflows/.python-version-it"
          architecture: x64
      # Build wazuh agent for macOS.
      - name: Build wazuh agent for macOS
        run: |
          make deps -C src TARGET=agent -j2
          make -C src TARGET=agent -j2
      # Install wazuh agent for macOS.
      - name: Install wazuh agent for macOS
        run: |
            echo 'USER_LANGUAGE="en"' > ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_NO_STOP="y"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_INSTALL_TYPE="agent"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo "USER_DIR=/Library/Ossec" >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_ENABLE_EMAIL="n"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_ENABLE_SYSCHECK="n"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_ENABLE_ROOTCHECK="n"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_ENABLE_OPENSCAP="n"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_WHITE_LIST="n"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_ENABLE_SYSLOG="n"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_ENABLE_AUTHD="n"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_AUTO_START="y"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            echo 'USER_AGENT_SERVER_IP="127.0.0.1"' >> ./etc/preloaded-vars.conf
            echo "" >> ./etc/preloaded-vars.conf
            sudo sh install.sh
            rm ./etc/preloaded-vars.conf
      # Download and install integration tests framework.
      - name: Download and install integration tests framework
        run: |
          if [ "X`git ls-remote https://github.com/wazuh/qa-integration-framework.git ${BRANCH_NAME}`" != "X" ]; then
              QA_BRANCH=${BRANCH_NAME}
          elif [ "X`git ls-remote https://github.com/wazuh/qa-integration-framework.git ${BRANCH_BASE}`" != "X" ]; then
              QA_BRANCH=${BRANCH_BASE}
          else
              QA_BRANCH="main"
          fi
          git clone -b ${QA_BRANCH} --single-branch https://github.com/wazuh/qa-integration-framework.git
          sudo pip install qa-integration-framework/
          sudo rm -rf qa-integration-framework/
      # Run fim integration tests.
      - name: Run fim integration tests
        run: |
          cd tests/integration
          sudo python -m pytest --tier 0 --tier 1 test_fim/
