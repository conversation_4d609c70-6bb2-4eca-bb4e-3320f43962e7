name: Integration tests for SCA on Windows - Tier 0 and 1

on:
  workflow_dispatch:
    inputs:
      base_branch:
        description: 'Base branch'
        required: true
        default: 'main'
  pull_request:
    paths:
        - ".github/workflows/4_testintegration_sca-tier-0-1-win.yml"
        - "src/config/wmodules-sca.c"
        - "src/wazuh_modules/wm_sca.c"
        - "src/wazuh_modules/wm_sca.h"
        - "src/win32/**"
        - "src/Makefile"
        - "tests/integration/conftest.py"
        - "tests/integration/test_sca/**"


jobs:
  # Build the winagent on linux.
  build:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v3
      # Install wingw required to build the winagent.
      - name: Install dependencies
        run: sudo apt install gcc-mingw-w64 g++-mingw-w64-i686 g++-mingw-w64-x86-64 nsis -y
      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake
      # Build winagent.
      - name: Build wazuh agent for windows
        run: |
          make deps -C src TARGET=winagent -j2
          make -C src TARGET=winagent -j2
      # Compress the files generated by the build.
      - name: Compress the winagent build
        run: cd .. && zip -r wazuh.zip wazuh
      # Make folder and save the compressed build as artifacts.
      - name: Save compressed build
        run: |
          mkdir -p src/winagent
          cp ../wazuh.zip src/winagent/wazuh.zip
      # Upload build artifacts.
      - name: Upload Artifact winagent
        uses: actions/upload-artifact@v4
        with:
          name: winagent
          path: src/winagent
  # Execute the tests on windows.
  run-test:
    needs: build
    env:
      BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
      BRANCH_BASE: ${{ github.base_ref || inputs.base_branch }}
    runs-on: windows-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version-file: ".github/workflows/.python-version-it"
          architecture: x64
      - name: Add WiX toolkit to PATH
        shell: bash
        run: echo "${WIX}bin" >> $GITHUB_PATH
      # Download the compressed winagent build.
      - name: Download Artifact winagent
        uses: actions/download-artifact@v4
        with:
          name: winagent
          path: C:\winagent\
      # Decompress the winagent files.
      - name: Decompress wazuh
        run: |
          Expand-Archive -LiteralPath C:\winagent\wazuh.zip -DestinationPath C:\
      # Build the winagent installer.
      - name: Build wazuh installer
        run: |
          cd  C:\wazuh\src\win32
          .\wazuh-installer-build-msi.bat
      # Install the windows agent.
      - name: Install wazuh agent
        run: |
          cd  C:\wazuh\src\win32
          .\wazuh-agent--.msi /q WAZUH_MANAGER="127.0.0.1"
      # Download and install integration tests framework.
      - name: Download and install qa-integration-framework
        run: |
          if (git ls-remote https://github.com/wazuh/qa-integration-framework.git $env:BRANCH_NAME) {
              $QA_BRANCH = $env:BRANCH_NAME
          } elseif (git ls-remote https://github.com/wazuh/qa-integration-framework.git $env:BRANCH_BASE) {
              $QA_BRANCH = $env:BRANCH_BASE
          } else {
              $QA_BRANCH = "main"
          }
          git clone -b $QA_BRANCH --single-branch https://github.com/wazuh/qa-integration-framework.git
          pip install qa-integration-framework/
          rm qa-integration-framework/ -r -force
      # Run sca integration tests.
      - name: Run sca integration tests
        run: |
          cd C:\wazuh\tests\integration
          python -m pytest -m win32 --tier 0 --tier 1 test_sca\
