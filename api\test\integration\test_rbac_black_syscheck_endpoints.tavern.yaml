---
test_name: GET /syscheck

stages:

  - name: Try to get syscheck scan results for agent 000 (<PERSON><PERSON>)
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/000"
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

  - name: Try to get syscheck scan results for agent 002 (Allow)
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/002"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

---
test_name: GET /syscheck/{agent_id}/last_scan

stages:

  - name: Try to get when the last scan for agent 000 started and ended
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/000/last_scan"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Try to get when the last scan for agent 002 started and ended
    request:
      verify: False
      method: GET
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/002/last_scan"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - end: !anything
              start: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: PUT /syscheck

stages:

  - name: Run a syscheck scan in all agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 5
          total_failed_items: 0
        
  - name: Try to run a syscheck scan on a list of agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '000,003,001,004,008'
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 4000
              id:
                - '000'
                - '001'
                - '003'
          total_affected_items: 2
          total_failed_items: 3
        
  - name: Try to run a syscheck scan on a list of agents
    request:
      verify: False
      method: PUT
      url: "{protocol:s}://{host:s}:{port:d}/syscheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001'
    response:
      <<: *permission_denied

---
test_name: DELETE /syscheck

stages:

  - name: Try to delete syscheck scans in agent 002 (Allow)
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/002"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 1760
              id:
                - '002'
          total_affected_items: 0
          total_failed_items: 1

  - name: Try to delete syscheck scans in agent 001 (Deny)
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/syscheck/001"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied
