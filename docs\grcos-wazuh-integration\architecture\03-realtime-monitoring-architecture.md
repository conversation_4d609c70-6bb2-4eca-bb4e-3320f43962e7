# Real-time Monitoring Architecture

## Overview

This document outlines the real-time monitoring architecture for Wazuh SIEM/XDR integration within GRCOS. The architecture enables sub-second threat detection, immediate incident response, and continuous security posture assessment across IT/OT/IoT environments.

## Real-time Processing Architecture

### Event Streaming Infrastructure

```mermaid
graph TB
    subgraph "Event Sources"
        A[Wazuh Agents]
        B[Network Sensors]
        C[Cloud APIs]
        D[Log Forwarders]
    end
    
    subgraph "Stream Processing Layer"
        E[Event Ingestion Gateway]
        F[Stream Router]
        G[Event Enrichment]
        H[Real-time Analytics]
    end
    
    subgraph "Processing Engines"
        I[Threat Detection Engine]
        J[Anomaly Detection Engine]
        K[Correlation Engine]
        L[Response Engine]
    end
    
    subgraph "Output Systems"
        M[Alert Manager]
        N[Incident Manager]
        O[Dashboard Updates]
        P[Automated Response]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    
    H --> I
    H --> J
    H --> K
    
    I --> M
    J --> M
    K --> N
    
    M --> O
    N --> P
```

### Stream Processing Pipeline

```python
import asyncio
import aioredis
from dataclasses import dataclass
from typing import List, Dict, Any
import json

@dataclass
class StreamEvent:
    event_id: str
    timestamp: str
    source: str
    event_type: str
    severity: int
    data: Dict[str, Any]
    metadata: Dict[str, Any]

class RealTimeEventProcessor:
    def __init__(self, config):
        self.config = config
        self.redis_client = None
        self.processing_queues = {}
        self.processors = {}
        self.metrics = {}
    
    async def initialize(self):
        """Initialize the real-time processing system"""
        # Initialize Redis for stream processing
        self.redis_client = await aioredis.from_url(
            self.config['redis_url'],
            encoding='utf-8',
            decode_responses=True
        )
        
        # Initialize processing queues
        await self._initialize_processing_queues()
        
        # Initialize processors
        await self._initialize_processors()
        
        # Start metrics collection
        asyncio.create_task(self._collect_metrics())
    
    async def _initialize_processing_queues(self):
        """Initialize Redis streams for different event types"""
        queue_configs = {
            'security_events': {'maxlen': 100000, 'approximate': True},
            'system_events': {'maxlen': 50000, 'approximate': True},
            'network_events': {'maxlen': 75000, 'approximate': True},
            'compliance_events': {'maxlen': 25000, 'approximate': True}
        }
        
        for queue_name, config in queue_configs.items():
            self.processing_queues[queue_name] = {
                'stream_key': f"events:{queue_name}",
                'consumer_group': f"grcos_{queue_name}_processors",
                'config': config
            }
            
            # Create consumer group if it doesn't exist
            try:
                await self.redis_client.xgroup_create(
                    self.processing_queues[queue_name]['stream_key'],
                    self.processing_queues[queue_name]['consumer_group'],
                    id='0',
                    mkstream=True
                )
            except Exception:
                pass  # Group already exists
    
    async def ingest_event(self, event: StreamEvent):
        """Ingest event into appropriate processing stream"""
        # Determine target queue based on event characteristics
        target_queue = self._determine_target_queue(event)
        
        # Serialize event data
        event_data = {
            'event_id': event.event_id,
            'timestamp': event.timestamp,
            'source': event.source,
            'event_type': event.event_type,
            'severity': str(event.severity),
            'data': json.dumps(event.data),
            'metadata': json.dumps(event.metadata)
        }
        
        # Add to Redis stream
        stream_key = self.processing_queues[target_queue]['stream_key']
        await self.redis_client.xadd(
            stream_key,
            event_data,
            maxlen=self.processing_queues[target_queue]['config']['maxlen'],
            approximate=self.processing_queues[target_queue]['config']['approximate']
        )
        
        # Update metrics
        await self._update_ingestion_metrics(target_queue)
    
    def _determine_target_queue(self, event: StreamEvent) -> str:
        """Determine which processing queue to use for the event"""
        if event.severity >= 7:  # High severity security events
            return 'security_events'
        elif event.event_type in ['network', 'firewall', 'ids']:
            return 'network_events'
        elif event.event_type in ['compliance', 'audit', 'policy']:
            return 'compliance_events'
        else:
            return 'system_events'
```

### Real-time Threat Detection Engine

```python
class RealTimeThreatDetector:
    def __init__(self, ml_models, threat_intel_client):
        self.ml_models = ml_models
        self.threat_intel = threat_intel_client
        self.detection_rules = self._load_detection_rules()
        self.baseline_models = {}
        self.active_threats = {}
    
    async def analyze_event(self, event: StreamEvent) -> Dict[str, Any]:
        """Real-time threat analysis of security event"""
        analysis_result = {
            'threat_detected': False,
            'threat_score': 0.0,
            'threat_categories': [],
            'confidence': 0.0,
            'indicators': [],
            'recommended_actions': []
        }
        
        # Rule-based detection
        rule_results = await self._apply_detection_rules(event)
        analysis_result.update(rule_results)
        
        # ML-based anomaly detection
        ml_results = await self._ml_anomaly_detection(event)
        analysis_result = self._merge_analysis_results(analysis_result, ml_results)
        
        # Threat intelligence correlation
        ti_results = await self._threat_intelligence_correlation(event)
        analysis_result = self._merge_analysis_results(analysis_result, ti_results)
        
        # Behavioral analysis
        behavioral_results = await self._behavioral_analysis(event)
        analysis_result = self._merge_analysis_results(analysis_result, behavioral_results)
        
        # Calculate final threat score
        analysis_result['threat_score'] = self._calculate_composite_threat_score(
            analysis_result
        )
        
        # Determine if threat detected
        analysis_result['threat_detected'] = analysis_result['threat_score'] > 0.7
        
        return analysis_result
    
    async def _apply_detection_rules(self, event: StreamEvent) -> Dict[str, Any]:
        """Apply rule-based threat detection"""
        results = {
            'rule_matches': [],
            'rule_score': 0.0,
            'rule_indicators': []
        }
        
        for rule in self.detection_rules:
            if await self._evaluate_rule(rule, event):
                results['rule_matches'].append(rule['id'])
                results['rule_score'] = max(results['rule_score'], rule['score'])
                results['rule_indicators'].extend(rule['indicators'])
        
        return results
    
    async def _ml_anomaly_detection(self, event: StreamEvent) -> Dict[str, Any]:
        """ML-based anomaly detection"""
        results = {
            'anomaly_score': 0.0,
            'anomaly_type': None,
            'baseline_deviation': 0.0
        }
        
        # Extract features for ML model
        features = self._extract_ml_features(event)
        
        # Get anomaly score from ML model
        anomaly_score = await self.ml_models['anomaly_detector'].predict(features)
        results['anomaly_score'] = anomaly_score
        
        # Determine anomaly type
        if anomaly_score > 0.8:
            results['anomaly_type'] = 'high_anomaly'
        elif anomaly_score > 0.6:
            results['anomaly_type'] = 'medium_anomaly'
        elif anomaly_score > 0.4:
            results['anomaly_type'] = 'low_anomaly'
        
        return results
    
    async def _threat_intelligence_correlation(self, event: StreamEvent) -> Dict[str, Any]:
        """Correlate event with threat intelligence"""
        results = {
            'ti_matches': [],
            'ti_score': 0.0,
            'ti_indicators': []
        }
        
        # Extract IOCs from event
        iocs = self._extract_iocs(event)
        
        # Check against threat intelligence
        for ioc in iocs:
            ti_result = await self.threat_intel.lookup(ioc)
            if ti_result and ti_result['malicious']:
                results['ti_matches'].append({
                    'ioc': ioc,
                    'threat_type': ti_result['threat_type'],
                    'confidence': ti_result['confidence']
                })
                results['ti_score'] = max(results['ti_score'], ti_result['confidence'])
        
        return results
```

### Real-time Response Orchestration

```python
class RealTimeResponseOrchestrator:
    def __init__(self, policy_engine, action_executors):
        self.policy_engine = policy_engine
        self.action_executors = action_executors
        self.response_cache = {}
        self.active_responses = {}
    
    async def orchestrate_response(self, threat_analysis: Dict[str, Any], 
                                 event: StreamEvent) -> Dict[str, Any]:
        """Orchestrate real-time response to detected threat"""
        response_plan = {
            'response_id': self._generate_response_id(),
            'timestamp': datetime.utcnow().isoformat(),
            'threat_analysis': threat_analysis,
            'source_event': event,
            'actions': [],
            'status': 'initiated'
        }
        
        # Determine response actions based on policy
        policy_decision = await self.policy_engine.evaluate_response_policy(
            threat_analysis, event
        )
        
        # Execute immediate actions
        immediate_actions = policy_decision.get('immediate_actions', [])
        for action in immediate_actions:
            execution_result = await self._execute_action(action, event)
            response_plan['actions'].append({
                'action': action,
                'result': execution_result,
                'timestamp': datetime.utcnow().isoformat()
            })
        
        # Schedule delayed actions
        delayed_actions = policy_decision.get('delayed_actions', [])
        for action in delayed_actions:
            await self._schedule_delayed_action(action, event, response_plan['response_id'])
        
        # Update response status
        response_plan['status'] = 'executing'
        self.active_responses[response_plan['response_id']] = response_plan
        
        return response_plan
    
    async def _execute_action(self, action: Dict[str, Any], 
                            event: StreamEvent) -> Dict[str, Any]:
        """Execute a response action"""
        action_type = action['type']
        action_params = action.get('parameters', {})
        
        if action_type not in self.action_executors:
            return {
                'success': False,
                'error': f'Unknown action type: {action_type}'
            }
        
        try:
            executor = self.action_executors[action_type]
            result = await executor.execute(action_params, event)
            return {
                'success': True,
                'result': result
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
```

## Performance Optimization

### Low-latency Processing

```python
class LowLatencyProcessor:
    def __init__(self, config):
        self.config = config
        self.processing_pools = {}
        self.circuit_breakers = {}
        self.performance_metrics = {}
    
    async def process_event_batch(self, events: List[StreamEvent]) -> List[Dict[str, Any]]:
        """Process batch of events with low latency optimization"""
        # Pre-allocate result list
        results = [None] * len(events)
        
        # Group events by processing requirements
        event_groups = self._group_events_by_processing_type(events)
        
        # Process groups in parallel
        processing_tasks = []
        for group_type, group_events in event_groups.items():
            task = asyncio.create_task(
                self._process_event_group(group_type, group_events)
            )
            processing_tasks.append(task)
        
        # Wait for all processing to complete
        group_results = await asyncio.gather(*processing_tasks)
        
        # Merge results back to original order
        result_index = 0
        for group_result in group_results:
            for result in group_result:
                results[result_index] = result
                result_index += 1
        
        return results
    
    def _group_events_by_processing_type(self, events: List[StreamEvent]) -> Dict[str, List[StreamEvent]]:
        """Group events by their processing requirements"""
        groups = {
            'high_priority': [],
            'standard': [],
            'bulk': []
        }
        
        for event in events:
            if event.severity >= 10:
                groups['high_priority'].append(event)
            elif event.severity >= 7:
                groups['standard'].append(event)
            else:
                groups['bulk'].append(event)
        
        return groups
```

### Memory-efficient Processing

```python
class MemoryEfficientProcessor:
    def __init__(self, max_memory_mb=1024):
        self.max_memory_mb = max_memory_mb
        self.memory_monitor = MemoryMonitor()
        self.object_pools = {}
        self.gc_threshold = 0.8  # Trigger GC at 80% memory usage
    
    async def process_with_memory_management(self, event_stream):
        """Process events with active memory management"""
        processed_count = 0
        
        async for event in event_stream:
            # Check memory usage
            memory_usage = self.memory_monitor.get_usage_percentage()
            
            if memory_usage > self.gc_threshold:
                # Trigger garbage collection
                await self._trigger_gc()
                
                # If still high, apply backpressure
                if self.memory_monitor.get_usage_percentage() > 0.9:
                    await self._apply_backpressure()
            
            # Process event using object pools
            result = await self._process_event_pooled(event)
            
            # Yield result
            yield result
            
            processed_count += 1
            
            # Periodic cleanup
            if processed_count % 1000 == 0:
                await self._periodic_cleanup()
    
    async def _process_event_pooled(self, event: StreamEvent):
        """Process event using object pools to reduce memory allocation"""
        # Get processor from pool
        processor = self._get_processor_from_pool()
        
        try:
            # Process event
            result = await processor.process(event)
            return result
        finally:
            # Return processor to pool
            self._return_processor_to_pool(processor)
```

## Monitoring and Observability

### Real-time Metrics Collection

```python
class RealTimeMetricsCollector:
    def __init__(self, metrics_backend):
        self.metrics_backend = metrics_backend
        self.metric_buffers = {}
        self.collection_intervals = {
            'latency': 1,      # 1 second
            'throughput': 5,   # 5 seconds
            'errors': 1,       # 1 second
            'resources': 10    # 10 seconds
        }
    
    async def start_collection(self):
        """Start real-time metrics collection"""
        collection_tasks = []
        
        for metric_type, interval in self.collection_intervals.items():
            task = asyncio.create_task(
                self._collect_metric_type(metric_type, interval)
            )
            collection_tasks.append(task)
        
        await asyncio.gather(*collection_tasks)
    
    async def _collect_metric_type(self, metric_type: str, interval: int):
        """Collect specific type of metrics at regular intervals"""
        while True:
            try:
                if metric_type == 'latency':
                    await self._collect_latency_metrics()
                elif metric_type == 'throughput':
                    await self._collect_throughput_metrics()
                elif metric_type == 'errors':
                    await self._collect_error_metrics()
                elif metric_type == 'resources':
                    await self._collect_resource_metrics()
                
                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"Error collecting {metric_type} metrics: {e}")
                await asyncio.sleep(interval)
    
    async def _collect_latency_metrics(self):
        """Collect processing latency metrics"""
        latency_data = {
            'event_ingestion_latency': self._get_ingestion_latency(),
            'processing_latency': self._get_processing_latency(),
            'response_latency': self._get_response_latency(),
            'end_to_end_latency': self._get_end_to_end_latency()
        }
        
        await self.metrics_backend.record_metrics('latency', latency_data)
```

### Health Monitoring

```python
class RealTimeHealthMonitor:
    def __init__(self, components):
        self.components = components
        self.health_status = {}
        self.alert_thresholds = {
            'latency_ms': 1000,
            'error_rate': 0.05,
            'memory_usage': 0.85,
            'cpu_usage': 0.80
        }
    
    async def continuous_health_check(self):
        """Continuously monitor system health"""
        while True:
            try:
                health_report = await self._generate_health_report()
                
                # Check for critical issues
                critical_issues = self._identify_critical_issues(health_report)
                
                if critical_issues:
                    await self._handle_critical_issues(critical_issues)
                
                # Update health status
                self.health_status = health_report
                
                await asyncio.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Health check error: {e}")
                await asyncio.sleep(60)  # Longer interval on error
    
    async def _generate_health_report(self) -> Dict[str, Any]:
        """Generate comprehensive health report"""
        health_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'overall_status': 'healthy',
            'components': {}
        }
        
        for component_name, component in self.components.items():
            component_health = await self._check_component_health(component)
            health_report['components'][component_name] = component_health
            
            if component_health['status'] != 'healthy':
                health_report['overall_status'] = 'degraded'
        
        return health_report
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Real-time Systems Team
