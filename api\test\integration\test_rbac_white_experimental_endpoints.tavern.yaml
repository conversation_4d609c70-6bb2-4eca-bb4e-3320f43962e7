---
test_name: DELETE /experimental/rootcheck

stages:

  - name: Delete rootcheck scans in agent 001 (Allow)
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/experimental/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "001"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Try to delete rootcheck scans in agent 002 (Deny)
    request:
      verify: False
      method: DELETE
      url: "{protocol:s}://{host:s}:{port:d}/experimental/rootcheck"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: "002"
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/rootcheck"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: 'all'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - '001'
            - '003'
            - '005'
            - '007'
          failed_items: [ ]
          total_affected_items: 4
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/rootcheck"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,002,005,006'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - '001'
            - '005'
          failed_items:
            - error:
                code: 4000
              id:
                - '002'
                - '006'
          total_affected_items: 2
          total_failed_items: 2


---
test_name: DELETE /experimental/syscheck

stages:

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscheck"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: 'all'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []  # Some agents are allowed but they raise error 1706, which is excluded as the agents newer than 3.12 are not affected.
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscheck"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,002,005,006'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1760
              id:
                - '001'
                - '005'
            - error:
                code: 4000
              id:
                - '002'
                - '006'
          total_affected_items: 0
          total_failed_items: 4

---
test_name: GET /experimental/ciscat/results

stages:

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/ciscat/results"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/ciscat/results"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '003,004,007,008'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4000
              id:
                - '004'
                - '008'
          total_affected_items: 0
          total_failed_items: 2

---
test_name: GET /experimental/syscollector/hardware

stages:

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hardware"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '003'
            - agent_id: '005'
            - agent_id: '007'
          failed_items: []
          total_affected_items: 4
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hardware"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,002,006,007'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '007'
          failed_items:
            - error:
                code: 4000
              id:
                - '002'
                - '006'
          total_affected_items: 2
          total_failed_items: 2

---
test_name: GET /experimental/syscollector/netaddr

stages:

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netaddr"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '003'
            - agent_id: '005'
            - agent_id: '007'
          failed_items: []
          total_affected_items: 4
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netaddr"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '005,006,007,008'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '005'
            - agent_id: '007'
          failed_items:
            - error:
                code: 4000
              id:
                - '006'
                - '008'
          total_affected_items: 2
          total_failed_items: 2

---
test_name: GET /experimental/syscollector/netiface

stages:

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netiface"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '003'
            - agent_id: '005'
            - agent_id: '007'
          failed_items: []
          total_affected_items: 4
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netiface"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,005,007,008'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '005'
            - agent_id: '007'
          failed_items:
            - error:
                code: 4000
              id:
                - '008'
          total_affected_items: 3
          total_failed_items: 1

---
test_name: GET /experimental/syscollector/netproto

stages:

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netproto"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '003'
            - agent_id: '005'
            - agent_id: '007'
          failed_items: []
          total_affected_items: 4
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/netproto"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '002,004,005,008'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '005'
          failed_items:
            - error:
                code: 4000
              id:
                - '002'
                - '004'
                - '008'
          total_affected_items: 1
          total_failed_items: 3

---
test_name: GET /experimental/syscollector/os

stages:

  - name: Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/os"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '003'
            - agent_id: '005'
            - agent_id: '007'
          failed_items: []
          total_affected_items: 4
          total_failed_items: 0

  - name: Request a list of agents (Partially allowed, user aware)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/os"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,002,003,004'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '003'
          failed_items:
            - error:
                code: 4000
              id:
                - '002'
                - '004'
          total_affected_items: 2
          total_failed_items: 2

---
test_name: GET /experimental/syscollector/packages

stages:

  - name:  Request all agents (Partially allowed, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/packages?wait_for_complete=true"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name:  Request all agents (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/packages"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '002,004,006,008'
    response:
      <<: *permission_denied
      json:
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

---
test_name: GET /experimental/syscollector/ports

stages:

  - name: Request
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/ports"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '001'
            - agent_id: '003'
            - agent_id: '003'
            - agent_id: '005'
            - agent_id: '007'
          failed_items: []
          total_affected_items: 6
          total_failed_items: 0

  - name: Request
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/ports"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,003,005,007'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - agent_id: '001'
            - agent_id: '001'
            - agent_id: '003'
            - agent_id: '003'
            - agent_id: '005'
            - agent_id: '007'
          failed_items: []
          total_affected_items: 6
          total_failed_items: 0

---
test_name: GET /experimental/syscollector/processes

stages:

  - name: Request
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/processes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Request
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/processes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '001,004,005,008'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 4000
              id:
                - '004'
                - '008'
          total_affected_items: !anyint
          total_failed_items: 2

---
test_name: GET /experimental/syscollector/hotfixes

stages:

  - name: Request
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hotfixes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0  # Only the agent 000 will have results but we don't have permissions
          total_failed_items: 0

  - name: Request
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/experimental/syscollector/hotfixes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: '000,004,005,006'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: [] # Only the agent 000 will have results but we don't have permissions
          failed_items:
          # Even agent IDs do not have permissions
            - error:
                code: 4000
              id:
                - '000'
                - '004'
                - '006'
          total_affected_items: 0
          total_failed_items: 3
