name: VD Scanner tools Delivery

# Controls when the action will run.
on:
  # Trigger when a pull request is merged into main
  push:
    branches:
      - main
    paths:
      - "src/wazuh_modules/vulnerability_scanner/testtool/**"
      - "src/wazuh_modules/vulnerability_scanner/src/**"
      - "src/wazuh_modules/vulnerability_scanner/include/**"
      - ".github/workflows/4_builderprecompiled_vdscanner-testtools.yml"
      - "src/wazuh_modules/vulnerability_scanner/schemas/**"

  # Allows manual trigger.
  workflow_dispatch:

# Ensures only one instance of this workflow is running per PR
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

env:
  TOOLS_DIR: ${{github.workspace}}/src/wazuh_modules/vulnerability_scanner/build/testtool/
  TOOLS_FILE_NAME: legacy_vdscanner_testtools

jobs:
  build:
    name: VD Scanner Tools Delivery

    # Runs only if the PR status is different to Draft
    if: ${{ !github.event.pull_request.draft }}
    runs-on: ubuntu-22.04
    timeout-minutes: 60

    steps:
      - name: Install dependencies
        run: sudo apt-get install lzip xz-utils

      - name: Check out repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake

      - name: Project dependencies
        uses: ./.github/actions/vulnerability_scanner_deps
        with:
          test: "false"

      - name: Router
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/router
          test: "false"

      - name: Indexer connector
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/indexer_connector
          test: "false"

      - name: Content manager
        uses: ./.github/actions/compile_and_test
        with:
          path: src/shared_modules/content_manager
          test: "false"

      - name: Vulnerability scanner
        uses: ./.github/actions/compile_and_test
        with:
          path: src/wazuh_modules/vulnerability_scanner
          test: "false"

      - name: Prepare vdscanner tools
        run: |
          # Paths
          SRC_DIR=${{github.workspace}}/src/
          SHARED_MODULES_DIR=${{github.workspace}}/src/shared_modules/
          EXTERNAL_DIR=${{github.workspace}}/src/external/
          VDSCANNER_DIR=${{github.workspace}}/src/wazuh_modules/vulnerability_scanner/

          # Copy flatbuffer schemas
          cp -r ${VDSCANNER_DIR}schemas ${{env.TOOLS_DIR}}
          cp ${SHARED_MODULES_DIR}utils/flatbuffers/schemas/syscollector_deltas.fbs ${{env.TOOLS_DIR}}/schemas
          cp ${SHARED_MODULES_DIR}utils/flatbuffers/schemas/rsync.fbs ${{env.TOOLS_DIR}}/schemas

          # Copy required libraries
          cp ${SRC_DIR}libwazuhext.so ${{env.TOOLS_DIR}}
          cp ${EXTERNAL_DIR}rocksdb/build/librocksdb.so.8.3.2 ${{env.TOOLS_DIR}}librocksdb.so.8
          cp ${SHARED_MODULES_DIR}indexer_connector/build/libindexer_connector.so ${{env.TOOLS_DIR}}
          cp ${SHARED_MODULES_DIR}router/build/librouter.so ${{env.TOOLS_DIR}}
          cp ${SHARED_MODULES_DIR}content_manager/build/libcontent_manager.so ${{env.TOOLS_DIR}}
          cp ${VDSCANNER_DIR}build/libvulnerability_scanner.so ${{env.TOOLS_DIR}}

      - name: Compress vdscanner tools and flatbuffer schemas.
        run: |
          cd ${{env.TOOLS_DIR}}; XZ_OPT=-9; find . \( -name "*.so*" -o -wholename ./scanner/vd_scanner_testtool -o -wholename ./rocksDBQuery/rocks_db_query_testtool -o -name schemas \) -exec tar cfJ ${{env.TOOLS_FILE_NAME}}.tar.xz {} +;
          if [ $? -eq 0 ]; then
            size=$(du -sh ${{env.TOOLS_FILE_NAME}}.tar.xz)
            echo "vdscanner tools successfully compressed: ${size}"

            tar -tf ${{env.TOOLS_FILE_NAME}}.tar.xz

            # Moving location to access easily in the next step.
            mv ${{env.TOOLS_FILE_NAME}}.tar.xz ${{github.workspace}}
            exit 0
          else
            echo "Error compressing vdscanner tools and flatbuffer schemas."
            exit 1
          fi

      # Upload vdscanner tools
      - name: Upload vdscanner tools
        if: ${{ success() && (github.event_name == 'push' || github.event_name == 'workflow_dispatch') }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{env.TOOLS_FILE_NAME}}
          path: ${{env.TOOLS_FILE_NAME}}.tar.xz
          if-no-files-found: error
          compression-level: 0
          overwrite: true
