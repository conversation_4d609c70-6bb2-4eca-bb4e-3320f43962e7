name: Python code analysis

on:
  workflow_dispatch:
  schedule:
    - cron: '0 10 * * 2'

jobs:
  pick-branches:
    name: Get branches to analyze
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate dynamic matrix
        id: set-matrix
        run: |
          VERSIONED_BRANCHES_REGEX="^main$|^[0-9]{1,3}\.[0-9]{2}\.[0-9]{1,2}$"

          BRANCHES=$(git branch -r | awk '{print $1}' | sed 's|origin/||' | grep -E "$VERSIONED_BRANCHES_REGEX")

          JSON_ARRAY=$(printf '%s\n' "$BRANCHES" | while read -r ref; do
            sha=$(git rev-parse "origin/$ref")
            jq -c -n --arg ref "$ref" --arg sha "$sha" '{ref: $ref, sha: $sha}'
          done | jq -c -s '{include: .}')

          echo "matrix=$JSON_ARRAY" >> "$GITHUB_OUTPUT"
          echo Picked branches: $JSON_ARRAY

  bandit:
    name: Bandit scan
    needs: pick-branches
    runs-on: ubuntu-24.04
    strategy:
      matrix: ${{ fromJSON(needs.pick-branches.outputs.matrix) }}
    permissions:
      contents: read
      security-events: write
      actions: read

    steps:
      - name: Checkot the repo
        uses: actions/checkout@v4
        with:
          ref: ${{ matrix.ref }}
          fetch-depth: 0

      - name: Bandit Scan
        uses: PyCQA/bandit-action@v1
        with:
          exclude: .git,__pycache__,*/test_*,*/test/*,*/tests/*,*/testing/*
