name: Wodles unit tests

on:
  workflow_dispatch:
    inputs:
      generate_report:
        description: 'Generate test report'
        required: false
        type: boolean
        default: false
      qa_branch:
        description: 'QA repository branch'
        required: false
        default: 'main'

jobs:
  build: 
    runs-on: ubuntu-24.04
    strategy:
      fail-fast: false
      matrix:
        python-version: ['3.10']
    env:
      PYTHONPATH: /home/<USER>/work/wazuh/wazuh/api:/home/<USER>/work/wazuh/wazuh/framework
      TEST_REPORT_PATH: /home/<USER>/work/test_report.txt
    steps:
      - uses: actions/checkout@v4

      - name: Setup Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
          cache: 'pip'
          cache-dependency-path: 'framework/requirements-dev.txt'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip wheel
          pip install -r framework/requirements-dev.txt --no-build-isolation

      - name: Run Wodles tests
        run: |
          set -o pipefail
          python -m pytest wodles -vv --ignore=tests --cov=wodles | tee ${TEST_REPORT_PATH}
      
      - name: Generate test report
        if: inputs.generate_report
        run: |
          git clone -b ${{ inputs.qa_branch }} --single-branch https://github.com/wazuh/qa-integration-framework.git
          python qa-integration-framework/src/wazuh_testing/scripts/pytest_results_parser.py -f ${TEST_REPORT_PATH}
