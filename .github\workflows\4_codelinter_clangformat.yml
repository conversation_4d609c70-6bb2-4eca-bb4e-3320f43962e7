name: 4_codelinter_clangformat

on:
  workflow_dispatch:

jobs:
  style:
    runs-on: ubuntu-22.04

    steps:
      - name: Cancel previous runs
        uses: fkirc/skip-duplicate-actions@master
        with:
          cancel_others: 'true'
          github_token: ${{ secrets.GITHUB_TOKEN }}
          skip_after_successful_duplicate: 'false'
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Inventory harvester - Coding style
        uses: ./.github/actions/clang_format
        with:
          path: src/wazuh_modules/inventory_harvester
