[{"path": "api/api", "files": [{"name": "alogging.py", "tag": "alogging", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "api_exception.py", "tag": "api", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "authentication.py", "tag": "authentication", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "configuration.py", "tag": "configuration", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "constants.py", "tag": "constants", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "encoder.py", "tag": "encoder", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "middlewares.py", "tag": "middlewares", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "signals.py", "tag": "signals", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "uri_parser.py", "tag": "uri", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "util.py", "tag": "util", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "validator.py", "tag": "validator", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/api/configuration", "files": [{"name": "api.yaml", "tag": "api", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/api/controllers", "files": [{"name": "active_response_controller.py", "tag": "active", "tests": ["test_active_response_endpoints.tavern.yaml", "test_rbac_black_active_response_endpoints.tavern.yaml", "test_rbac_white_active_response_endpoints.tavern.yaml"]}, {"name": "agent_controller.py", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "cdb_list_controller.py", "tag": "cdb", "tests": ["test_cdb_list_endpoints.tavern.yaml", "test_rbac_black_cdb_list_endpoints.tavern.yaml", "test_rbac_white_cdb_list_endpoints.tavern.yaml"]}, {"name": "ciscat_controller.py", "tag": "ciscat", "tests": ["test_ciscat_endpoints.tavern.yaml", "test_rbac_black_ciscat_endpoints.tavern.yaml", "test_rbac_white_ciscat_endpoints.tavern.yaml"]}, {"name": "cluster_controller.py", "tag": "cluster", "tests": ["test_cluster_endpoints.tavern.yaml", "test_rbac_black_cluster_endpoints.tavern.yaml", "test_rbac_white_cluster_endpoints.tavern.yaml"]}, {"name": "decoder_controller.py", "tag": "decoder", "tests": ["test_decoder_endpoints.tavern.yaml", "test_rbac_black_decoder_endpoints.tavern.yaml", "test_rbac_white_decoder_endpoints.tavern.yaml"]}, {"name": "default_controller.py", "tag": "default", "tests": ["test_default_endpoints.tavern.yaml"]}, {"name": "event_controller.py", "tag": "event", "tests": ["test_event_endpoints.tavern.yaml", "test_rbac_black_event_endpoints.tavern.yaml", "test_rbac_white_event_endpoints.tavern.yaml"]}, {"name": "experimental_controller.py", "tag": "experimental", "tests": ["test_experimental_endpoints.tavern.yaml", "test_rbac_black_experimental_endpoints.tavern.yaml", "test_rbac_white_experimental_endpoints.tavern.yaml"]}, {"name": "logtest_controller.py", "tag": "logtest", "tests": ["test_logtest_endpoints.tavern.yaml", "test_rbac_black_logtest_endpoints.tavern.yaml", "test_rbac_white_logtest_endpoints.tavern.yaml"]}, {"name": "manager_controller.py", "tag": "manager", "tests": ["test_manager_endpoints.tavern.yaml", "test_rbac_black_manager_endpoints.tavern.yaml", "test_rbac_white_manager_endpoints.tavern.yaml"]}, {"name": "mitre_controller.py", "tag": "mitre", "tests": ["test_mitre_endpoints.tavern.yaml", "test_rbac_black_mitre_endpoints.tavern.yaml", "test_rbac_white_mitre_endpoints.tavern.yaml"]}, {"name": "overview_controller.py", "tag": "overview", "tests": ["test_overview_endpoints.tavern.yaml", "test_rbac_black_overview_endpoints.tavern.yaml", "test_rbac_white_overview_endpoints.tavern.yaml"]}, {"name": "rootcheck_controller.py", "tag": "rootcheck", "tests": ["test_rbac_black_rootcheck_endpoints.tavern.yaml", "test_rbac_white_rootcheck_endpoints.tavern.yaml", "test_rootcheck_endpoints.tavern.yaml"]}, {"name": "rule_controller.py", "tag": "rule", "tests": ["test_rbac_black_rule_endpoints.tavern.yaml", "test_rbac_white_rule_endpoints.tavern.yaml", "test_rule_endpoints.tavern.yaml"]}, {"name": "sca_controller.py", "tag": "sca", "tests": ["test_rbac_black_ciscat_endpoints.tavern.yaml", "test_rbac_black_sca_endpoints.tavern.yaml", "test_rbac_white_ciscat_endpoints.tavern.yaml", "test_rbac_white_sca_endpoints.tavern.yaml", "test_sca_endpoints.tavern.yaml"]}, {"name": "security_controller.py", "tag": "security", "tests": ["test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_security_DELETE_endpoints.tavern.yaml", "test_security_GET_endpoints.tavern.yaml", "test_security_POST_endpoints.tavern.yaml", "test_security_PUT_endpoints.tavern.yaml"]}, {"name": "syscheck_controller.py", "tag": "syscheck", "tests": ["test_rbac_black_syscheck_endpoints.tavern.yaml", "test_rbac_white_syscheck_endpoints.tavern.yaml", "test_syscheck_endpoints.tavern.yaml"]}, {"name": "syscollector_controller.py", "tag": "syscollector", "tests": ["test_rbac_black_syscollector_endpoints.tavern.yaml", "test_rbac_white_syscollector_endpoints.tavern.yaml", "test_syscollector_endpoints.tavern.yaml"]}, {"name": "task_controller.py", "tag": "task", "tests": ["test_rbac_black_task_endpoints.tavern.yaml", "test_rbac_white_task_endpoints.tavern.yaml", "test_task_endpoints.tavern.yaml"]}]}, {"path": "api/api/controllers/test", "files": [{"name": "utils.py", "tag": "utils", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/api/models", "files": [{"name": "active_response_model.py", "tag": "active", "tests": ["test_active_response_endpoints.tavern.yaml", "test_rbac_black_active_response_endpoints.tavern.yaml", "test_rbac_white_active_response_endpoints.tavern.yaml"]}, {"name": "agent_added_model.py", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "agent_group_added_model.py", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "agent_inserted_model.py", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "base_model_.py", "tag": "base", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "basic_info_model.py", "tag": "basic", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "configuration_model.py", "tag": "configuration", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "event_ingest_model.py", "tag": "event", "tests": ["test_event_endpoints.tavern.yaml", "test_rbac_black_event_endpoints.tavern.yaml", "test_rbac_white_event_endpoints.tavern.yaml"]}, {"name": "logtest_model.py", "tag": "logtest", "tests": ["test_logtest_endpoints.tavern.yaml", "test_rbac_black_logtest_endpoints.tavern.yaml", "test_rbac_white_logtest_endpoints.tavern.yaml"]}, {"name": "security_model.py", "tag": "security", "tests": ["test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_security_DELETE_endpoints.tavern.yaml", "test_security_GET_endpoints.tavern.yaml", "test_security_POST_endpoints.tavern.yaml", "test_security_PUT_endpoints.tavern.yaml"]}, {"name": "security_token_response_model.py", "tag": "security", "tests": ["test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_security_DELETE_endpoints.tavern.yaml", "test_security_GET_endpoints.tavern.yaml", "test_security_POST_endpoints.tavern.yaml", "test_security_PUT_endpoints.tavern.yaml"]}]}, {"path": "api/api/spec", "files": [{"name": "spec.yaml", "tag": "spec", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh", "files": [{"name": "active_response.py", "tag": "active", "tests": ["test_active_response_endpoints.tavern.yaml", "test_rbac_black_active_response_endpoints.tavern.yaml", "test_rbac_white_active_response_endpoints.tavern.yaml"]}, {"name": "agent.py", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "cdb_list.py", "tag": "cdb", "tests": ["test_cdb_list_endpoints.tavern.yaml", "test_rbac_black_cdb_list_endpoints.tavern.yaml", "test_rbac_white_cdb_list_endpoints.tavern.yaml"]}, {"name": "ciscat.py", "tag": "ciscat", "tests": ["test_ciscat_endpoints.tavern.yaml", "test_rbac_black_ciscat_endpoints.tavern.yaml", "test_rbac_white_ciscat_endpoints.tavern.yaml"]}, {"name": "cluster.py", "tag": "cluster", "tests": ["test_cluster_endpoints.tavern.yaml", "test_rbac_black_cluster_endpoints.tavern.yaml", "test_rbac_white_cluster_endpoints.tavern.yaml"]}, {"name": "decoder.py", "tag": "decoder", "tests": ["test_decoder_endpoints.tavern.yaml", "test_rbac_black_decoder_endpoints.tavern.yaml", "test_rbac_white_decoder_endpoints.tavern.yaml"]}, {"name": "event.py", "tag": "event", "tests": ["test_event_endpoints.tavern.yaml", "test_rbac_black_event_endpoints.tavern.yaml", "test_rbac_white_event_endpoints.tavern.yaml"]}, {"name": "logtest.py", "tag": "logtest", "tests": ["test_logtest_endpoints.tavern.yaml", "test_rbac_black_logtest_endpoints.tavern.yaml", "test_rbac_white_logtest_endpoints.tavern.yaml"]}, {"name": "manager.py", "tag": "manager", "tests": ["test_manager_endpoints.tavern.yaml", "test_rbac_black_manager_endpoints.tavern.yaml", "test_rbac_white_manager_endpoints.tavern.yaml"]}, {"name": "mitre.py", "tag": "mitre", "tests": ["test_mitre_endpoints.tavern.yaml", "test_rbac_black_mitre_endpoints.tavern.yaml", "test_rbac_white_mitre_endpoints.tavern.yaml"]}, {"name": "rootcheck.py", "tag": "rootcheck", "tests": ["test_rbac_black_rootcheck_endpoints.tavern.yaml", "test_rbac_white_rootcheck_endpoints.tavern.yaml", "test_rootcheck_endpoints.tavern.yaml"]}, {"name": "rule.py", "tag": "rule", "tests": ["test_rbac_black_rule_endpoints.tavern.yaml", "test_rbac_white_rule_endpoints.tavern.yaml", "test_rule_endpoints.tavern.yaml"]}, {"name": "sca.py", "tag": "sca", "tests": ["test_rbac_black_ciscat_endpoints.tavern.yaml", "test_rbac_black_sca_endpoints.tavern.yaml", "test_rbac_white_ciscat_endpoints.tavern.yaml", "test_rbac_white_sca_endpoints.tavern.yaml", "test_sca_endpoints.tavern.yaml"]}, {"name": "security.py", "tag": "security", "tests": ["test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_security_DELETE_endpoints.tavern.yaml", "test_security_GET_endpoints.tavern.yaml", "test_security_POST_endpoints.tavern.yaml", "test_security_PUT_endpoints.tavern.yaml"]}, {"name": "stats.py", "tag": "stats", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "syscheck.py", "tag": "syscheck", "tests": ["test_rbac_black_syscheck_endpoints.tavern.yaml", "test_rbac_white_syscheck_endpoints.tavern.yaml", "test_syscheck_endpoints.tavern.yaml"]}, {"name": "syscollector.py", "tag": "syscollector", "tests": ["test_rbac_black_syscollector_endpoints.tavern.yaml", "test_rbac_white_syscollector_endpoints.tavern.yaml", "test_syscollector_endpoints.tavern.yaml"]}, {"name": "task.py", "tag": "task", "tests": ["test_rbac_black_task_endpoints.tavern.yaml", "test_rbac_white_task_endpoints.tavern.yaml", "test_task_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core", "files": [{"name": "InputValidator.py", "tag": "inputvalidator", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "active_response.py", "tag": "active", "tests": ["test_active_response_endpoints.tavern.yaml", "test_rbac_black_active_response_endpoints.tavern.yaml", "test_rbac_white_active_response_endpoints.tavern.yaml"]}, {"name": "agent.py", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "cdb_list.py", "tag": "cdb", "tests": ["test_cdb_list_endpoints.tavern.yaml", "test_rbac_black_cdb_list_endpoints.tavern.yaml", "test_rbac_white_cdb_list_endpoints.tavern.yaml"]}, {"name": "common.py", "tag": "common", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "configuration.py", "tag": "configuration", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "decoder.py", "tag": "decoder", "tests": ["test_decoder_endpoints.tavern.yaml", "test_rbac_black_decoder_endpoints.tavern.yaml", "test_rbac_white_decoder_endpoints.tavern.yaml"]}, {"name": "exception.py", "tag": "exception", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "logtest.py", "tag": "logtest", "tests": ["test_logtest_endpoints.tavern.yaml", "test_rbac_black_logtest_endpoints.tavern.yaml", "test_rbac_white_logtest_endpoints.tavern.yaml"]}, {"name": "manager.py", "tag": "manager", "tests": ["test_manager_endpoints.tavern.yaml", "test_rbac_black_manager_endpoints.tavern.yaml", "test_rbac_white_manager_endpoints.tavern.yaml"]}, {"name": "mitre.py", "tag": "mitre", "tests": ["test_mitre_endpoints.tavern.yaml", "test_rbac_black_mitre_endpoints.tavern.yaml", "test_rbac_white_mitre_endpoints.tavern.yaml"]}, {"name": "pyDaemonModule.py", "tag": "pydae<PERSON><PERSON><PERSON>le", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "results.py", "tag": "results", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "rootcheck.py", "tag": "rootcheck", "tests": ["test_rbac_black_rootcheck_endpoints.tavern.yaml", "test_rbac_white_rootcheck_endpoints.tavern.yaml", "test_rootcheck_endpoints.tavern.yaml"]}, {"name": "rule.py", "tag": "rule", "tests": ["test_rbac_black_rule_endpoints.tavern.yaml", "test_rbac_white_rule_endpoints.tavern.yaml", "test_rule_endpoints.tavern.yaml"]}, {"name": "sca.py", "tag": "sca", "tests": ["test_rbac_black_ciscat_endpoints.tavern.yaml", "test_rbac_black_sca_endpoints.tavern.yaml", "test_rbac_white_ciscat_endpoints.tavern.yaml", "test_rbac_white_sca_endpoints.tavern.yaml", "test_sca_endpoints.tavern.yaml"]}, {"name": "security.py", "tag": "security", "tests": ["test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_security_DELETE_endpoints.tavern.yaml", "test_security_GET_endpoints.tavern.yaml", "test_security_POST_endpoints.tavern.yaml", "test_security_PUT_endpoints.tavern.yaml"]}, {"name": "stats.py", "tag": "stats", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "syscheck.py", "tag": "syscheck", "tests": ["test_rbac_black_syscheck_endpoints.tavern.yaml", "test_rbac_white_syscheck_endpoints.tavern.yaml", "test_syscheck_endpoints.tavern.yaml"]}, {"name": "syscollector.py", "tag": "syscollector", "tests": ["test_rbac_black_syscollector_endpoints.tavern.yaml", "test_rbac_white_syscollector_endpoints.tavern.yaml", "test_syscollector_endpoints.tavern.yaml"]}, {"name": "task.py", "tag": "task", "tests": ["test_rbac_black_task_endpoints.tavern.yaml", "test_rbac_white_task_endpoints.tavern.yaml", "test_task_endpoints.tavern.yaml"]}, {"name": "utils.py", "tag": "utils", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "wazuh_queue.py", "tag": "wazuh", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "wazuh_socket.py", "tag": "wazuh", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "wdb.py", "tag": "wdb", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "wlogging.py", "tag": "wlogging", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/cluster", "files": [{"name": "client.py", "tag": "client", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "cluster.py", "tag": "cluster", "tests": ["test_cluster_endpoints.tavern.yaml", "test_rbac_black_cluster_endpoints.tavern.yaml", "test_rbac_white_cluster_endpoints.tavern.yaml"]}, {"name": "common.py", "tag": "common", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "control.py", "tag": "control", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "local_client.py", "tag": "local", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "local_server.py", "tag": "local", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "master.py", "tag": "master", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "server.py", "tag": "server", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "utils.py", "tag": "utils", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "worker.py", "tag": "worker", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/cluster/dapi", "files": [{"name": "dapi.py", "tag": "dapi", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/cluster/hap_helper", "files": [{"name": "hap_helper.py", "tag": "hap", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "proxy.py", "tag": "proxy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "wazuh.py", "tag": "wazuh", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/cluster/tests", "files": [{"name": "conftest.py", "tag": "conftest", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/tests/data/test_agent", "files": [{"name": "schema_global_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/tests/data/test_agent/add_manual", "files": [{"name": "add_manual_use_cases.yaml", "tag": "add", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/tests/data/test_database", "files": [{"name": "schema_global_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/core/tests/data/test_rootcheck", "files": [{"name": "schema_rootcheck_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/rbac", "files": [{"name": "auth_context.py", "tag": "auth", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "decorators.py", "tag": "decorators", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "orm.py", "tag": "orm", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "preprocessor.py", "tag": "preprocessor", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "utils.py", "tag": "utils", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/rbac/default", "files": [{"name": "policies.yaml", "tag": "policies", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "relationships.yaml", "tag": "relationships", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "roles.yaml", "tag": "roles", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "rules.yaml", "tag": "rules", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "users.yaml", "tag": "users", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/rbac/tests", "files": [{"name": "utils.py", "tag": "utils", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/rbac/tests/data", "files": [{"name": "schema_security_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/tests", "files": [{"name": "util.py", "tag": "util", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/tests/data", "files": [{"name": "schema_ciscat_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "schema_global_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "schema_mitre_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "schema_syscheck_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "schema_syscollector_000.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "schema_tasks_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/tests/data/security", "files": [{"name": "policies_test_cases.yml", "tag": "policies", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "rbac_catalog.yml", "tag": "rbac", "tests": ["test_rbac_black_active_response_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_black_cdb_list_endpoints.tavern.yaml", "test_rbac_black_ciscat_endpoints.tavern.yaml", "test_rbac_black_cluster_endpoints.tavern.yaml", "test_rbac_black_decoder_endpoints.tavern.yaml", "test_rbac_black_event_endpoints.tavern.yaml", "test_rbac_black_experimental_endpoints.tavern.yaml", "test_rbac_black_logtest_endpoints.tavern.yaml", "test_rbac_black_manager_endpoints.tavern.yaml", "test_rbac_black_mitre_endpoints.tavern.yaml", "test_rbac_black_overview_endpoints.tavern.yaml", "test_rbac_black_rootcheck_endpoints.tavern.yaml", "test_rbac_black_rule_endpoints.tavern.yaml", "test_rbac_black_sca_endpoints.tavern.yaml", "test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_black_syscheck_endpoints.tavern.yaml", "test_rbac_black_syscollector_endpoints.tavern.yaml", "test_rbac_black_task_endpoints.tavern.yaml", "test_rbac_white_active_response_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml", "test_rbac_white_all_endpoints.tavern.yaml", "test_rbac_white_cdb_list_endpoints.tavern.yaml", "test_rbac_white_ciscat_endpoints.tavern.yaml", "test_rbac_white_cluster_endpoints.tavern.yaml", "test_rbac_white_decoder_endpoints.tavern.yaml", "test_rbac_white_event_endpoints.tavern.yaml", "test_rbac_white_experimental_endpoints.tavern.yaml", "test_rbac_white_logtest_endpoints.tavern.yaml", "test_rbac_white_manager_endpoints.tavern.yaml", "test_rbac_white_mitre_endpoints.tavern.yaml", "test_rbac_white_overview_endpoints.tavern.yaml", "test_rbac_white_rootcheck_endpoints.tavern.yaml", "test_rbac_white_rule_endpoints.tavern.yaml", "test_rbac_white_sca_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_rbac_white_syscheck_endpoints.tavern.yaml", "test_rbac_white_syscollector_endpoints.tavern.yaml", "test_rbac_white_task_endpoints.tavern.yaml"]}, {"name": "role_policies_test_cases.yml", "tag": "role", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "roles_test_cases.yml", "tag": "roles", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "sanitize_policies.yaml", "tag": "sanitize", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "schema_security_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "users_roles_test_cases.yml", "tag": "users", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "users_test_cases.yml", "tag": "users", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "framework/wazuh/tests/data/security/default", "files": [{"name": "default_cases.yml", "tag": "default", "tests": ["test_default_endpoints.tavern.yaml"]}, {"name": "migration_policies.yml", "tag": "migration", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "mock_relationships.yml", "tag": "mock", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "new_default_policies.yml", "tag": "new", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration", "files": [{"name": "common.yaml", "tag": "common", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "conftest.py", "tag": "conftest", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "run_tests.py", "tag": "run", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "tavern_utils.py", "tag": "tavern", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "test_active_response_endpoints.tavern.yaml", "tag": "test", "tests": ["test_active_response_endpoints.tavern.yaml"]}, {"name": "test_agent_DELETE_endpoints.tavern.yaml", "tag": "test", "tests": ["test_agent_DELETE_endpoints.tavern.yaml"]}, {"name": "test_agent_GET_endpoints.tavern.yaml", "tag": "test", "tests": ["test_agent_GET_endpoints.tavern.yaml"]}, {"name": "test_agent_POST_endpoints.tavern.yaml", "tag": "test", "tests": ["test_agent_POST_endpoints.tavern.yaml"]}, {"name": "test_agent_PUT_endpoints.tavern.yaml", "tag": "test", "tests": ["test_agent_PUT_endpoints.tavern.yaml"]}, {"name": "test_cdb_list_endpoints.tavern.yaml", "tag": "test", "tests": ["test_cdb_list_endpoints.tavern.yaml"]}, {"name": "test_ciscat_endpoints.tavern.yaml", "tag": "test", "tests": ["test_ciscat_endpoints.tavern.yaml"]}, {"name": "test_cluster_endpoints.tavern.yaml", "tag": "test", "tests": ["test_cluster_endpoints.tavern.yaml"]}, {"name": "test_decoder_endpoints.tavern.yaml", "tag": "test", "tests": ["test_decoder_endpoints.tavern.yaml"]}, {"name": "test_default_endpoints.tavern.yaml", "tag": "test", "tests": ["test_default_endpoints.tavern.yaml"]}, {"name": "test_event_endpoints.tavern.yaml", "tag": "test", "tests": ["test_event_endpoints.tavern.yaml"]}, {"name": "test_experimental_endpoints.tavern.yaml", "tag": "test", "tests": ["test_experimental_endpoints.tavern.yaml"]}, {"name": "test_logtest_endpoints.tavern.yaml", "tag": "test", "tests": ["test_logtest_endpoints.tavern.yaml"]}, {"name": "test_manager_endpoints.tavern.yaml", "tag": "test", "tests": ["test_manager_endpoints.tavern.yaml"]}, {"name": "test_mitre_endpoints.tavern.yaml", "tag": "test", "tests": ["test_mitre_endpoints.tavern.yaml"]}, {"name": "test_overview_endpoints.tavern.yaml", "tag": "test", "tests": ["test_overview_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_active_response_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_active_response_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_agent_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_agent_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_cdb_list_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_cdb_list_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_ciscat_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_ciscat_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_cluster_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_cluster_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_decoder_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_decoder_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_event_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_event_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_experimental_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_experimental_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_logtest_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_logtest_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_manager_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_manager_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_mitre_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_mitre_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_overview_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_overview_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_rootcheck_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_rootcheck_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_rule_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_rule_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_sca_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_sca_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_security_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_security_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_syscheck_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_syscheck_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_syscollector_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_syscollector_endpoints.tavern.yaml"]}, {"name": "test_rbac_black_task_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_black_task_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_active_response_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_active_response_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_agent_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_all_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_all_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_cdb_list_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_cdb_list_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_ciscat_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_ciscat_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_cluster_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_cluster_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_decoder_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_decoder_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_event_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_event_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_experimental_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_experimental_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_logtest_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_logtest_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_manager_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_manager_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_mitre_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_mitre_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_overview_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_overview_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_rootcheck_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_rootcheck_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_rule_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_rule_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_sca_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_sca_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_security_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_security_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_syscheck_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_syscheck_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_syscollector_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_syscollector_endpoints.tavern.yaml"]}, {"name": "test_rbac_white_task_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rbac_white_task_endpoints.tavern.yaml"]}, {"name": "test_rootcheck_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rootcheck_endpoints.tavern.yaml"]}, {"name": "test_rule_endpoints.tavern.yaml", "tag": "test", "tests": ["test_rule_endpoints.tavern.yaml"]}, {"name": "test_sca_endpoints.tavern.yaml", "tag": "test", "tests": ["test_sca_endpoints.tavern.yaml"]}, {"name": "test_security_DELETE_endpoints.tavern.yaml", "tag": "test", "tests": ["test_security_DELETE_endpoints.tavern.yaml"]}, {"name": "test_security_GET_endpoints.tavern.yaml", "tag": "test", "tests": ["test_security_GET_endpoints.tavern.yaml"]}, {"name": "test_security_POST_endpoints.tavern.yaml", "tag": "test", "tests": ["test_security_POST_endpoints.tavern.yaml"]}, {"name": "test_security_PUT_endpoints.tavern.yaml", "tag": "test", "tests": ["test_security_PUT_endpoints.tavern.yaml"]}, {"name": "test_syscheck_endpoints.tavern.yaml", "tag": "test", "tests": ["test_syscheck_endpoints.tavern.yaml"]}, {"name": "test_syscollector_endpoints.tavern.yaml", "tag": "test", "tests": ["test_syscollector_endpoints.tavern.yaml"]}, {"name": "test_task_endpoints.tavern.yaml", "tag": "test", "tests": ["test_task_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env", "files": [{"name": "docker-compose.yml", "tag": "docker-compose", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/base/agent", "files": [{"name": "entrypoint.sh", "tag": "entrypoint", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/base/cti", "files": [{"name": "http_server.py", "tag": "http", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/base/haproxy-lb", "files": [{"name": "entrypoint.sh", "tag": "entrypoint", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/base/manager", "files": [{"name": "entrypoint.sh", "tag": "entrypoint", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/active/agent/configuration_files", "files": [{"name": "agent3_config.sh", "tag": "agent3", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/agent/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/all/manager/configuration_files", "files": [{"name": "base_security_test.sql", "tag": "base", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/base/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "legacy_healthcheck.py", "tag": "legacy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/base/manager/config/api/configuration", "files": [{"name": "api.yaml", "tag": "api", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/base/manager/config/api/configuration/security", "files": [{"name": "security.yaml", "tag": "security", "tests": ["test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_security_DELETE_endpoints.tavern.yaml", "test_security_GET_endpoints.tavern.yaml", "test_security_POST_endpoints.tavern.yaml", "test_security_PUT_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/base/manager/configuration_files", "files": [{"name": "base_security_test.sql", "tag": "base", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/base/manager/configuration_files/master_only", "files": [{"name": "agent_groups.yaml", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "agent_info.yaml", "tag": "agent", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_rbac_black_agent_endpoints.tavern.yaml", "test_rbac_white_agent_endpoints.tavern.yaml"]}, {"name": "update_agent_info.py", "tag": "update", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/base/manager/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/cluster/manager/configuration_files", "files": [{"name": "entrypoint.sh", "tag": "entrypoint", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/decoder/manager/configuration_files", "files": [{"name": "manager.sh", "tag": "manager", "tests": ["test_manager_endpoints.tavern.yaml", "test_rbac_black_manager_endpoints.tavern.yaml", "test_rbac_white_manager_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/experimental/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "legacy_healthcheck.py", "tag": "legacy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/experimental/manager/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/manager/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "legacy_healthcheck.py", "tag": "legacy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/manager/manager/configuration_files", "files": [{"name": "manager.sh", "tag": "manager", "tests": ["test_manager_endpoints.tavern.yaml", "test_rbac_black_manager_endpoints.tavern.yaml", "test_rbac_white_manager_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/manager/manager/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/active", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_active_response_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_active_response_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/agent", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_agent_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_agent_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/cdb", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_cdb_list_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_cdb_list_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/ciscat", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_ciscat_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_ciscat_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/cluster", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_cluster_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/decoder", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_decoder_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_decoder_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/event", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_event_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_event_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/experimental", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_experimental_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_experimental_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/logtest", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_logtest_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/manager", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_manager_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_manager_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/mitre", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_mitre_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/overview", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_overview_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_overview_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/rootcheck", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_rootcheck_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_rootcheck_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/rule", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_rule_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_rule_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/sca", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_ciscat_endpoints.tavern.yaml", "test_rbac_black_sca_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_ciscat_endpoints.tavern.yaml", "test_rbac_white_sca_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/security", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_security_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_security_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/syscheck", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_syscheck_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_syscheck_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/syscollector", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_syscollector_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_syscollector_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rbac/task", "files": [{"name": "black_config.yaml", "tag": "black", "tests": ["test_rbac_black_task_endpoints.tavern.yaml"]}, {"name": "white_config.yaml", "tag": "white", "tests": ["test_rbac_white_task_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/rule/manager/configuration_files", "files": [{"name": "manager.sh", "tag": "manager", "tests": ["test_manager_endpoints.tavern.yaml", "test_rbac_black_manager_endpoints.tavern.yaml", "test_rbac_white_manager_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/sca/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "legacy_healthcheck.py", "tag": "legacy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/security/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "legacy_healthcheck.py", "tag": "legacy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/security/manager/configuration_files", "files": [{"name": "schema_security_test.sql", "tag": "schema", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "security_config.sh", "tag": "security", "tests": ["test_rbac_black_security_endpoints.tavern.yaml", "test_rbac_white_security_endpoints.tavern.yaml", "test_security_DELETE_endpoints.tavern.yaml", "test_security_GET_endpoints.tavern.yaml", "test_security_POST_endpoints.tavern.yaml", "test_security_PUT_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/security/manager/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/syscheck/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "legacy_healthcheck.py", "tag": "legacy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/syscheck/manager/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/syscollector/agent/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "legacy_healthcheck.py", "tag": "legacy", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/configurations/syscollector/manager/healthcheck", "files": [{"name": "healthcheck.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}, {"path": "api/test/integration/env/tools", "files": [{"name": "healthcheck_utils.py", "tag": "healthcheck", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "print_active_agents.py", "tag": "print", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}, {"name": "xml_parser.py", "tag": "xml", "tests": ["test_agent_DELETE_endpoints.tavern.yaml", "test_agent_GET_endpoints.tavern.yaml", "test_agent_POST_endpoints.tavern.yaml", "test_agent_PUT_endpoints.tavern.yaml", "test_cluster_endpoints.tavern.yaml"]}]}]