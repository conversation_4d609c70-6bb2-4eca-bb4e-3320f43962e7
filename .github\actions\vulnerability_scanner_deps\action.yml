name: "Vulnerability scanner dependencies"
description: "Download and compiles the dependencies for the vulnerability scanner modules"

runs:
  using: "composite"
  steps:
      - name: Dependencies for local execution
        if: env.ACT # Only run for local execution
        shell: bash
        run: |

          # Obtain a copy of the signing key
          wget -O - https://apt.kitware.com/keys/kitware-archive-latest.asc 2>/dev/null | gpg --dearmor - | sudo tee /usr/share/keyrings/kitware-archive-keyring.gpg >/dev/null

          # Add the repository to your sources list
          echo 'deb [signed-by=/usr/share/keyrings/kitware-archive-keyring.gpg] https://apt.kitware.com/ubuntu/ jammy main' | sudo tee /etc/apt/sources.list.d/kitware.list >/dev/null

          # Update packages
          sudo apt-get update
          sudo apt-get install -y cmake

      - name: General dependencies
        shell: bash
        run: |
          sudo apt-get update
          sudo apt-get install -y libc6-dbg

      - name: Build external deps
        run: |
          cd src
          make deps TARGET=server -j2
          make build_gtest TARGET=server -j2
          make build_benchmark TARGET=server -j2
          make libwazuhext.so TARGET=server -j2
          # Only the schemas are required from syscollector
          mkdir -p wazuh_modules/syscollector/build && cmake -DTARGET=server -B wazuh_modules/syscollector/build -S wazuh_modules/syscollector && make -C wazuh_modules/syscollector/build compile_schemas compile_schemas_deltas -j2
        shell: bash

      - name: Build http-request
        run: |
          cd src
          SRC_FOLDER=$(pwd)

          cd shared_modules/http-request
          mkdir -p build && cd build
          cmake .. -DCMAKE_PROJECT_NAME=http-request -DSRC_FOLDER=${SRC_FOLDER} && make -j2
        shell: bash

      - name: Build keystore
        run: |
          cd src
          SRC_FOLDER=$(pwd)

          cd shared_modules/keystore
          mkdir -p build && cd build
          cmake .. -DCMAKE_PROJECT_NAME=keystore -DSRC_FOLDER=${SRC_FOLDER} && make -j2
        shell: bash
