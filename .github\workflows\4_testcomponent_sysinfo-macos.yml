name: Syscollector test on macOS

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: macos-13
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v3
      # Build wazuh agent for macOS.
      - name: Build wazuh agent for macOS
        run: |
          make deps -C src TARGET=agent -j4
          make -C src build_syscollector TARGET=agent -j4
      - name: Install dependencies
        run: |
          brew install wget
          pip3 install -r src/data_provider/qa/requirements.txt
      - name: Install macports package manager
        run: |
          wget https://github.com/macports/macports-base/releases/download/v2.8.1/MacPorts-2.8.1-13-Ventura.pkg
          sudo installer -pkg MacPorts-2.8.1-13-Ventura.pkg -target /
          rm -rf MacPorts-2.8.1-13-Ventura.pkg
      - name: Install port
        run: |
          sudo /opt/local/bin/port selfupdate
          sudo /opt/local/bin/port -b install nano
      - name: Run tests
        run: |
          cd src/data_provider
          sudo python3 -m pytest -vv qa/
