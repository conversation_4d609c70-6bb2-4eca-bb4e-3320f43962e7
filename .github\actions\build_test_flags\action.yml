name: "Build Wazuh with test flags"
description: "This action builds Wazuh on Ubuntu Linux/Windows"
inputs:
   target:
     required: true
     description: "Target specifies whether the build is for Linux or Windows"
runs:
  using: "composite"
  steps:
    - name: Set permissions and prepare the library
      shell: bash
      run: |
        chmod +x .github/actions/legacy_unit_tests.sh
    - name: Build Wazuh
      shell: bash
      run: |
        source .github/actions/legacy_unit_tests.sh
        build_wazuh_test_flags ${{ inputs.target }}
