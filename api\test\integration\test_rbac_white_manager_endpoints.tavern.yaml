---
test_name: GET /manager/configuration

stages:

  - name: Request manager configuration (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

---
test_name: GET /manager/configuration/validation

stages:

  - name: Request manager configuration validation (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration/validation"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/configuration/{component}/{configuration}

stages:

  - name: Request manager component configuration (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration/agentless/agentless"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/info

stages:

  - name: Request manager info (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/info"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/logs

stages:

  - name: Request manager logs (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/logs"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/logs/summary

stages:

  - name: Request manager logs summary (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/logs/summary"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/daemons/stats

stages:

  - name: Request daemons statistics (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats

stages:

  - name: Request manager stats (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        date: "2019-08-27"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/analysisd

stages:

  - name: Request manager analysisd stats (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/analysisd"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/hourly

stages:

  - name: Request manager hourly stats (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/hourly"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/remoted

stages:

  - name: Request manager remoted stats (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/remoted"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/stats/weekly

stages:

  - name: Request manager weekly stats (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/stats/weekly"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/status

stages:

  - name: Request manager status (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /manager/api/config

stages:

  - name: Request API config (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/api/config"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

---
test_name: PUT /manager/configuration

stages:

  - name: Upload a valid configuration (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/configuration"
      method: PUT
      data: "{valid_ossec_conf:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      status_code: 200

---
test_name: PUT /manager/restart

stages:

  - name: Restart manager (Denied) (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/manager/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied
