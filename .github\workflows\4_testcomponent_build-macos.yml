name: macOS compilation test

on:
  pull_request:
    paths:
      - "src/**"
      - ".github/workflows/4_testcomponent_build-macos.yml"

jobs:
  build-ventura:
    runs-on: macos-13
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v3
      - name: Build wazuh agent for macOS 13
        run: |
          make deps -C src TARGET=agent -j4
          make -C src TARGET=agent -j4
  build-sonoma:
    runs-on: macos-14
    steps:
      - name: Checkout Rep<PERSON>
        uses: actions/checkout@v3
      - name: Build wazuh agent for macOS 14
        run: |
          make deps -C src TARGET=agent -j3
          make -C src TARGET=agent -j3
