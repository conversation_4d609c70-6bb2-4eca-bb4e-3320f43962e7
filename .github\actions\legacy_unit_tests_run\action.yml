name: "Legacy unit tests run for Wazuh"
description: "This action runs Wazuh unit tests on Ubuntu Linux/Windows"
inputs:
   target:
     required: true
     description: "Target specifies whether the build is for Linux or Windows"
runs:
  using: "composite"
  steps:
    - name: Set permissions and prepare the library
      shell: bash
      run: |
        chmod +x .github/actions/legacy_unit_tests.sh
    - name: Build Wazuh Agent Unit Tests
      shell: bash
      run: |
        source .github/actions/legacy_unit_tests.sh
        run_wazuh_unit_tests ${{ inputs.target }}
    - name: Format and Display Test Results
      shell: bash
      run: |
        source .github/actions/legacy_unit_tests.sh
        format_display_test_results ${{ inputs.target }}
    - name: Format and Display Test Coverage
      if: ${{ inputs.target != 'winagent' }}
      shell: bash
      run: |
        source .github/actions/legacy_unit_tests.sh
        format_display_test_coverage ${{ inputs.target }}
