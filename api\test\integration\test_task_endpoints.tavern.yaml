---
test_name: GET /tasks/status

stages:

  - name: Get all existent tasks (At this point there are no tasks created)
    request: &get_tasks
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Upgrade old agents to create tasks
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        force: True
        agents_list: '005,006'
        upgrade_version: '4.2.4'
    response:
      status_code: 200
      json:
        data:
          affected_items:
            - agent: '005'
              task_id: !anyint
            - agent: '006'
              task_id: !anyint
          total_affected_items: 2
          total_failed_items: 0
          failed_items: []
        message: !anystr

  - name: Upgrade agents to create tasks (Invalid version)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/agents/upgrade"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        force: True
        agents_list: '007'
        upgrade_version: '2.0.0'
    response:
      status_code: 200
      json:
        data:
          affected_items:
            - agent: '007'
              task_id: 3
          total_affected_items: 1
          total_failed_items: 0
          failed_items: []
        message: !anystr

  - name: Get all existent tasks (Limit 1)
    request:
      verify: False
      <<: *get_tasks
      params:
        limit: 1
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: &task
            - agent_id: !anystr
              create_time: !anystr
              node: !anystr
              status: !anystr
              last_update_time: !anystr
              command: "upgrade"
              module: "upgrade_module"
              task_id: 2
          failed_items: []
          total_affected_items: 3
          total_failed_items: 0

  - name: Get all existent tasks (Limit 2)
    request:
      verify: False
      <<: *get_tasks
      params:
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - agent_id: !anystr
              create_time: !anystr
              node: !anystr
              status: !anystr
              last_update_time: !anystr
              command: "upgrade"
              module: "upgrade_module"
              task_id: 1
            - <<: *task
          failed_items: []
          total_affected_items: 3
          total_failed_items: 0

  - name: Try to get tasks using select parameter
    request:
      verify: False
      <<: *get_tasks
      params:
        select: "task_id"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: "task_id"

  - name: Try to get tasks using select parameter with more than a field
    request:
      verify: False
      <<: *get_tasks
      params:
        select: "task_id,node,module"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_select_key_affected_items
          extra_kwargs:
            select_key: "task_id,node,module"

  - name: Try to get tasks using select parameter with incorrect field
    request:
      verify: False
      <<: *get_tasks
      params:
        select: "incorrect_field"
    response:
      status_code: 400

  - name: Try to get all tasks using sort parameter with incorrect field
    request:
      verify: False
      <<: *get_tasks
      params:
        sort: "-incorrect_field"
    response:
      status_code: 400

  - name: Try to get all tasks using sort and select parameter
    request:
      verify: False
      <<: *get_tasks
      params:
        select: "task_id"
        sort: "-task_id"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "task_id"
            reverse: True

  - name: Verify that query parameter works as expected
    request:
      verify: False
      <<: *get_tasks
      params:
        q: "agent_id=005"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "agent_id"
            expected_values: "005"

  - name: Verify that query parameter works as expected
    request:
      verify: False
      <<: *get_tasks
      params:
        q: "create_time>2021-07-01"
    response:
      status_code: 200
      json:
        error: 0
        data:
          failed_items: [ ]
          total_affected_items: 3
          total_failed_items: 0

  - name: Verify that query parameter works as expected
    request:
      verify: False
      <<: *get_tasks
      params:
        q: "agent_id>004;agent_id<006"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "agent_id"
            expected_values: "005"

  - name: Verify that query parameter works as expected (using non-existent agent_id)
    request:
      verify: False
      <<: *get_tasks
      params:
        q: "agent_id=099"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Verify that query parameter works as expected when using multiple values
    request:
      verify: False
      <<: *get_tasks
      params:
        q: "agent_id=005;module=upgrade_module;command=upgrade"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "agent_id"
            expected_values: "005"
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "module"
            expected_values: "upgrade_module"
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "command"
            expected_values: "upgrade"

  - name: Verify that query parameter works as expected (wrong_field)
    request:
      verify: False
      <<: *get_tasks
      params:
        q: "wrong_field=INVALID"
    response:
      status_code: 400

---
test_name: GET /tasks/status (Filters)

stages:

  - name: Get specified tasks
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        tasks_list: 3,2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *task
              status: !anystr
              last_update_time: !anystr
              agent_id: !anystr
              task_id: 2
            - <<: *task
              status: !anystr
              last_update_time: !anystr
              agent_id: !anystr
              task_id: 3
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Get specified tasks, agent_id (005)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: ["005","000"]
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *task
              status: !anystr
              last_update_time: !anystr
              agent_id: "005"
              task_id: !anyint
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Get specified tasks, agent_id (000)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        agents_list: ["000"]
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Get all existent tasks with module=upgrade_module
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        module: "upgrade_module"
    response:
      status_code: 200
      json: &task_module_response
        error: 0
        data:
          affected_items:
            - <<: *task
              status: !anystr
              last_update_time: !anystr
              task_id: 1
            - <<: *task
              status: !anystr
              last_update_time: !anystr
              task_id: 2
            - <<: *task
              status: !anystr
              last_update_time: !anystr
              task_id: 3
          failed_items: []
          total_affected_items: 3
          total_failed_items: 0

  - name: Get all existent tasks with command=upgrade
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        command: "upgrade"
    response:
      status_code: 200
      json: *task_module_response

  - name: Get all existent tasks with node=worker2
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        node: "worker2"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "node"
            expected_values: "worker2"
            empty_response_possible: True

  - name: Get all existent tasks with status=In progress
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/tasks/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        status: "In progress"
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_expected_value
          extra_kwargs:
            key: "status"
            expected_values: "In progress"
