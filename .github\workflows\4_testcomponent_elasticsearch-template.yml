name: wazuh-template for Elasticsearch test

# if the file wazuh/extensions/elasticsearch/7.x/wazuh-template.json is modified launch the test or is a part of
# a pull request

on:
  push:
    paths:
      - "extensions/elasticsearch/7.x/**"

jobs:
  wazuh-template-elasticsearch:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python 3.11
        uses: actions/setup-python@v1
        with:
          python-version: 3.11
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r extensions/elasticsearch/7.x/qa/requirements.txt
      - name: Test wazuh-template for Elasticsearch
        run: |
          cd extensions/elasticsearch/7.x
          python -m pytest -vv qa/
