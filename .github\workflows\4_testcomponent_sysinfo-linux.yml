name: Syscollector test on Ubuntu

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
        with:
          submodules: recursive
      - name: "Install a compatible CMake"
        uses: ./.github/actions/reinstall_cmake
      # Build wazuh agent for linux.
      - name: Build wazuh agent for linux
        run: |
          make deps -C src TARGET=agent -j2
          make -C src build_syscollector TARGET=agent -j2
      - name: Install dependencies
        run: |
          pip install -r src/data_provider/qa/requirements.txt
      - name: Run tests
        run: |
          cd src/data_provider
          python -m pytest -vv qa/
