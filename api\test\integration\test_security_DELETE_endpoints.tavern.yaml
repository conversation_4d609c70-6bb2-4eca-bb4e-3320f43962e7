---
test_name: DELETE /security/roles/{role_id}

stages:

  # DELETE /security/roles/{role_id}
  - name: Try to delete a existent role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 102
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 102
              name: !anystr
              policies: !anything
              rules: !anything
          total_affected_items: 1

  # DELETE /security/roles/1
  - name: Try to delete the admin role of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 1
    response:
      status_code: 200
      json: &error
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: !anyint
              id: !anything
          total_affected_items: 0
          total_failed_items: !anyint

  # DELETE /security/roles/{non-existent role}
  - name: Try to delete a non-existent role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 999
    response:
      status_code: 200
      json:
        <<: *error

---
test_name: DELETE /security/roles/{role_id}/policies/{policy_id}

stages:

  # DELETE /security/roles/{role_id}/policies/{policy_id}
  - name: Try to delete a existent role-policy
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/103/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 104,109
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 4010
              id: !anything
          total_affected_items: 1
          total_failed_items: 1

  # DELETE /security/roles/{non-existent role_id}/policies/{non-existent policy_id}
  - name: Try to delete a non-existent role-policy
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/999/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 999
    response:
      status_code: 404
      json:
        error: 4002

  # DELETE /security/roles/{non-existent role_id}/policies/{policy_id}
  - name: Try to delete a non-existent role-policy (role non-existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/999/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 3
    response:
      status_code: 404
      json:
        error: 4002

  # DELETE /security/roles/{role_id}/policies/{policy_id}.
  - name: Try to delete a non-existent role-policy (default role, policy non-existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/3/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 999
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4008
              id:
                - 3
          total_affected_items: 0
          total_failed_items: 1

  # DELETE /security/roles/{role_id}/policies/{policy_id}.
  - name: Try to delete a non-existent role-policy from a role (policy non-existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/100/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 999
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4007
              id:
                - 999
          total_affected_items: 0
          total_failed_items: 1
---
test_name: DELETE /security/roles/{role_id}/rules

stages:

  # DELETE /security/roles/{role_id}/rules/{rule_id}
  - name: Try to delete a existent role-rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/103/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 103,109
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items:
            - error:
                code: 4022
              id: !anything
          total_affected_items: 1
          total_failed_items: 1

  # DELETE /security/roles/{non-existent role_id}/rules/{non-existent policy_id}
  - name: Try to delete a non-existent role-rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/999/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 999
    response:
      status_code: 404
      json:
        error: 4002

  # DELETE /security/roles/{non-existent role_id}/rules/{rule_id}
  - name: Try to delete a non-existent role-rule (role non-existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/999/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 100
    response:
      status_code: 404
      json:
        error: 4002

  # DELETE /security/roles/{role_id}/rules/{rule_id}
  - name: Try to delete a non-existent role-rule (reserved role)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/3/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: [ ]
          failed_items:
            - error:
                code: 4022
              id: !anything
          total_failed_items: 1

  # DELETE /security/roles/{role_id}/rules/{rule_id}
  - name: Try to delete a non-existent role-rule (rule non-existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles/100/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 999
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4022
              id: !anything
          total_failed_items: 1

---
test_name: DELETE /security/user/{user_id}/roles/{role_id}

stages:

  # DELETE /security/user/{user_id}/roles/{role_id}
  - name: Try to delete a existent user-role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/101/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 104,103
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  # DELETE /security/user/{user_id}/roles/{role_id}
  - name: Try to delete a non-existent user-role
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/987/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 104
    response:
      status_code: 404
      json:
        error: 5001

  # DELETE /security/user/{user_id}/roles/{role_id}
  - name: Try to delete a non-existent user-role (role non-existent)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users/105/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 999
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4002
              id:
              - 999
          total_affected_items: 0
          total_failed_items: 1

---
test_name: DELETE /security/policies/{policy_id}

stages:

  # DELETE /security/policies/{role_id}
  - name: Try to delete a existent policy
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 104
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
              name: !anystr
              policy: !anything
              roles: !anything
          total_affected_items: 1

  # DELETE /security/policies/1
  - name: Try to delete the admin policy of the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 1
    response:
      status_code: 200
      json:
        <<: *error

  # DELETE /security/roles/{non-existent role}
  - name: Try to delete an inexistent policy
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 999
    response:
      status_code: 200
      json:
        <<: *error
---
test_name: DELETE /security/roles

stages:

  # DELETE /security/roles
  - name: Try to delete one existent role and no existent one
    request: &delete_roles_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: 999,103
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 103
              name: !anystr
              policies: !anything
              rules: !anything
          failed_items:
            - error:
                code: 4002
              id:
                - 999
          total_affected_items: 1
          total_failed_items: 1

  # DELETE /security/roles
  - name: Try to delete all roles of the system
    request:
      verify: False
      <<: *delete_roles_request
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  # DELETE /security/roles
  - name: Try to delete the administrator role
    request:
      verify: False
      <<: *delete_roles_request
      params:
        role_ids: 1
    response:
      status_code: 200
      json:
        <<: *error

  - name: Try to delete roles (invalid role_ids)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        role_ids: invalid
    response:
      status_code: 400

---
test_name: DELETE /security/policies

stages:

  # DELETE /security/policies
  - name: Try to delete two existent policies
    request: &delete_all_policies_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: 100,101
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 100
              name: !anystr
              policy: !anything
              roles: !anything
            - id: 101
              name: !anystr
              policy: !anything
              roles: !anything
          total_affected_items: 2

  # DELETE /security/policies
  - name: Try to delete all policies of the system
    request:
      verify: False
      <<: *delete_all_policies_request
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          total_affected_items: 0

  - name: Try to delete the administrator policy
    request:
      verify: False
      <<: *delete_all_policies_request
      params:
        policy_ids: 1
    response:
      status_code: 200
      json:
        <<: *error

  - name: Try to delete policies (invalid policy_ids)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        policy_ids: none
    response:
      status_code: 400

---
test_name: DELETE /security/users

stages:

  - name: Delete a non-existent user
    delay_before: 10
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 200
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 5001
              id:
                - 200
          total_affected_items: 0
          total_failed_items: 1

  - name: Delete the current user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 99
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 5004
              id:
                - 99
          total_affected_items: 0
          total_failed_items: 1

  - name: Delete an admin user (wazuh-wui)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 2
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 5004
              id:
                - 2
          total_affected_items: 0
          total_failed_items: 1

  - name: Delete an existent user (with body)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 101
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 101
          total_affected_items: 1

  - name: Delete an existent user
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: 104
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 104
          total_affected_items: 1

  - name: Delete all users in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 100
              username: administrator
              roles:
                - 100
                - 101
            - id: 102
              username: ossec
              roles:
                - 101
                - 104
            - id: 103
              username: python
              roles:
                - 101
            - id: 105
              username: guest
              roles: []
          total_affected_items: 4
          total_failed_items: 0
          failed_items: []

  - name: Delete all users in the system
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/users"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        user_ids: none
    response:
      status_code: 400

---
test_name: DELETE /security/rules

stages:

  - name: Delete a non-existent rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 200
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4022
              id:
                - 200
          total_affected_items: 0
          total_failed_items: 1

  - name: Delete an admin rule (wui-admin)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: []
          failed_items:
            - error:
                code: 4008
              id:
                - 1
          total_affected_items: 0
          total_failed_items: 1

  - name: Delete an existent rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: 100
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - id: 100
          total_affected_items: 1

  - name: Delete all rules in the system
    delay_after: 1
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: all
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: # Admin rules cannot be deleted
            - id: 101
            - id: 102
            - id: 103
            - id: 104
            - id: 105
          total_affected_items: 5
          total_failed_items: 0
          failed_items: []

---
test_name: DELETE /security/rules (invalid)

stages:

  - name: Delete all rules in the system (invalid)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
      params:
        rule_ids: none
    response:
      status_code: 400

---
test_name: PUT /security/config

stages:

  # PUT /security/config
  - name: Change security configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: PUT
      json:
        auth_token_exp_timeout: 3000
    response:
      status_code: 200

---
test_name: RESTORE /security/config

stages:

  # DELETE /security/roles
  - name: Restore default configuration
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: DELETE
    response:
      status_code: 200

---
test_name: CHECK /security/config

stages:

  - name: Get security configuration to check if DELETE method worked correctly
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/config"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200
      verify_response_with:
        - function: tavern_utils:test_response_is_different
          extra_kwargs:
            response_value: auth_token_exp_timeout
            unexpected_value: 3000

---
test_name: CLEANER DELETE /security/{policies}

stages:

  - name: Try to delete all policies
    delay_before: 10
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/policies"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        policy_ids: all
      method: DELETE
    response:
      status_code: 200

---
test_name: CLEANER DELETE /security/{roles}

stages:

  # DELETE /security/roles
  - name: Try to delete all roles
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/security/roles"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        role_ids: all
      method: DELETE
    response:
      status_code: 200
