---
test_name: GET /rules

stages:

  - name: Try to show the rules of the system
    request: &general_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: &rules_response
            - description: !anystr
              details: !anything
              filename: !anystr
              gdpr: !anything
              groups: !anything
              id: !anyint
              level: !anyint
              relative_dirname: !anystr
              pci_dss: !anything
              status: !anystr
              gpg13: !anything
              hipaa: !anything
              nist_800_53: !anything
              tsc: !anything
              mitre: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules of the system, offset = 0
    request:
      verify: False
      <<: *general_rules_request
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - description: !anystr
              filename: !anystr
              status: !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: !anyint
      save:
        json:
          second_rule_description: data.affected_items[1].description
          second_rule_filename: data.affected_items[1].filename
          second_rule_status: data.affected_items[1].status

  - name: Try to show the rules of the system, offset = 1
    request:
      verify: False
      <<: *general_rules_request
      params:
        limit: 2
        offset: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - description: "{second_rule_description}"
              filename: "{second_rule_filename}"
              status: "{second_rule_status}"
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules of the system, without limit
    request:
      verify: False
      <<: *general_rules_request
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Try to show the rules of the system with a existent state
    request:
      verify: False
      <<: *general_rules_request
      params:
        status: disabled
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to get all rules with limit = 0
    request:
      verify: False
      <<: *general_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Sort without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        sort: -filename
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "filename"
            reverse: True
      status_code: 200

  - name: Sort rules by multiple fields
    request:
      verify: False
      <<: *general_rules_request
      params:
        sort: level,filename
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "level,filename"
      status_code: 200

  - name: Show rules of the system using valid select
    request:
      verify: False
      <<: *general_rules_request
      params:
        # Single and nested select
        select: 'filename,details.noalert'
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'id,filename,details.noalert' # required_fields={'id'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: []
          total_failed_items: 0

  - name: Show rules of the system using invalid select
    request:
      verify: False
      <<: *general_rules_request
      params:
        select: 'noexists'
    response:
      status_code: 400
      json: &invalid_select
        error: 1724

  - name: Show rules of the system using select (one select is invalid)
    request:
      verify: False
      <<: *general_rules_request
      params:
        select: 'id,filename,noexists'
    response:
      status_code: 400
      json:
        <<: *invalid_select

  - name: Show lists using select and specifying a non-existent file in the filename parameter
    request:
      verify: False
      <<: *general_rules_request
      params:
        select: 'id'
        filename: 'invalid'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Search in rules
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        search: web
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      save:
        json:
          rule_id: data.affected_items[1].id

  - name: Test search
    request:
      verify: False
      <<: *general_rules_request
      params:
        search: web
        offset: 1
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - id: !int "{rule_id}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid parameter
    request:
      verify: False
      <<: *general_rules_request
      params:
        noexist: nothing
    response:
      status_code: 400

  - name: Invalid status
    request:
      verify: False
      <<: *general_rules_request
      params:
        status: aCtIvE
    response:
      status_code: 400

  - name: Invalid parameters - Extra fields
    request:
      verify: False
      <<: *general_rules_request
      params:
        status: enabled
        noexist: True
    response:
      status_code: 400

  - name: Filters status
    request:
      verify: False
      <<: *general_rules_request
      params:
        status: enabled
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter status without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        status: enabled
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Try to show the rules of the system with a non-existent state
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        status: notexist
    response:
      status_code: 400

  - name: Test q
    request:
      verify: False
      <<: *general_rules_request
      params:
        q: 'id=200'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
              id: 200
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Test complex q
    request:
      verify: False
      <<: *general_rules_request
      params:
        q: 'filename=0010-rules_config.xml;id>3;id<7;details.category=squid'
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
              id: !anyint
              details:
                category:
                  'squid'
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0


---
test_name: GET /rules filters

stages:

  - name: Filter group
    request:
      verify: False
      <<: *general_rules_request
      params:
        group: web
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter group without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        group: web
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Try to show the rules by levels
    request:
      verify: False
      <<: *general_rules_request
      params:
        level: 2
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter levels without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        level: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter levels without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        level: 2-3
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Try to show the rules by levels (invalid interval)
    request:
      verify: False
      <<: *general_rules_request
      params:
        level: 2-4-5
    response:
      status_code: 400

  - name: Filter relative_dirname
    request:
      verify: False
      <<: *general_rules_request
      params:
        relative_dirname: ruleset/rules
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter relative_dirname without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        relative_dirname: ruleset/rules
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter filename
    request:
      verify: False
      <<: *general_rules_request
      params:
        filename: 0015-ossec_rules.xml
        limit: 5
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - <<: *rules_response
            - <<: *rules_response
            - <<: *rules_response
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0
      save:
        json:
          pci_dss_value: data.affected_items[1].pci_dss[0]
          gdpr_value: data.affected_items[1].gdpr[0]
          gpg13_value: data.affected_items[1].gpg13[0]
          hipaa_value: data.affected_items[1].hipaa[0]
          nist-800-53_value: data.affected_items[1].nist_800_53[0]
          tsc_value: data.affected_items[1].tsc[0]
          mitre_value: data.affected_items[4].mitre[0]

  - name: Filter filename without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        filename: 0015-ossec_rules.xml
        offset: 1
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - pci_dss:
              - "{pci_dss_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter pci_dss
    request:
      verify: False
      <<: *general_rules_request
      params:
        pci_dss: "{pci_dss_value}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - pci_dss:
              - "{pci_dss_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter pci_dss without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        pci_dss: "{pci_dss_value}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter gdpr
    request:
      verify: False
      <<: *general_rules_request
      params:
        gdpr: "{gdpr_value}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - gdpr:
              - "{gdpr_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter gdpr without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        gdpr: "{gdpr_value}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter gpg13
    request:
      verify: False
      <<: *general_rules_request
      params:
        gpg13: "{gpg13_value}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - gpg13:
              - "{gpg13_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter gpg13 without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        gpg13: "{gpg13_value}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter hipaa
    request:
      verify: False
      <<: *general_rules_request
      params:
        hipaa: "{hipaa_value}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - hipaa:
              - "{hipaa_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter hipaa without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        hipaa: "{hipaa_value}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter nist-800-53
    request:
      verify: False
      <<: *general_rules_request
      params:
        nist-800-53: "{nist-800-53_value}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - nist_800_53:
              - "{nist-800-53_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter nist-800-53 without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        nist-800-53: "{nist-800-53_value}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter tsc
    request:
      verify: False
      <<: *general_rules_request
      params:
        tsc: "{tsc_value}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - tsc:
                - "{tsc_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter tsc without limit
    request:
      verify: False
      <<: *general_rules_request
      params:
        tsc: "{tsc_value}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Filter mitre
    request:
      <<: *general_rules_request
      params:
        mitre: "{mitre_value}"
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - mitre:
              - "{mitre_value}"
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter mitre without limit
    request:
      <<: *general_rules_request
      params:
        mitre: "{mitre_value}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Get distinct rules
    request:
      verify: False
      <<: *general_rules_request
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /rules/requirement/pci_dss

stages:

  - name: Try to show the rules by requirement pci_dss
    request: &pci_dss_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/pci_dss"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - !anystr
            - !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement pci_dss, limit and offset
    request:
      verify: False
      <<: *pci_dss_rules_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement pci_dss, limit = 0
    request:
      verify: False
      <<: *pci_dss_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Test limit
    request:
      verify: False
      <<: *pci_dss_rules_request
      params:
        limit: 500
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter sort
    request:
      verify: False
      <<: *pci_dss_rules_request
      params:
        sort: "-"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            reverse: True
            # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *pci_dss_rules_request
      params:
        search: 10
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *pci_dss_rules_request
      params:
        noexist: True
    response:
      status_code: 400

---
test_name: GET /rules/requirement/gdpr

stages:

  - name: Try to show the rules by requirement gdpr
    request: &gdpr_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/gdpr"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 2
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - !anystr
            - !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement gdpr, limit and offset
    request:
      verify: False
      <<: *gdpr_rules_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement gdpr, limit = 0
    request:
      verify: False
      <<: *gdpr_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *gdpr_rules_request
      params:
        limit: 500
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter reverse sort
    request:
      verify: False
      <<: *gdpr_rules_request
      params:
        limit: 500
        sort: "-"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            reverse: True
            # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *gdpr_rules_request
      params:
        search: 10
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *gdpr_rules_request
      params:
        noexist: True
    response:
      status_code: 400

---
test_name: GET /rules/requirement/gpg13

stages:

  - name: Try to show the rules by requirement gpg13
    request: &gpg13_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/gpg13"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint

  - name: Try to show the rules by requirement gpg13, limit and offset
    request:
      verify: False
      <<: *gpg13_rules_request
      params:
        limit: 2
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - !anystr
            - !anystr
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement gpg13, limit = 0
    request:
      verify: False
      <<: *gpg13_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *gpg13_rules_request
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter sort
    request:
      verify: False
      <<: *gpg13_rules_request
      params:
        limit: 500
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter reverse sort
    request:
      verify: False
      <<: *gpg13_rules_request
      params:
        sort: "+"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *gpg13_rules_request
      params:
        search: 10
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *gpg13_rules_request
      params:
        noexist: True
    response:
      status_code: 400

---
test_name: GET /rules/requirement/hipaa

stages:

  - name: Try to show the rules by requirement hipaa
    request: &hipaa_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/hipaa"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement hipaa, limit and offset
    request:
      verify: False
      <<: *hipaa_rules_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: !anyint

  - name: Try to show the rules by requirement hipaa, limit = 0
    request:
      verify: False
      <<: *hipaa_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *hipaa_rules_request
      params:
        sort: "-"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            reverse: True
            # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *hipaa_rules_request
      params:
        search: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *hipaa_rules_request
      params:
        noexist: True
    response:
      status_code: 400

---
test_name: GET /rules/requirement/nist-800-53

stages:

  - name: Try to show the rules by requirement nist-800-53
    request: &nist-800-53_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/nist-800-53"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement nist-800-53, limit and offset
    request:
      verify: False
      <<: *nist-800-53_rules_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement nist-800-53, limit = 0
    request:
      verify: False
      <<: *nist-800-53_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *nist-800-53_rules_request
      params:
        sort: "-"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            reverse: True
            # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *nist-800-53_rules_request
      params:
        search: 10
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *nist-800-53_rules_request
      params:
        noexist: True
    response:
      status_code: 400

---
test_name: GET /rules/requirement/tsc

stages:

  - name: Try to show the rules by requirement tsc
    request: &tsc_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/tsc"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement tsc, limit and offset
    request:
      verify: False
      <<: *tsc_rules_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement tsc, limit = 0
    request:
      verify: False
      <<: *tsc_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *tsc_rules_request
      params:
        sort: "+"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *tsc_rules_request
      params:
        search: 10
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *tsc_rules_request
      params:
        noexist: True
    response:
      status_code: 400

---
test_name: GET /rules/requirement/mitre

stages:

  - name: Try to show the rules by requirement mitre
    request: &mitre_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/requirement/mitre"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement mitre, limit and offset
    request:
      verify: False
      <<: *mitre_rules_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the rules by requirement mitre, limit = 0
    request:
      verify: False
      <<: *mitre_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *mitre_rules_request
      params:
        sort: "-"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            reverse: True
            # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *mitre_rules_request
      params:
        search: 10
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *mitre_rules_request
      params:
        noexist: True
    response:
      status_code: 400

---
test_name: GET /rules/groups

stages:

  - name: Try to show the rules groups
    request: &groups_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/groups"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the groups of rules, limit and offset
    request:
      verify: False
      <<: *groups_rules_request
      params:
        offset: 0
        limit: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show the groups of rules, limit = 0
    request:
      verify: False
      <<: *groups_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *groups_rules_request
      params:
        sort: "+"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          # No `key` parameter needed as we are sorting a list of strings
      status_code: 200

  - name: Filter search
    request:
      verify: False
      <<: *groups_rules_request
      params:
        search: web
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Filter search formatted name
    request:
      verify: False
      <<: *groups_rules_request
      params:
        search: authentication_success
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - authentication_success
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *groups_rules_request
      params:
        noexist: nothing
    response:
      status_code: 400

---
test_name: GET /rules/files

stages:

  - name: Get the rules files
    request: &files_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get the rules files, limit = 1, offset = 0
    request:
      verify: False
      <<: *files_rules_request
      params:
        limit: 1
        offset: 0
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get the rules files, limit = 0
    request:
      verify: False
      <<: *files_rules_request
      params:
        limit: 0
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *files_rules_request
      params:
        sort: "-filename"
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "filename"
            reverse: True
      status_code: 200

  - name: Invalid filter
    request:
      verify: False
      <<: *files_rules_request
      params:
        noexist: True
    response:
      status_code: 400

  - name: Invalid filter - Extra field
    request:
      verify: False
      <<: *files_rules_request
      params:
        status: enabled
        noexist: True
    response:
      status_code: 400

  - name: Filter status
    request:
      verify: False
      <<: *files_rules_request
      params:
        status: enabled
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: !anything
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Get rules files using query parameter
    request:
      verify: False
      <<: *files_rules_request
      params:
        q: filename=0275-squid_rules.xml
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - filename: 0275-squid_rules.xml
              relative_dirname: ruleset/rules
              status: enabled
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    
  - name: Try to get rules files using an invalid query
    request:
      verify: False
      <<: *files_rules_request
      params:
        q: filename%=invalid
    response:
      status_code: 400
      json:
        error: 1407

  - name: Show rules files using valid select
    request:
      verify: False
      <<: *files_rules_request
      params:
        select: 'status'
    response:
      verify_response_with:
        # Check response item keys are the selected keys
        function: tavern_utils:test_select_key_affected_items
        extra_kwargs:
          select_key: 'filename,status' # required_fields={'filename'}
      status_code: 200
      json:
        error: 0
        data:
          total_affected_items: !anyint
          failed_items: []
          total_failed_items: 0

  - name: Try to show rules files using invalid select
    request:
      verify: False
      <<: *files_rules_request
      params:
        select: 'noexists'
    response:
      status_code: 400
      json:
        <<: *invalid_select

  - name: Get distinct rules files
    request:
      verify: False
      <<: *files_rules_request
      params:
        distinct: true
    response:
      status_code: 200
      verify_response_with:
        function: tavern_utils:test_distinct_key

---
test_name: GET /rules/files/(filename)

stages:

  - name: Download rule (JSON format)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/0350-amazon_rules.xml"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        data:
          affected_items:
            - !anything
          total_affected_items: 1
          failed_items: []
          total_failed_items: 0
        error: 0

  - name: Download rule (raw format)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/0350-amazon_rules.xml"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        raw: True
    response:
      status_code: 200
      headers:
        content-type: "application/xml; charset=utf-8"

  - name: Download rule (it does not exist)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/no_exists.xml"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        data:
          affected_items: []
          total_affected_items: 0
          failed_items:
            - error:
                code: 1415
              id:
                - "no_exists.xml"
          total_failed_items: 1
        error: 1

  - name: Download rule (user ruleset, rule belongs to default ruleset)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/0350-amazon_rules.xml"
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        relative_dirname: 'etc/rules'
    response:
      status_code: 200
      json:
        data:
          affected_items: []
          total_affected_items: 0
          failed_items:
            - error:
                code: 1415
              id:
                - "0350-amazon_rules.xml"
          total_failed_items: 1
        error: 1

  - name: Download rule (invalid file 1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/rulexml"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        detail: !anystr
        title: !anystr

  - name: Download rule (invalid file 2)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/..%2F..%2Fetc%local_rules.xml"
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        detail: !anystr
        title: !anystr

---
test_name: PUT /rule/files/{filename}

stages:

  - name: Upload new valid rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: PUT
      data: "{new_rules:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response: &upload_valid_rule
      status_code: 200
      json:
        data:
          affected_items:
            - 'etc/rules/new_rule.xml'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
        error: 0

  - name: Upload new valid rule in custom subdir
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: PUT
      data: "{new_rules:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
      params:
        relative_dirname: 'etc/rules/subdir'
    response:
      status_code: 200
      json:
        data:
          affected_items:
            - 'etc/rules/subdir/new_rule.xml'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
        error: 0

  - name: Upload same rule (overwrite=False)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: PUT
      data: "{new_rules:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      status_code: 200
      json:
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1905
              id:
                - "etc/rules/new_rule.xml"
          total_affected_items: 0
          total_failed_items: 1
        error: 1

  - name: Upload same rule (overwrite=True)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: PUT
      data: "{new_rules:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
      params:
        overwrite: True
    response:
      <<: *upload_valid_rule

  - name: Upload rule with invalid name
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule"
      method: PUT
      data: "{new_rules:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      status_code: 400
      json:
        title: !anystr
        detail: !anystr

  - name: Upload invalid rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/invalid_rule.xml"
      method: PUT
      data: "{new_rules_with_syntax_error:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      status_code: 200
      json:
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1113
              id:
                - "etc/rules/invalid_rule.xml"
          total_affected_items: 0
          total_failed_items: 1
        error: 1

  - name: Upload invalid rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/empty_file.xml"
      method: PUT
      data: "{empty_file_xml:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      status_code: 200
      json:
        data:
          affected_items: [ ]
          failed_items:
            - error:
                code: 1113
              id:
                - "etc/rules/empty_file.xml"
          total_affected_items: 0
          total_failed_items: 1
        error: 1

---
test_name: DELETE /rules/files/{filename}

stages:

  - name: Delete user rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        data:
          affected_items:
            - "etc/rules/new_rule.xml"
          total_affected_items: 1
          failed_items: []
          total_failed_items: 0
        error: 0

  - name: Delete user rule in subdir
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        relative_dirname: 'etc/rules/subdir'
    response:
      status_code: 200
      json:
        data:
          affected_items:
            - "etc/rules/subdir/new_rule.xml"
          total_affected_items: 1
          failed_items: []
          total_failed_items: 0
        error: 0

  - name: Delete same user rule (it does not exist now)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/new_rule.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &delete_no_exists
      status_code: 200
      json:
        data:
          affected_items: []
          total_affected_items: 0
          failed_items:
            - error:
                code: 1906
              id:
                - "etc/rules/new_rule.xml"
          total_failed_items: 1
        error: 1

  - name: Delete ruleset rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/0010-rules_config.xml"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        data:
          affected_items: []
          total_affected_items: 0
          failed_items:
            - error:
                code: 1906
              id:
                - "etc/rules/0010-rules_config.xml"
          total_failed_items: 1
        error: 1

  - name: Delete invalid rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules/files/ossec.conf"
      method: DELETE
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 400
      json:
        title: !anystr
        detail: !anystr

---
test_name: GET /rules?rule_ids

stages:

  - name: Try to show a rule with a existent id
    request: &id_rules_request
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        rule_ids: 1
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show a rule with two existent ids
    request:
      verify: False
      <<: *id_rules_request
      params:
        rule_ids: 200,201
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - <<: *rules_response
            - <<: *rules_response
          failed_items: []
          total_affected_items: !anyint
          total_failed_items: 0

  - name: Try to show a rule with a existent id and no existent one
    request:
      verify: False
      <<: *id_rules_request
      params:
        rule_ids: 200,99999999
    response:
      status_code: 200
      json:
        error: 2
        data:
          affected_items:
            - <<: *rules_response
          failed_items:
            - error:
                code: 1208
              id:
                - 99999999
          total_affected_items: !anyint
          total_failed_items: 1

  - name: Try to show a rule with a existent id but with a wrong file in file filter
    request:
      verify: False
      <<: *id_rules_request
      params:
        rule_ids: 200
        filename: 0025-sendmail_rules.xml
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Try to show a rule with a existent id, limit = 0
    request:
      verify: False
      <<: *id_rules_request
      params:
        limit: 0
        rule_ids: 1
    response:
      status_code: 400

  - name: Filter sort
    request:
      verify: False
      <<: *id_rules_request
      params:
        sort: filename
        rule_ids: 1
    response:
      verify_response_with:
        - function: tavern_utils:test_sort_response
          extra_kwargs:
            key: "filename"
      status_code: 200

  - name: Filter search error
    request:
      verify: False
      <<: *id_rules_request
      params:
        search: error
        rule_ids: 200
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items: []
          failed_items: []
          total_affected_items: 0
          total_failed_items: 0

  - name: Invalid filter
    request:
      verify: False
      <<: *id_rules_request
      params:
        noexist: True
        rule_ids: 201
    response:
      status_code: 400

  - name: No rule
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/rules"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        rule_ids: 9999999999
    response:
      status_code: 200
      json:
        error: 1
        data:
          affected_items: []
          failed_items:
            - error:
                code: 1208
              id:
                - 9999999999
          total_affected_items: 0
          total_failed_items: 1
