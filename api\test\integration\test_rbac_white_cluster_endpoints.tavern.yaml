---
test_name: /cluster/local/config

stages:

  - name: Get cluster config
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/local/config"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - name: !anystr
              node_name: !anystr
              node_type: !anystr
              key: !anystr
              port: !anyint
              bind_addr: !anystr
              nodes: !anything
              hidden: !anystr
              disabled: false
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: GET /cluster/healthcheck

stages:

  - name: Get cluster healtcheck
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/healthcheck"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: 'worker1,worker2'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - info:
                name: "worker2"
                ip: !anystr
                version: !anystr
                type: !anystr
                n_active_agents: !anyint
              status: !anything
          failed_items:
            - error:
                code: 4000
              id:
                - 'worker1'
          total_affected_items: 1
          total_failed_items: 1

  - name: Get cluster healtcheck
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/healthcheck"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - info:
                name: "master-node"
                ip: !anystr
                version: !anystr
                type: !anystr
                n_active_agents: !anyint
            - info:
                name: "worker2"
                ip: !anystr
                version: !anystr
                type: !anystr
                n_active_agents: !anyint
              status: !anything
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

---
test_name: GET /cluster/ruleset/synchronization

stages:

  - name: Get cluster ruleset synchronization status (partial response, user agnostic)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/ruleset/synchronization"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: 0
        data:
          affected_items:
            - name: 'master-node'
              synced: !anybool
            - name: 'worker2'
              synced: !anybool
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

---
test_name: GET /cluster/local/info

stages:

  - name: Get cluster node
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/local/info"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - cluster: wazuh
              node: master-node
              type: master
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: GET /cluster/nodes

stages:

  - name: Get cluster nodes
    request: &get_cluster_nodes
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/nodes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        limit: 1
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items: &cluster_nodes_response
            - name: !anystr
              type: !anystr
              version: !anystr
              ip: !anything
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Get cluster nodes
    request:
      verify: False
      <<: *get_cluster_nodes
      params:
        limit: 500
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *cluster_nodes_response
            - <<: *cluster_nodes_response
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Filters (type) -> master
    request:
      verify: False
      <<: *get_cluster_nodes
      params:
        type: master
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *cluster_nodes_response
              type: master
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Filters (type) -> worker
    request:
      verify: False
      <<: *get_cluster_nodes
      params:
        limit: 5
        type: worker
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *cluster_nodes_response
              type: worker
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: GET /cluster/nodes/{node_id}

stages:

  - name: Get cluster (worker1) (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/nodes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: 'worker1'
    response: &permission_denied
      status_code: 403
      json:
        error: 4000

  - name: Get cluster (worker2) (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/nodes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: 'worker2'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *cluster_nodes_response
              type: worker
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Get cluster (master-node,worker1,worker2) (Allow, Deny, Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/nodes"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: 'master-node,worker1,worker2'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - <<: *cluster_nodes_response
              type: master
            - <<: *cluster_nodes_response
              type: worker
          failed_items:
            - error:
                code: 4000
              id:
                - 'worker1'
          total_affected_items: 2
          total_failed_items: 1

---
test_name: GET /cluster/status

stages:

  - name: Get cluster status
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          enabled: "yes"
          running: "yes"

---
test_name: GET /cluster/{node_id}/configuration

stages:

  - name: Request (master-node)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/master-node/configuration"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - alerts: !anything
              auth: !anything
              cis-cat: !anything
              cluster: !anything
              command: !anything
              global: !anything
              localfile: !anything
              osquery: !anything
              remote: !anything
              rootcheck: !anything
              ruleset: !anything
              sca: !anything
              syscheck: !anything
              syscollector: !anything
              vulnerability-detection: !anything
              indexer: !anything
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

  - name: Request (worker1) (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/configuration"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Filters -> section=alerts (worker2) (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/configuration"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        section: alerts
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - alerts:
                email_alert_level: !anystr
                log_alert_level: !anystr
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0

---
test_name: GET /cluster/configuration/validation

stages:

  - name: Request cluster validation
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/configuration/validation"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - name: master-node
              status: 'OK'
            - name: worker2
              status: 'OK'
          failed_items: []
          total_affected_items: 2
          total_failed_items: 0

  - name: Request cluster validation
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/configuration/validation"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: 'worker1,worker2'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - name: worker2
              status: OK
          failed_items:
            - error:
                code: 4000
              id:
                - worker1
          total_affected_items: 1
          total_failed_items: 1

---
test_name: GET /cluster/{node_id}/configuration/{component}/{configuration}

stages:

  - name: Try to show the config of analisys/global through a worker
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/configuration/analysis/global"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      <<: *permission_denied

  - name: Try to show the config of analisys/global through a worker
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/configuration/analysis/global"
      headers:
        Authorization: "Bearer {test_login_token}"
      method: GET
    response:
      status_code: 200

---
test_name: GET /cluster/{node_id}/info

stages:

  - name: Request (worker1)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/info"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Request (master-node)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/master-node/info"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

---
test_name: GET /cluster/{node_id}/logs

stages:

  - name: Request (worker2) (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/logs"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

  - name: Request (worker1) (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/logs"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/logs/summary

stages:

  - name: Request (worker2) (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/logs/summary"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

  - name: Request (worker1) (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/logs/summary"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/{node_id}/daemons/stats

stages:

  - name: Request daemons statistics from worker1 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Request daemons statistics from worker2 (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/daemons/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

---
test_name: GET /cluster/{node_id}/stats

stages:

  - name: Cluster stats (worker1) today (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Cluster stats (worker2) today (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/stats"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        date: "2019-08-27"
    response:
      status_code: 200

---
test_name: GET /cluster/{node_id}/status

stages:

  - name: Request (worker2) (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200

  - name: Request (worker1) (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/status"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

---
test_name: GET /cluster/api/config

stages:

  - name: Request API config (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/api/config"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: 'worker1,worker2'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - node_name: 'worker2'
              node_api_config: !anything
          failed_items:
            - error:
                code: 4000
              id:
                - worker1
          total_affected_items: 1
          total_failed_items: 1

---
test_name: PUT /cluster/restart

stages:

  - name: Restart cluster
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
      params:
        nodes_list: 'worker1,worker2'
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - worker2
          failed_items:
            - error:
                code: 4000
              id:
                - worker1
          total_affected_items: 1
          total_failed_items: 1
    delay_after: !float "{restart_delay_cluster}"

  - name: Restart cluster
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/restart"
      method: PUT
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data:
          affected_items:
            - 'worker2'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
    delay_after: !float "{restart_delay_cluster}"

---
test_name: PUT /cluster/{node_id}/configuration

stages:

  - name: Upload a valid configuration (Allow)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker2/configuration"
      method: PUT
      data: "{valid_ossec_conf:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      status_code: 200
      json:
        data:
          affected_items:
            - 'worker2'
          failed_items: []
          total_affected_items: 1
          total_failed_items: 0
        error: 0

  - name: Try to upload a valid configuration (Deny)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/cluster/worker1/configuration"
      method: PUT
      data: "{valid_ossec_conf:s}"
      headers:
        Authorization: "Bearer {test_login_token}"
        content-type: application/octet-stream
    response:
      <<: *permission_denied
