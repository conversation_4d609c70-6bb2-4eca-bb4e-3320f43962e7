services:
  wazuh-master:
    profiles:
      - standalone
      - cluster
    build:
      context: .
      dockerfile: base/manager/manager.Dockerfile
    image: integration_test_wazuh-manager
    hostname: wazuh-master
    ports:
      - "55000:55000"
    volumes:
      - ./configurations/tmp/manager:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - wazuh-master
      - master-node
      - master
      - ${ENV_MODE}

  wazuh-worker1:
    profiles:
      - cluster
    image: integration_test_wazuh-manager
    hostname: wazuh-worker1
    volumes:
      - ./configurations/tmp/manager:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - wazuh-master
      - worker1
      - worker
    depends_on:
      - wazuh-master

  wazuh-worker2:
    profiles:
      - cluster
    image: integration_test_wazuh-manager
    hostname: wazuh-worker2
    volumes:
      - ./configurations/tmp/manager:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - wazuh-master
      - worker2
      - worker
    depends_on:
      - wazuh-master

  wazuh-agent1:
    profiles:
      - standalone
      - cluster
    build:
      context: .
      dockerfile: base/agent/new.Dockerfile
    image: integration_test_wazuh-agent
    hostname: wazuh-agent1
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent1
    depends_on:
      - haproxy-lb

  wazuh-agent2:
    profiles:
      - standalone
      - cluster
    image: integration_test_wazuh-agent
    hostname: wazuh-agent2
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent2
    depends_on:
      - wazuh-agent1
      - haproxy-lb

  wazuh-agent3:
    profiles:
      - standalone
      - cluster
    image: integration_test_wazuh-agent
    hostname: wazuh-agent3
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent3
    depends_on:
      - wazuh-agent2
      - haproxy-lb

  wazuh-agent4:
    profiles:
      - standalone
      - cluster
    image: integration_test_wazuh-agent
    hostname: wazuh-agent4
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent4
    depends_on:
      - wazuh-agent3
      - haproxy-lb

  wazuh-agent5:
    profiles:
      - standalone
      - cluster
    build:
      context: .
      dockerfile: base/agent/old.Dockerfile
    image: integration_test_wazuh-agent_old
    hostname: wazuh-agent5
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent5
      - agent_old
    depends_on:
      - wazuh-agent4
      - haproxy-lb

  wazuh-agent6:
    profiles:
      - standalone
      - cluster
    image: integration_test_wazuh-agent_old
    hostname: wazuh-agent6
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent6
      - agent_old
    depends_on:
      - wazuh-agent5
      - haproxy-lb

  wazuh-agent7:
    profiles:
      - standalone
      - cluster
    image: integration_test_wazuh-agent_old
    hostname: wazuh-agent7
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent7
      - agent_old
    depends_on:
      - wazuh-agent6
      - haproxy-lb

  wazuh-agent8:
    profiles:
      - standalone
      - cluster
    image: integration_test_wazuh-agent_old
    hostname: wazuh-agent8
    volumes:
      - ./configurations/tmp/agent:/tmp_volume
      - ./tools/:/tools
    entrypoint:
      - /scripts/entrypoint.sh
      - haproxy-lb
      - wazuh-agent8
      - agent_old
    depends_on:
      - wazuh-agent7
      - haproxy-lb

  haproxy-lb:
    profiles:
      - standalone
      - cluster
    build:
      context: ./base/haproxy-lb
    image: integration_test_haproxy-lb
    entrypoint:
      - /scripts/entrypoint.sh
      - ${ENV_MODE}
    depends_on:
      - wazuh-master
      - wazuh-worker1
      - wazuh-worker2

  cti:
    profiles:
      - standalone
      - cluster
    build:
      context: ./base/cti
    image: integration_test_cti
    restart: always
    environment:
      - PORT=4041
