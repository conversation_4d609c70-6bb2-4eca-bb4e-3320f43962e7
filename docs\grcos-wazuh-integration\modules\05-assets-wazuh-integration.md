# Assets Module Wazuh Integration

## Overview

The Assets Module integration with Wazuh SIEM/XDR transforms static asset inventory management into dynamic, real-time asset discovery and security posture monitoring. This integration provides comprehensive visibility into IT/OT/IoT environments with automated asset classification, vulnerability tracking, and compliance status monitoring.

## Integration Architecture

### Asset Discovery and Management Flow

```mermaid
graph TB
    subgraph "Wazuh Agent Network"
        A[IT Infrastructure Agents]
        B[OT Network Agents]
        C[IoT Device Agents]
        D[Cloud Service Agents]
    end
    
    subgraph "Wazuh Manager"
        E[Agent Registration]
        F[System Inventory Collection]
        G[Network Discovery]
        H[Software Inventory]
    end
    
    subgraph "GRCOS Assets Module"
        I[Asset Discovery Engine]
        J[Asset Classification Engine]
        K[Asset Relationship Mapper]
        L[Asset Security Monitor]
    end
    
    subgraph "Asset Database"
        M[Asset Registry]
        N[Configuration Database]
        O[Vulnerability Database]
        P[Compliance Database]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    
    E --> F
    F --> G
    G --> H
    
    H --> I
    I --> J
    J --> K
    K --> L
    
    I --> M
    J --> N
    L --> O
    L --> P
```

### Core Integration Components

#### 1. Automated Asset Discovery
- **Real-time Agent Registration**: Automatic discovery of new assets as Wazuh agents come online
- **Network Topology Mapping**: Dynamic mapping of network relationships and dependencies
- **Service Discovery**: Identification of running services and applications
- **Hardware Inventory**: Comprehensive hardware specification collection

#### 2. Asset Classification and Categorization
- **Automated Classification**: AI-powered asset categorization based on characteristics
- **Business Context Mapping**: Integration with business function and criticality data
- **Compliance Tagging**: Automatic assignment of compliance requirements
- **Risk Assessment**: Dynamic risk scoring based on security posture

#### 3. Security Posture Monitoring
- **Vulnerability Tracking**: Real-time vulnerability status for all assets
- **Configuration Monitoring**: Continuous monitoring of security configurations
- **Patch Status Tracking**: Automated tracking of patch levels and update status
- **Compliance Status**: Real-time compliance posture assessment

## Asset Discovery Implementation

### Wazuh Agent Integration

```python
import asyncio
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from datetime import datetime

@dataclass
class DiscoveredAsset:
    agent_id: str
    hostname: str
    ip_addresses: List[str]
    mac_addresses: List[str]
    operating_system: Dict[str, str]
    hardware_info: Dict[str, Any]
    software_inventory: List[Dict[str, str]]
    network_interfaces: List[Dict[str, Any]]
    services: List[Dict[str, Any]]
    discovery_timestamp: str
    last_seen: str

class WazuhAssetDiscoveryEngine:
    def __init__(self, wazuh_client, asset_db, classification_engine):
        self.wazuh_client = wazuh_client
        self.asset_db = asset_db
        self.classification_engine = classification_engine
        self.discovery_cache = {}
        self.discovery_rules = self._load_discovery_rules()
    
    async def start_continuous_discovery(self):
        """Start continuous asset discovery process"""
        discovery_tasks = [
            asyncio.create_task(self._monitor_agent_registration()),
            asyncio.create_task(self._periodic_inventory_sync()),
            asyncio.create_task(self._network_discovery_scan()),
            asyncio.create_task(self._service_discovery_scan())
        ]
        
        await asyncio.gather(*discovery_tasks)
    
    async def _monitor_agent_registration(self):
        """Monitor for new Wazuh agent registrations"""
        while True:
            try:
                # Get list of all agents
                agents = await self.wazuh_client.get_agents()
                
                for agent in agents['data']['affected_items']:
                    if agent['id'] not in self.discovery_cache:
                        # New agent discovered
                        await self._process_new_agent(agent)
                        self.discovery_cache[agent['id']] = agent['dateAdd']
                
                await asyncio.sleep(30)  # Check every 30 seconds
            except Exception as e:
                logger.error(f"Error monitoring agent registration: {e}")
                await asyncio.sleep(60)
    
    async def _process_new_agent(self, agent_info: Dict[str, Any]):
        """Process newly discovered agent"""
        agent_id = agent_info['id']
        
        # Collect comprehensive asset information
        asset_data = await self._collect_asset_data(agent_id)
        
        # Create discovered asset object
        discovered_asset = DiscoveredAsset(
            agent_id=agent_id,
            hostname=agent_info['name'],
            ip_addresses=[agent_info['ip']] if agent_info.get('ip') else [],
            mac_addresses=[],
            operating_system=agent_info.get('os', {}),
            hardware_info={},
            software_inventory=[],
            network_interfaces=[],
            services=[],
            discovery_timestamp=datetime.utcnow().isoformat(),
            last_seen=agent_info.get('lastKeepAlive', '')
        )
        
        # Enrich with detailed information
        await self._enrich_asset_data(discovered_asset, asset_data)
        
        # Classify and register asset
        await self._classify_and_register_asset(discovered_asset)
    
    async def _collect_asset_data(self, agent_id: str) -> Dict[str, Any]:
        """Collect comprehensive asset data from Wazuh"""
        asset_data = {}
        
        # Collect hardware information
        try:
            hardware_info = await self.wazuh_client.get_syscollector_hardware(agent_id)
            asset_data['hardware'] = hardware_info['data']['affected_items']
        except Exception as e:
            logger.warning(f"Failed to collect hardware info for {agent_id}: {e}")
            asset_data['hardware'] = []
        
        # Collect network information
        try:
            network_info = await self.wazuh_client.get_syscollector_network(agent_id)
            asset_data['network'] = network_info['data']['affected_items']
        except Exception as e:
            logger.warning(f"Failed to collect network info for {agent_id}: {e}")
            asset_data['network'] = []
        
        # Collect software packages
        try:
            packages_info = await self.wazuh_client.get_syscollector_packages(agent_id)
            asset_data['packages'] = packages_info['data']['affected_items']
        except Exception as e:
            logger.warning(f"Failed to collect packages for {agent_id}: {e}")
            asset_data['packages'] = []
        
        # Collect running processes
        try:
            processes_info = await self.wazuh_client.get_syscollector_processes(agent_id)
            asset_data['processes'] = processes_info['data']['affected_items']
        except Exception as e:
            logger.warning(f"Failed to collect processes for {agent_id}: {e}")
            asset_data['processes'] = []
        
        # Collect open ports
        try:
            ports_info = await self.wazuh_client.get_syscollector_ports(agent_id)
            asset_data['ports'] = ports_info['data']['affected_items']
        except Exception as e:
            logger.warning(f"Failed to collect ports for {agent_id}: {e}")
            asset_data['ports'] = []
        
        return asset_data
    
    async def _enrich_asset_data(self, asset: DiscoveredAsset, raw_data: Dict[str, Any]):
        """Enrich discovered asset with detailed information"""
        # Process hardware information
        if raw_data.get('hardware'):
            hardware = raw_data['hardware'][0] if raw_data['hardware'] else {}
            asset.hardware_info = {
                'cpu_name': hardware.get('cpu_name', ''),
                'cpu_cores': hardware.get('cpu_cores', 0),
                'cpu_mhz': hardware.get('cpu_mhz', 0),
                'ram_total': hardware.get('ram_total', 0),
                'ram_free': hardware.get('ram_free', 0),
                'board_serial': hardware.get('board_serial', '')
            }
        
        # Process network interfaces
        if raw_data.get('network'):
            asset.network_interfaces = []
            asset.mac_addresses = []
            
            for interface in raw_data['network']:
                asset.network_interfaces.append({
                    'name': interface.get('name', ''),
                    'type': interface.get('type', ''),
                    'state': interface.get('state', ''),
                    'mac': interface.get('mac', ''),
                    'ipv4': interface.get('ipv4', {}),
                    'ipv6': interface.get('ipv6', {})
                })
                
                if interface.get('mac'):
                    asset.mac_addresses.append(interface['mac'])
                
                if interface.get('ipv4', {}).get('address'):
                    if interface['ipv4']['address'] not in asset.ip_addresses:
                        asset.ip_addresses.append(interface['ipv4']['address'])
        
        # Process software inventory
        if raw_data.get('packages'):
            asset.software_inventory = [
                {
                    'name': pkg.get('name', ''),
                    'version': pkg.get('version', ''),
                    'architecture': pkg.get('architecture', ''),
                    'description': pkg.get('description', '')
                }
                for pkg in raw_data['packages']
            ]
        
        # Process services (from processes and ports)
        asset.services = []
        
        # Add services from running processes
        if raw_data.get('processes'):
            for process in raw_data['processes']:
                if self._is_service_process(process):
                    asset.services.append({
                        'name': process.get('name', ''),
                        'type': 'process',
                        'pid': process.get('pid', 0),
                        'state': process.get('state', ''),
                        'cmd': process.get('cmd', '')
                    })
        
        # Add services from open ports
        if raw_data.get('ports'):
            for port in raw_data['ports']:
                service_name = self._identify_service_by_port(port.get('local_port', 0))
                asset.services.append({
                    'name': service_name,
                    'type': 'network_service',
                    'port': port.get('local_port', 0),
                    'protocol': port.get('protocol', ''),
                    'state': port.get('state', '')
                })
```

### Asset Classification Engine

```python
class AssetClassificationEngine:
    def __init__(self, ml_model, business_context_db):
        self.ml_model = ml_model
        self.business_context_db = business_context_db
        self.classification_rules = self._load_classification_rules()
        self.asset_categories = self._load_asset_categories()
    
    async def classify_asset(self, asset: DiscoveredAsset) -> Dict[str, Any]:
        """Classify discovered asset using multiple classification methods"""
        classification_result = {
            'asset_type': 'unknown',
            'asset_category': 'unclassified',
            'business_function': 'unknown',
            'criticality': 'medium',
            'environment': 'unknown',
            'compliance_requirements': [],
            'security_zone': 'unknown',
            'confidence': 0.0
        }
        
        # Rule-based classification
        rule_classification = await self._rule_based_classification(asset)
        classification_result.update(rule_classification)
        
        # ML-based classification
        ml_classification = await self._ml_based_classification(asset)
        classification_result = self._merge_classifications(
            classification_result, ml_classification
        )
        
        # Business context classification
        business_classification = await self._business_context_classification(asset)
        classification_result = self._merge_classifications(
            classification_result, business_classification
        )
        
        # Network-based classification
        network_classification = await self._network_based_classification(asset)
        classification_result = self._merge_classifications(
            classification_result, network_classification
        )
        
        # Calculate final confidence score
        classification_result['confidence'] = self._calculate_confidence_score(
            classification_result
        )
        
        return classification_result
    
    async def _rule_based_classification(self, asset: DiscoveredAsset) -> Dict[str, Any]:
        """Apply rule-based classification logic"""
        classification = {}
        
        # Classify by operating system
        os_name = asset.operating_system.get('name', '').lower()
        if 'windows' in os_name:
            if 'server' in os_name:
                classification['asset_type'] = 'windows_server'
            else:
                classification['asset_type'] = 'windows_workstation'
        elif 'linux' in os_name or 'ubuntu' in os_name:
            classification['asset_type'] = 'linux_server'
        elif 'cisco' in os_name or 'juniper' in os_name:
            classification['asset_type'] = 'network_device'
        
        # Classify by services
        service_names = [service['name'].lower() for service in asset.services]
        
        if any(db in service_names for db in ['mysql', 'postgresql', 'oracle', 'mssql']):
            classification['asset_category'] = 'database_server'
            classification['criticality'] = 'high'
        elif any(web in service_names for web in ['apache', 'nginx', 'iis']):
            classification['asset_category'] = 'web_server'
            classification['criticality'] = 'high'
        elif any(mail in service_names for mail in ['postfix', 'exchange', 'sendmail']):
            classification['asset_category'] = 'mail_server'
            classification['criticality'] = 'high'
        
        # Classify by network characteristics
        if len(asset.ip_addresses) > 1:
            classification['asset_type'] = 'multi_homed_server'
        
        # Determine security zone by IP range
        for ip in asset.ip_addresses:
            zone = self._determine_security_zone(ip)
            if zone != 'unknown':
                classification['security_zone'] = zone
                break
        
        return classification
    
    async def _ml_based_classification(self, asset: DiscoveredAsset) -> Dict[str, Any]:
        """Use ML model for asset classification"""
        # Extract features for ML model
        features = self._extract_classification_features(asset)
        
        # Get ML predictions
        predictions = await self.ml_model.predict_asset_classification(features)
        
        return {
            'ml_asset_type': predictions.get('asset_type', 'unknown'),
            'ml_criticality': predictions.get('criticality', 'medium'),
            'ml_confidence': predictions.get('confidence', 0.0)
        }
    
    def _extract_classification_features(self, asset: DiscoveredAsset) -> Dict[str, Any]:
        """Extract features for ML classification"""
        features = {
            # Operating system features
            'os_family': self._normalize_os_family(asset.operating_system.get('name', '')),
            'os_version': asset.operating_system.get('version', ''),
            
            # Hardware features
            'cpu_cores': asset.hardware_info.get('cpu_cores', 0),
            'ram_gb': asset.hardware_info.get('ram_total', 0) / (1024 * 1024),  # Convert to GB
            
            # Network features
            'interface_count': len(asset.network_interfaces),
            'ip_count': len(asset.ip_addresses),
            'has_public_ip': any(self._is_public_ip(ip) for ip in asset.ip_addresses),
            
            # Service features
            'service_count': len(asset.services),
            'has_web_service': any('http' in service['name'].lower() for service in asset.services),
            'has_database_service': any(db in service['name'].lower() 
                                      for service in asset.services 
                                      for db in ['mysql', 'postgres', 'oracle']),
            'has_ssh_service': any('ssh' in service['name'].lower() for service in asset.services),
            
            # Software features
            'package_count': len(asset.software_inventory),
            'has_security_software': any(sec in pkg['name'].lower() 
                                       for pkg in asset.software_inventory 
                                       for sec in ['antivirus', 'firewall', 'security'])
        }
        
        return features
```

### Asset Security Monitoring

```python
class AssetSecurityMonitor:
    def __init__(self, wazuh_client, vulnerability_db, compliance_engine):
        self.wazuh_client = wazuh_client
        self.vulnerability_db = vulnerability_db
        self.compliance_engine = compliance_engine
        self.monitoring_rules = self._load_monitoring_rules()
    
    async def monitor_asset_security_posture(self, asset_id: str) -> Dict[str, Any]:
        """Monitor comprehensive security posture of an asset"""
        security_posture = {
            'asset_id': asset_id,
            'timestamp': datetime.utcnow().isoformat(),
            'overall_score': 0.0,
            'vulnerability_status': {},
            'configuration_status': {},
            'compliance_status': {},
            'patch_status': {},
            'security_events': []
        }
        
        # Assess vulnerability status
        vulnerability_status = await self._assess_vulnerability_status(asset_id)
        security_posture['vulnerability_status'] = vulnerability_status
        
        # Assess configuration compliance
        config_status = await self._assess_configuration_status(asset_id)
        security_posture['configuration_status'] = config_status
        
        # Assess compliance status
        compliance_status = await self._assess_compliance_status(asset_id)
        security_posture['compliance_status'] = compliance_status
        
        # Assess patch status
        patch_status = await self._assess_patch_status(asset_id)
        security_posture['patch_status'] = patch_status
        
        # Get recent security events
        security_events = await self._get_recent_security_events(asset_id)
        security_posture['security_events'] = security_events
        
        # Calculate overall security score
        security_posture['overall_score'] = self._calculate_security_score(
            security_posture
        )
        
        return security_posture
    
    async def _assess_vulnerability_status(self, asset_id: str) -> Dict[str, Any]:
        """Assess vulnerability status of an asset"""
        try:
            # Get vulnerability data from Wazuh
            vuln_data = await self.wazuh_client.get_vulnerability_data(asset_id)
            
            vulnerabilities = vuln_data.get('data', {}).get('affected_items', [])
            
            # Categorize vulnerabilities by severity
            vuln_summary = {
                'total_vulnerabilities': len(vulnerabilities),
                'critical': 0,
                'high': 0,
                'medium': 0,
                'low': 0,
                'latest_scan': None,
                'top_vulnerabilities': []
            }
            
            for vuln in vulnerabilities:
                severity = vuln.get('severity', 'low').lower()
                if severity == 'critical':
                    vuln_summary['critical'] += 1
                elif severity == 'high':
                    vuln_summary['high'] += 1
                elif severity == 'medium':
                    vuln_summary['medium'] += 1
                else:
                    vuln_summary['low'] += 1
            
            # Get top 5 most critical vulnerabilities
            sorted_vulns = sorted(
                vulnerabilities,
                key=lambda x: self._get_severity_score(x.get('severity', 'low')),
                reverse=True
            )
            vuln_summary['top_vulnerabilities'] = sorted_vulns[:5]
            
            return vuln_summary
            
        except Exception as e:
            logger.error(f"Error assessing vulnerability status for {asset_id}: {e}")
            return {
                'total_vulnerabilities': 0,
                'critical': 0,
                'high': 0,
                'medium': 0,
                'low': 0,
                'error': str(e)
            }
    
    def _calculate_security_score(self, security_posture: Dict[str, Any]) -> float:
        """Calculate overall security score for an asset"""
        scores = []
        weights = []
        
        # Vulnerability score (40% weight)
        vuln_status = security_posture['vulnerability_status']
        vuln_score = self._calculate_vulnerability_score(vuln_status)
        scores.append(vuln_score)
        weights.append(0.4)
        
        # Configuration score (25% weight)
        config_status = security_posture['configuration_status']
        config_score = self._calculate_configuration_score(config_status)
        scores.append(config_score)
        weights.append(0.25)
        
        # Compliance score (20% weight)
        compliance_status = security_posture['compliance_status']
        compliance_score = self._calculate_compliance_score(compliance_status)
        scores.append(compliance_score)
        weights.append(0.2)
        
        # Patch score (15% weight)
        patch_status = security_posture['patch_status']
        patch_score = self._calculate_patch_score(patch_status)
        scores.append(patch_score)
        weights.append(0.15)
        
        # Calculate weighted average
        weighted_score = sum(score * weight for score, weight in zip(scores, weights))
        
        return round(weighted_score, 2)
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Assets Module Team
