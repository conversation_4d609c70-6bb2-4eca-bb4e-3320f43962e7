# GRCOS Artifacts Module OSCAL Integration

## Overview

The Artifacts Module manages blockchain-secured evidence repository using OSCAL Back-Matter and Evidence models. This module provides immutable compliance documentation storage, cryptographic verification of all security artifacts, and audit-ready evidence with tamper-proof integrity across the entire GRCOS ecosystem.

## OSCAL Model Integration

### Back-Matter Evidence Management

#### OSCAL Back-Matter Structure
```json
{
  "back-matter": {
    "resources": [
      {
        "uuid": "evidence-001-vuln-scan",
        "title": "Vulnerability Scan Report - Q1 2024",
        "description": "Comprehensive vulnerability assessment of production web servers",
        "props": [
          {"name": "evidence-type", "value": "vulnerability-scan"},
          {"name": "classification", "value": "internal"},
          {"name": "retention-period", "value": "7-years"},
          {"name": "blockchain-hash", "value": "sha256:abc123def456..."}
        ],
        "document-ids": [
          {
            "scheme": "grcos-evidence-id",
            "identifier": "EVD-2024-001"
          }
        ],
        "citation": {
          "text": "OpenVAS Vulnerability Scan Report",
          "props": [
            {"name": "scan-date", "value": "2024-01-20"},
            {"name": "scanner-version", "value": "22.4.1"},
            {"name": "scan-duration", "value": "4h 32m"}
          ]
        },
        "rlinks": [
          {
            "href": "ipfs://QmYwAPJzv5CZsnA625s3Xf2nemtYgPpHdWEz79ojWnPbdG",
            "media-type": "application/pdf",
            "hashes": [
              {
                "algorithm": "sha256",
                "value": "d886ea4c40165b2c4dd4b5ec247e8a4f0a7b758e4aa1a2311b7b9d9e8f6c5d4"
              }
            ]
          }
        ],
        "base64": {
          "filename": "vuln-scan-report-q1-2024.pdf",
          "media-type": "application/pdf",
          "value": "JVBERi0xLjQKJcOkw7zDtsO..."
        }
      }
    ]
  }
}
```

### Evidence Lifecycle Management

#### Evidence Collection and Storage
```python
class EvidenceManager:
    """
    Manage OSCAL evidence collection, storage, and verification
    """
    
    def __init__(self, blockchain_client, ipfs_client, encryption_service):
        self.blockchain_client = blockchain_client
        self.ipfs_client = ipfs_client
        self.encryption_service = encryption_service
        self.evidence_repository = EvidenceRepository()
    
    def collect_evidence(self, evidence_request):
        """
        Collect evidence from various sources and create OSCAL back-matter entry
        """
        evidence_collection = {
            "collection_id": str(uuid.uuid4()),
            "request": evidence_request,
            "collected_items": [],
            "collection_timestamp": datetime.utcnow(),
            "collector": evidence_request.get("collector", "grcos-system")
        }
        
        # Collect evidence from different sources
        for source in evidence_request["sources"]:
            try:
                evidence_item = self._collect_from_source(source)
                if evidence_item:
                    evidence_collection["collected_items"].append(evidence_item)
            except Exception as e:
                self._log_collection_error(source, str(e))
        
        # Process collected evidence
        processed_evidence = []
        for item in evidence_collection["collected_items"]:
            processed_item = self._process_evidence_item(item)
            processed_evidence.append(processed_item)
        
        # Create OSCAL back-matter entries
        back_matter_resources = self._create_back_matter_resources(processed_evidence)
        
        # Store evidence securely
        storage_results = self._store_evidence_securely(back_matter_resources)
        
        return {
            "collection_id": evidence_collection["collection_id"],
            "back_matter": {"resources": back_matter_resources},
            "storage_results": storage_results
        }
    
    def _collect_from_source(self, source):
        """
        Collect evidence from specific source
        """
        source_type = source.get("type")
        
        if source_type == "vulnerability_scanner":
            return self._collect_vulnerability_scan_evidence(source)
        elif source_type == "configuration_scanner":
            return self._collect_configuration_evidence(source)
        elif source_type == "log_files":
            return self._collect_log_evidence(source)
        elif source_type == "manual_documentation":
            return self._collect_manual_evidence(source)
        elif source_type == "screenshot":
            return self._collect_screenshot_evidence(source)
        else:
            raise ValueError(f"Unsupported evidence source type: {source_type}")
    
    def _process_evidence_item(self, evidence_item):
        """
        Process individual evidence item for secure storage
        """
        # Generate unique evidence ID
        evidence_id = f"EVD-{datetime.now().year}-{self._generate_sequence_number()}"
        
        # Calculate content hash
        content_hash = self._calculate_content_hash(evidence_item["content"])
        
        # Encrypt sensitive content if required
        if evidence_item.get("sensitive", False):
            encrypted_content = self.encryption_service.encrypt(evidence_item["content"])
            evidence_item["content"] = encrypted_content
            evidence_item["encrypted"] = True
        
        # Add metadata
        processed_item = {
            "evidence_id": evidence_id,
            "original_item": evidence_item,
            "content_hash": content_hash,
            "processing_timestamp": datetime.utcnow(),
            "retention_policy": self._determine_retention_policy(evidence_item),
            "classification": self._determine_classification(evidence_item)
        }
        
        return processed_item
    
    def _store_evidence_securely(self, back_matter_resources):
        """
        Store evidence with multiple redundancy and verification layers
        """
        storage_results = []
        
        for resource in back_matter_resources:
            storage_result = {
                "resource_uuid": resource["uuid"],
                "storage_locations": [],
                "blockchain_registration": None,
                "verification_status": "pending"
            }
            
            try:
                # Store in IPFS for distributed storage
                if resource.get("rlinks"):
                    ipfs_hash = self._store_in_ipfs(resource)
                    storage_result["storage_locations"].append({
                        "type": "ipfs",
                        "location": ipfs_hash,
                        "timestamp": datetime.utcnow()
                    })
                
                # Store in local encrypted storage
                local_path = self._store_locally(resource)
                storage_result["storage_locations"].append({
                    "type": "local_encrypted",
                    "location": local_path,
                    "timestamp": datetime.utcnow()
                })
                
                # Register on blockchain
                blockchain_tx = self._register_evidence_blockchain(resource)
                storage_result["blockchain_registration"] = blockchain_tx
                
                # Verify storage integrity
                verification_result = self._verify_storage_integrity(resource, storage_result)
                storage_result["verification_status"] = "verified" if verification_result else "failed"
                
            except Exception as e:
                storage_result["error"] = str(e)
                storage_result["verification_status"] = "failed"
            
            storage_results.append(storage_result)
        
        return storage_results
```

## Blockchain Evidence Registry

### Immutable Evidence Tracking

#### Evidence Blockchain Integration
```python
class EvidenceBlockchainRegistry:
    """
    Blockchain registry for immutable evidence tracking
    """
    
    def __init__(self, hyperledger_client):
        self.hyperledger_client = hyperledger_client
        self.evidence_chaincode = "evidence-registry"
    
    def register_evidence(self, evidence_resource):
        """
        Register evidence on blockchain for immutable tracking
        """
        evidence_record = {
            "evidence_uuid": evidence_resource["uuid"],
            "evidence_id": evidence_resource.get("document-ids", [{}])[0].get("identifier"),
            "title": evidence_resource["title"],
            "description": evidence_resource["description"],
            "evidence_type": self._extract_evidence_type(evidence_resource),
            "content_hash": self._extract_content_hash(evidence_resource),
            "collection_timestamp": evidence_resource.get("collection_timestamp"),
            "collector": evidence_resource.get("collector"),
            "classification": self._extract_classification(evidence_resource),
            "retention_period": self._extract_retention_period(evidence_resource),
            "storage_locations": self._extract_storage_locations(evidence_resource)
        }
        
        # Invoke chaincode to register evidence
        transaction_result = self.hyperledger_client.invoke_chaincode(
            channel_name="evidence-channel",
            chaincode_name=self.evidence_chaincode,
            function_name="RegisterEvidence",
            args=[
                evidence_record["evidence_uuid"],
                json.dumps(evidence_record)
            ]
        )
        
        return {
            "transaction_id": transaction_result["transaction_id"],
            "block_number": transaction_result["block_number"],
            "timestamp": transaction_result["timestamp"],
            "evidence_uuid": evidence_record["evidence_uuid"]
        }
    
    def verify_evidence_integrity(self, evidence_uuid, current_hash):
        """
        Verify evidence integrity against blockchain record
        """
        # Query blockchain for evidence record
        query_result = self.hyperledger_client.query_chaincode(
            channel_name="evidence-channel",
            chaincode_name=self.evidence_chaincode,
            function_name="GetEvidence",
            args=[evidence_uuid]
        )
        
        if not query_result["success"]:
            return {
                "verified": False,
                "error": "Evidence not found on blockchain"
            }
        
        blockchain_record = json.loads(query_result["result"])
        stored_hash = blockchain_record.get("content_hash")
        
        verification_result = {
            "verified": stored_hash == current_hash,
            "blockchain_hash": stored_hash,
            "current_hash": current_hash,
            "registration_timestamp": blockchain_record.get("collection_timestamp"),
            "verification_timestamp": datetime.utcnow().isoformat()
        }
        
        # Log verification attempt
        self._log_verification_attempt(evidence_uuid, verification_result)
        
        return verification_result
    
    def get_evidence_audit_trail(self, evidence_uuid):
        """
        Get complete audit trail for evidence from blockchain
        """
        # Query evidence history from blockchain
        history_result = self.hyperledger_client.query_chaincode(
            channel_name="evidence-channel",
            chaincode_name=self.evidence_chaincode,
            function_name="GetEvidenceHistory",
            args=[evidence_uuid]
        )
        
        if not history_result["success"]:
            return {"error": "Evidence history not found"}
        
        history_records = json.loads(history_result["result"])
        
        audit_trail = {
            "evidence_uuid": evidence_uuid,
            "total_transactions": len(history_records),
            "audit_events": []
        }
        
        for record in history_records:
            audit_event = {
                "transaction_id": record["transaction_id"],
                "timestamp": record["timestamp"],
                "block_number": record["block_number"],
                "event_type": record.get("event_type", "registration"),
                "details": record.get("details", {})
            }
            audit_trail["audit_events"].append(audit_event)
        
        return audit_trail
```

### Evidence Access Control

#### Role-Based Evidence Access
```python
class EvidenceAccessController:
    """
    Control access to evidence based on roles and classifications
    """
    
    def __init__(self, rbac_service, classification_service):
        self.rbac_service = rbac_service
        self.classification_service = classification_service
        self.access_policies = self._load_access_policies()
    
    def authorize_evidence_access(self, user_context, evidence_uuid, access_type="read"):
        """
        Authorize user access to specific evidence
        """
        # Get evidence metadata
        evidence_metadata = self._get_evidence_metadata(evidence_uuid)
        
        if not evidence_metadata:
            return {
                "authorized": False,
                "reason": "Evidence not found"
            }
        
        # Check user permissions
        user_permissions = self.rbac_service.get_user_permissions(user_context["user_id"])
        evidence_classification = evidence_metadata.get("classification", "internal")
        
        # Apply access control policies
        authorization_result = self._apply_access_policies(
            user_context,
            user_permissions,
            evidence_metadata,
            access_type
        )
        
        # Log access attempt
        self._log_access_attempt(user_context, evidence_uuid, access_type, authorization_result)
        
        return authorization_result
    
    def _apply_access_policies(self, user_context, user_permissions, evidence_metadata, access_type):
        """
        Apply evidence access control policies
        """
        evidence_classification = evidence_metadata.get("classification", "internal")
        evidence_type = evidence_metadata.get("evidence_type", "general")
        
        # Check classification clearance
        if not self._has_classification_clearance(user_context, evidence_classification):
            return {
                "authorized": False,
                "reason": f"Insufficient clearance for {evidence_classification} evidence"
            }
        
        # Check role-based permissions
        required_permission = f"evidence:{access_type}:{evidence_type}"
        if required_permission not in user_permissions:
            return {
                "authorized": False,
                "reason": f"Missing required permission: {required_permission}"
            }
        
        # Check time-based restrictions
        if not self._check_time_restrictions(user_context, evidence_metadata):
            return {
                "authorized": False,
                "reason": "Access not permitted during current time window"
            }
        
        # Check purpose limitation
        if not self._check_purpose_limitation(user_context, evidence_metadata, access_type):
            return {
                "authorized": False,
                "reason": "Access purpose not authorized for this evidence"
            }
        
        return {
            "authorized": True,
            "access_conditions": self._get_access_conditions(user_context, evidence_metadata)
        }
```

## Evidence Integration with OSCAL Documents

### Automatic Evidence Linking

#### Evidence-Control Mapping
```python
class EvidenceControlMapper:
    """
    Automatically map evidence to OSCAL controls and assessments
    """
    
    def __init__(self, oscal_repository, ml_classifier):
        self.oscal_repository = oscal_repository
        self.ml_classifier = ml_classifier
        self.mapping_rules = self._load_mapping_rules()
    
    def map_evidence_to_controls(self, evidence_resource):
        """
        Automatically map evidence to relevant OSCAL controls
        """
        # Extract evidence characteristics
        evidence_characteristics = self._extract_evidence_characteristics(evidence_resource)
        
        # Use ML classifier to identify relevant controls
        predicted_controls = self.ml_classifier.predict_relevant_controls(evidence_characteristics)
        
        # Apply rule-based mapping
        rule_based_controls = self._apply_mapping_rules(evidence_resource)
        
        # Combine and rank control mappings
        combined_mappings = self._combine_and_rank_mappings(
            predicted_controls,
            rule_based_controls
        )
        
        # Create evidence citations in OSCAL documents
        citation_results = []
        for mapping in combined_mappings:
            if mapping["confidence"] > 0.7:  # High confidence threshold
                citation_result = self._create_evidence_citation(evidence_resource, mapping)
                citation_results.append(citation_result)
        
        return {
            "evidence_uuid": evidence_resource["uuid"],
            "mapped_controls": combined_mappings,
            "citations_created": citation_results
        }
    
    def _create_evidence_citation(self, evidence_resource, control_mapping):
        """
        Create evidence citation in relevant OSCAL documents
        """
        control_id = control_mapping["control_id"]
        system_uuid = control_mapping.get("system_uuid")
        
        # Find relevant OSCAL documents
        relevant_documents = self._find_relevant_documents(control_id, system_uuid)
        
        citation_results = []
        
        for doc in relevant_documents:
            try:
                # Add evidence citation to document
                if doc["type"] == "assessment-results":
                    citation_result = self._add_citation_to_assessment(
                        doc["uuid"], 
                        evidence_resource, 
                        control_mapping
                    )
                elif doc["type"] == "system-security-plan":
                    citation_result = self._add_citation_to_ssp(
                        doc["uuid"], 
                        evidence_resource, 
                        control_mapping
                    )
                
                citation_results.append(citation_result)
                
            except Exception as e:
                self._log_citation_error(doc, evidence_resource, str(e))
        
        return citation_results
    
    def _add_citation_to_assessment(self, assessment_uuid, evidence_resource, control_mapping):
        """
        Add evidence citation to assessment results
        """
        assessment_results = self.oscal_repository.get_assessment_results(assessment_uuid)
        
        # Find relevant observation or finding
        target_observation = self._find_target_observation(
            assessment_results, 
            control_mapping["control_id"]
        )
        
        if target_observation:
            # Add evidence reference
            evidence_reference = {
                "href": f"#evidence-{evidence_resource['uuid']}",
                "description": evidence_resource["title"]
            }
            
            if "relevant-evidence" not in target_observation:
                target_observation["relevant-evidence"] = []
            
            target_observation["relevant-evidence"].append(evidence_reference)
            
            # Update assessment results
            self.oscal_repository.update_assessment_results(assessment_uuid, assessment_results)
            
            return {
                "document_uuid": assessment_uuid,
                "observation_uuid": target_observation["uuid"],
                "citation_added": True
            }
        
        return {
            "document_uuid": assessment_uuid,
            "citation_added": False,
            "reason": "No relevant observation found"
        }
```

## Evidence Retention and Lifecycle

### Automated Retention Management

#### Evidence Lifecycle Manager
```python
class EvidenceLifecycleManager:
    """
    Manage evidence lifecycle including retention and disposal
    """
    
    def __init__(self, evidence_repository, blockchain_client):
        self.evidence_repository = evidence_repository
        self.blockchain_client = blockchain_client
        self.retention_policies = self._load_retention_policies()
    
    def manage_evidence_lifecycle(self):
        """
        Execute evidence lifecycle management tasks
        """
        lifecycle_results = {
            "processed_evidence": 0,
            "retention_warnings": [],
            "disposal_actions": [],
            "errors": []
        }
        
        # Get all evidence requiring lifecycle management
        evidence_items = self.evidence_repository.get_evidence_for_lifecycle_review()
        
        for evidence in evidence_items:
            try:
                lifecycle_action = self._determine_lifecycle_action(evidence)
                
                if lifecycle_action["action"] == "warn":
                    warning = self._generate_retention_warning(evidence, lifecycle_action)
                    lifecycle_results["retention_warnings"].append(warning)
                
                elif lifecycle_action["action"] == "dispose":
                    disposal_result = self._execute_evidence_disposal(evidence, lifecycle_action)
                    lifecycle_results["disposal_actions"].append(disposal_result)
                
                lifecycle_results["processed_evidence"] += 1
                
            except Exception as e:
                error_record = {
                    "evidence_uuid": evidence["uuid"],
                    "error": str(e),
                    "timestamp": datetime.utcnow()
                }
                lifecycle_results["errors"].append(error_record)
        
        return lifecycle_results
    
    def _determine_lifecycle_action(self, evidence):
        """
        Determine appropriate lifecycle action for evidence
        """
        retention_period = evidence.get("retention_period", "7-years")
        collection_date = evidence.get("collection_timestamp")
        evidence_type = evidence.get("evidence_type", "general")
        
        # Calculate retention expiry
        retention_expiry = self._calculate_retention_expiry(collection_date, retention_period)
        days_until_expiry = (retention_expiry - datetime.utcnow()).days
        
        # Check for legal holds
        legal_holds = self._check_legal_holds(evidence)
        
        if legal_holds:
            return {
                "action": "hold",
                "reason": "Legal hold in effect",
                "hold_details": legal_holds
            }
        
        if days_until_expiry <= 30:
            if days_until_expiry <= 0:
                return {
                    "action": "dispose",
                    "reason": "Retention period expired",
                    "expiry_date": retention_expiry
                }
            else:
                return {
                    "action": "warn",
                    "reason": "Retention period expiring soon",
                    "days_remaining": days_until_expiry
                }
        
        return {
            "action": "maintain",
            "reason": "Within retention period",
            "days_remaining": days_until_expiry
        }
    
    def _execute_evidence_disposal(self, evidence, lifecycle_action):
        """
        Execute secure evidence disposal
        """
        disposal_result = {
            "evidence_uuid": evidence["uuid"],
            "disposal_timestamp": datetime.utcnow(),
            "disposal_method": "secure_deletion",
            "verification_hash": None,
            "blockchain_record": None
        }
        
        try:
            # Create disposal record on blockchain before deletion
            disposal_record = {
                "evidence_uuid": evidence["uuid"],
                "disposal_timestamp": disposal_result["disposal_timestamp"].isoformat(),
                "disposal_reason": lifecycle_action["reason"],
                "disposal_authorized_by": "grcos-lifecycle-manager",
                "original_hash": evidence.get("content_hash")
            }
            
            blockchain_tx = self.blockchain_client.invoke_chaincode(
                channel_name="evidence-channel",
                chaincode_name="evidence-registry",
                function_name="RecordEvidenceDisposal",
                args=[evidence["uuid"], json.dumps(disposal_record)]
            )
            
            disposal_result["blockchain_record"] = blockchain_tx["transaction_id"]
            
            # Securely delete evidence files
            deletion_result = self._secure_delete_evidence_files(evidence)
            disposal_result["deletion_verification"] = deletion_result
            
            # Update evidence repository
            self.evidence_repository.mark_evidence_disposed(evidence["uuid"])
            
            disposal_result["status"] = "completed"
            
        except Exception as e:
            disposal_result["status"] = "failed"
            disposal_result["error"] = str(e)
        
        return disposal_result
```

---

**Document Version**: 1.0  
**Classification**: Internal Use  
**Next Review**: Quarterly  
**Owner**: GRCOS Artifacts Module Team
