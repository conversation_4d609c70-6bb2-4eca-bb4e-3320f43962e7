---
name: Rules/Decoders Issue
about: Report a bug or make a feature request.
title: ''
labels: 'threatintel'
assignees: ''

---

|Wazuh version| Component | Action type |
|---| --- | --- |
| X.Y.Z-rev | Rules/Decoders | New/Error/Improve |

<!--
This template reflects sections that must be included in new issues
Contributions from the community are really appreciated. If this is the case, please add the
"contribution" to properly track the issue.
-->

## Description
<!-- Add a detailed description of your issue -->
<!-- Detail the reason that motivates this proposed change on the ruleset -->

### Service/Product/Module
<!-- Add a description of the service that you are targeting with the ruleset, indicating vendor and/or module if available -->
<!-- Add any URL or doc related to service and/or vendor/module -->


## Errors/Improvements
### Current results
<!--  Include current results -->

### Expected results
<!--  Include expected results -->

## Resources
### Log source / integration
<!-- Especify where the logs come from and/or if integration is required-->

### Log reference
<!-- Add any URL or doc related to log format -->
<!-- Add any URL or doc related to log events -->

### Log examples
<!-- Add any logs examples available -->

### Threats and compliance
<!-- We analyze in search of applicable threats (MITRE) and add  applicable commpliance (PCI DSS, GDPR) -->
<!-- Optionally list specific threats and compliance to be covered if desired -->
<!-- Include any related URL or documentation -->
