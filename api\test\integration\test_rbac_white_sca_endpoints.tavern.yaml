---
test_name: GET /sca/{agent_id}

stages:

  - name: Request sca for agent 000 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/000"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data: !anything

  - name: Request sca for agent 001 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response: &permission_denied
      status_code: 403
      json:
        error: 4000
        dapi_errors:
          unknown-node: # No permission to see node
            error: !anystr

  - name: Request sca for agent 002 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/002"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data: !anything

  - name: Request sca for agent 003 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/003"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Request sca for agent 003 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/003"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied

  - name: Request sca for agent 005 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/005"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data: !anything

---
test_name: GET /sca/{agent_id}/checks/{policy_id}

stages:

  - name: Request policy checks for 000 (Allowed)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/000/checks/cis_debian9_L1"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      status_code: 200
      json:
        error: !anyint
        data: !anything

  - name: Request policy checks for 001 (Denied)
    request:
      verify: False
      url: "{protocol:s}://{host:s}:{port:d}/sca/001/checks/cis_debian9_L1"
      method: GET
      headers:
        Authorization: "Bearer {test_login_token}"
    response:
      <<: *permission_denied
