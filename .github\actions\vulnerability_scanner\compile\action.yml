name: "Vulnerability Scanner Content generation"
description: "Executes the vulnerability scanner tools to generate the content and compress it."

runs:
  using: "composite"
  steps:
  # Dependencies
  - name: Project dependencies
    uses: ./.github/actions/vulnerability_scanner_deps

  # Router
  - name: Router - Compilation
    uses: ./.github/actions/compile_and_test
    with:
      path: src/shared_modules/router
      test: false

  # Indexer connector
  - name: Indexer connector - Compilation
    uses: ./.github/actions/compile_and_test
    with:
      path: src/shared_modules/indexer_connector
      test: false

  # Content manager
  - name: Content manager - Compilation
    uses: ./.github/actions/compile_and_test
    with:
      path: src/shared_modules/content_manager
      test: false

  # Vulnerability scanner
  - name: Vulnerability scanner - Compilation
    uses: ./.github/actions/compile_and_test
    with:
      path: src/wazuh_modules/vulnerability_scanner
      test: false
