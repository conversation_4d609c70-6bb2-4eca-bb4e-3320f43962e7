test_name: GET /overview/agents

stages:

 - name: Get full agents overview
   request:
     verify: False
     url: "{protocol:s}://{host:s}:{port:d}/overview/agents"
     method: GET
     headers:
       Authorization: "Bearer {test_login_token}"
   response:
     verify_response_with:
       function: tavern_utils:test_validate_data_dict_field
       extra_kwargs:
         fields_dict:
           agent_version:
             version: str
             count: int
           nodes:
             node: str
             count: int
     status_code: 200
     json:
       error: 0
       data:
         agent_os:
           - count: !anyint
             os:
               name: !anystr
               platform: !anystr
               version: !anystr
           - count: !anyint
             os:
               name: !anystr
               platform: !anystr
               version: !anystr
           - count: !anyint
             os:
               name: !anystr
               platform: !anystr
               version: !anystr
         agent_status:
           connection:
             active: 4
             disconnected: 1
             never_connected: 1
             pending: 0
             total: 6
           configuration:
             synced: 4
             not_synced: 2
             total: 6
         agent_version: !anything # This will be tested with an external function
         groups: []
         last_registered_agent:
           - configSum: !anystr
             dateAdd: !anystr
             group: !anything
             id: !anystr
             ip: !anystr
             lastKeepAlive: !anystr
             manager: !anystr
             mergedSum: !anystr
             name: !anystr
             node_name: !anystr
             os:
               arch: !anystr
               codename: !anystr
               major: !anystr
               minor: !anystr
               name: !anystr
               platform: !anystr
               uname: !anystr
               version: !anystr
             registerIP: any
             status: disconnected
             version: !anystr
         nodes: !anything
