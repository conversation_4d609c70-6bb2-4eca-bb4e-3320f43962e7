name: "Legacy unit tests build for Wazuh"
description: "This action builds legacy Unit Tests for Wazuh on Ubuntu Linux/Windows"
inputs:
   target:
     required: true
     description: "Target specifies whether the build is for Linux or Windows"
runs:
  using: "composite"
  steps:
    - name: Set permissions and prepare the library
      shell: bash
      run: |
        chmod +x .github/actions/legacy_unit_tests.sh
    - name: Build Wazuh Unit Tests
      shell: bash
      run: |
        source .github/actions/legacy_unit_tests.sh
        build_wazuh_unit_tests ${{ inputs.target }}
