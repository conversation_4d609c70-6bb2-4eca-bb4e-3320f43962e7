name: "Vulnerability Scanner Content generation"
description: "Executes the vulnerability scanner tools to generate the content and compress it."

inputs:
  vulnerability_scanner_path:
    required: true
    description: "Path to the vulnerability scanner tool"
    default: src/wazuh_modules/vulnerability_scanner/build/testtool/scanner/vd_scanner_testtool

  config_path:
    required: true
    description: "Path to the configuration file"
    default: src/wazuh_modules/vulnerability_scanner/testtool/scanner/config.content_generation.json

  content_version:
    required: true
    description: "Identifier for the generated content. The content will be compressed into a file named 'vd_1.0.0_vd_<content_version>.tar.xz'"

runs:
  using: "composite"
  steps:
  - name: Generate Vulnerability Scanner Content
    run: |
      VULNERABILITY_SCANNER_PATH=./${{ inputs.vulnerability_scanner_path }}
      CONFIG_PATH=${{ inputs.config_path }}

      if [ ! -f "${VULNERABILITY_SCANNER_PATH}" ]; then
        echo "Error: The file '${VULNERABILITY_SCANNER_PATH}' does not exist."
        exit 1
      fi

      if [ ! -f "${CONFIG_PATH}" ]; then
        echo "Error: The file '${CONFIG_PATH}' does not exist."
        exit 1
      fi

      echo "Running '${TEST_TOOL_PATH}'..."
      ${VULNERABILITY_SCANNER_PATH} -c ${CONFIG_PATH} -d
    shell: bash

  - name: Compress Vulnerability Scanner Content
    run: |
      rm -rf queue/indexer
      rm -rf queue/sockets
      rm -rf queue/router
      rm -rf queue/vd_updater/tmp
      rm -rf queue/vd/reports
      rm -rf queue/vd/sync
      rm -rf queue/vd/deltas
      rm -rf queue/vd/state_track
      rm -rf queue/keystore

      VD_FILENAME=vd_1.0.0_vd_${{ inputs.content_version }}.tar.xz

      echo "Compressing into '${VD_FILENAME}' ..."
      tar -cJf ${VD_FILENAME} --owner=0 --group=0 --no-same-owner --no-same-permissions queue

      if [ ! -f "${VD_FILENAME}" ]; then
        echo "Error: The file '${VD_FILENAME}' doesn't exist or could not be generated."
        exit 1
      else
          echo "File '${VD_FILENAME}' generated successfully."
          echo "Size of '${VD_FILENAME}': $(du -h "${VD_FILENAME}" | cut -f1)"
      fi
    shell: bash
