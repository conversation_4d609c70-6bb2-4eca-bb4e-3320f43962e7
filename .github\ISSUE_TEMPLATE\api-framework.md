---
name: API/Framework issue 
about: Report a bug or make a feature request.
title: ''
labels: 'team/framework'
assignees: ''

---

|Wazuh version|Component|
|---|---|
| X.Y.Z-rev | Wazuh component |

## Description
<!--
Whenever possible, issues should be created for bug reporting and feature requests.
For questions related to the user experience, please refer:
- <PERSON><PERSON><PERSON> mailing list: https://groups.google.com/forum/#!forum/wazuh
- <PERSON>in <PERSON>az<PERSON> on Slack: https://wazuh.com/community/join-us-on-slack
-->

## Checks
<!-- Do not modify, this will be ticked during development -->
The following elements have been updated or reviewed (should also be checked if no modification is required):
- [ ] Tests (unit tests, API integration tests).
- [ ] Changelog.
- [ ] Documentation.
- [ ] Integration test mapping (using `api/test/integration/mapping/_test_mapping.py`).
